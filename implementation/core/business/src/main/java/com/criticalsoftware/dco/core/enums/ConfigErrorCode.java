package com.criticalsoftware.dco.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.criticalsoftware.dco.core.common.exceptions.ErrorCode;

/**
 * Provides all enumerations that describe error codes related with configurations.
 */
@Getter
@AllArgsConstructor
public enum ConfigErrorCode implements ErrorCode {

    /**
     * Error code used for loading configuration errors.
     */
    LOAD_CONFIG_ERROR("core.load_config_error");

    private final String errorCode;

}
