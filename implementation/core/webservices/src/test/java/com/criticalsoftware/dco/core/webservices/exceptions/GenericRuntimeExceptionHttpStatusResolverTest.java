package com.criticalsoftware.dco.core.webservices.exceptions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.MockitoAnnotations.openMocks;

import java.util.UUID;

import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.criticalsoftware.dco.core.common.businessentities.ErrorData;
import com.criticalsoftware.dco.core.common.businessentities.ResultErrorData;
import com.criticalsoftware.dco.core.common.exceptions.AbstractRuntimeException;
import com.criticalsoftware.dco.core.common.utils.TransactionContext;
import com.criticalsoftware.dco.core.webservices.exceptions.enums.TestErrorCode;

class GenericRuntimeExceptionHttpStatusResolverTest {

    private String transactionId;

    private ExceptionMapper<AbstractRuntimeException> victim;

    private static MockedStatic<TransactionContext> transactionContextMockedStatic;

    private AutoCloseable autoCloseable;

    @BeforeEach
    void setUp() {
        autoCloseable = openMocks(this);

        final UUID uuid = UUID.randomUUID();
        transactionId = uuid.toString();

        transactionContextMockedStatic = mockStatic(TransactionContext.class);
        transactionContextMockedStatic.when(TransactionContext::getTransactionId).thenReturn(uuid);

        victim = new GenericRuntimeExceptionHttpStatusResolver();
    }

    @AfterEach
    void close() throws Exception {
        autoCloseable.close();
        transactionContextMockedStatic.close();
    }

    @Test
    void exceptionMappedSuccess() {
        final TestErrorCode testErrorCode = TestErrorCode.TEST_CODE;

        try (final Response actualResponse = victim.toResponse(new AbstractRuntimeException(testErrorCode, "Error {0}", "message") {
        })) {
            assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), actualResponse.getStatus());
            assertInstanceOf(ResultErrorData.class, actualResponse.getEntity());

            final ResultErrorData actualResultErrorData = (ResultErrorData) actualResponse.getEntity();

            assertEquals(1, actualResultErrorData.getErrors().size());

            final ErrorData actualErrorData = actualResultErrorData.getErrors().get(0);

            assertEquals("", actualErrorData.getCode(), "error code");
            assertEquals("Internal Server Error.", actualErrorData.getMessage(), "message");
            assertEquals(transactionId, actualErrorData.getTransactionId());
        }
    }

}
