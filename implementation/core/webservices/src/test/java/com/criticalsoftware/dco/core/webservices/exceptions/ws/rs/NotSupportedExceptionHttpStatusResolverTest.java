package com.criticalsoftware.dco.core.webservices.exceptions.ws.rs;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mockStatic;

import java.util.UUID;

import jakarta.ws.rs.NotSupportedException;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;

import com.criticalsoftware.dco.core.common.utils.TransactionContext;

class NotSupportedExceptionHttpStatusResolverTest {

    private ExceptionMapper<NotSupportedException> victim;

    private static MockedStatic<TransactionContext> transactionContextMockedStatic;

    @BeforeEach
    void setUp() {
        victim = new NotSupportedExceptionHttpStatusResolver();

        final UUID uuid = UUID.randomUUID();
        transactionContextMockedStatic = mockStatic(TransactionContext.class);
        transactionContextMockedStatic.when(TransactionContext::getTransactionId).thenReturn(uuid);
    }

    @AfterEach
    void close() {
        transactionContextMockedStatic.close();
    }

    @Test
    void exceptionMappedSuccess() {
        final String message = "Could not find resource for full path: http://dco/dco/requestmanager/asdasdserviceRequests";
        try (final Response actualResponse = victim.toResponse(new NotSupportedException(message, new RuntimeException()))) {
            assertEquals(actualResponse.getStatus(), Response.Status.BAD_REQUEST.getStatusCode());
        }
    }

}
