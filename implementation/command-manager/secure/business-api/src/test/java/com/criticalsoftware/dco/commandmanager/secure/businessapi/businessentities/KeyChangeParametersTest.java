package com.criticalsoftware.dco.commandmanager.secure.businessapi.businessentities;

import static com.criticalsoftware.dco.core.common.test.utils.BeanValidationUtils.validateObject;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import java.util.stream.Stream;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class KeyChangeParametersTest {


    private static final String DEVICE_MODEL = "106C433100AC124074DA";
    private static final String METER_SERIAL_NUMBER = "DEADBEEF";
    private static final String DCO_PUBLIC_KEY = "wQFBAAEAgICABP8H5wsJBAEDNQ0AAAA=";
    private static final String DEVICE_SIGNED_PUBLIC_KEY = "38BN9bAQyz/BV25c0flzBA==";

    static Stream<Arguments> methodSource() {
        return Stream.of(
            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 0),

            arguments(new KeyChangeParameters("", METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 1),
            arguments(new KeyChangeParameters(null, METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 1),

            arguments(new KeyChangeParameters(DEVICE_MODEL, "", DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 1),
            arguments(new KeyChangeParameters(DEVICE_MODEL, null, DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 1),

            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, "", DEVICE_SIGNED_PUBLIC_KEY), 1),
            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, null, DEVICE_SIGNED_PUBLIC_KEY), 1),

            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, ""), 1),
            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, null), 1),


            arguments(new KeyChangeParameters("", "", DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 2),
            arguments(new KeyChangeParameters(null, null, DCO_PUBLIC_KEY, DEVICE_SIGNED_PUBLIC_KEY), 2),

            arguments(new KeyChangeParameters(DEVICE_MODEL, "", "", DEVICE_SIGNED_PUBLIC_KEY), 2),
            arguments(new KeyChangeParameters(DEVICE_MODEL, null, null, DEVICE_SIGNED_PUBLIC_KEY), 2),

            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, "", ""), 2),
            arguments(new KeyChangeParameters(DEVICE_MODEL, METER_SERIAL_NUMBER, null, null), 2),

            arguments(new KeyChangeParameters("", METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, ""), 2),
            arguments(new KeyChangeParameters(null, METER_SERIAL_NUMBER, DCO_PUBLIC_KEY, null), 2),


            arguments(new KeyChangeParameters("", "", "", DEVICE_SIGNED_PUBLIC_KEY), 3),
            arguments(new KeyChangeParameters(null, null, null, DEVICE_SIGNED_PUBLIC_KEY), 3),

            arguments(new KeyChangeParameters(DEVICE_MODEL, "", "", ""), 3),
            arguments(new KeyChangeParameters(DEVICE_MODEL, null, null, null), 3),

            arguments(new KeyChangeParameters("", "", DCO_PUBLIC_KEY, ""), 3),
            arguments(new KeyChangeParameters(null, null, DCO_PUBLIC_KEY, null), 3),

            arguments(new KeyChangeParameters("", METER_SERIAL_NUMBER, "", ""), 3),
            arguments(new KeyChangeParameters(null, METER_SERIAL_NUMBER, null, null), 3),


            arguments(new KeyChangeParameters("", "", "", ""), 4),
            arguments(new KeyChangeParameters(null, null, null, null), 4),

            //Validate device model
            arguments(new KeyChangeParameters("qwertyuiopçlkjhgfdsa1234567890m", METER_SERIAL_NUMBER, DCO_PUBLIC_KEY,
                DEVICE_SIGNED_PUBLIC_KEY), 1)

        );
    }

    @MethodSource("methodSource")
    @ParameterizedTest
    void shouldDetectInvalidObject(final Object object, final int expectedErrors) {
        assertEquals(validateObject(object).size(), expectedErrors);
    }

}