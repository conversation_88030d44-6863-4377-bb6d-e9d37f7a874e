package com.criticalsoftware.dco.commandmanager.secure.webservices.cache;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;

import com.criticalsoftware.dco.commandmanager.secure.businessapi.builders.SecureKeyDetails;
import com.criticalsoftware.dco.commandmanager.secure.businessapi.businessentities.SecureKeyDetailsParameters;
import com.criticalsoftware.dco.commandmanager.secure.businessapi.cache.SecureKeyDetailsService;
import com.criticalsoftware.dco.commandmanager.secure.businessapi.exceptions.SecureKeyDetailsCacheNotFoundException;
import com.criticalsoftware.dco.commandmanager.secure.businessapi.exceptions.SecureKeyDetailsCacheUnauthorizedException;
import com.criticalsoftware.dco.commandmanager.secure.webservices.cache.interceptors.CanAddToCache;
import com.criticalsoftware.dco.commandmanager.secure.webservices.cache.interceptors.RestCacheable;
import com.criticalsoftware.dco.core.common.validation.constraints.NotEmpty;

/**
 * SecureKeyDetailsCacheResource.
 */
@ApplicationScoped
@RestCacheable
@Slf4j
public class SecureKeyDetailsResource implements SecureKeyDetailsApi {

    
    @Inject
    SecureKeyDetailsService secureKeyDetailsService;

    @Override
    public Response deleteSecureKeyDetails(@NotEmpty final String serialNumber)
        throws SecureKeyDetailsCacheNotFoundException,
        SecureKeyDetailsCacheUnauthorizedException { //SecureKeyDetailsCacheUnauthorizedException thrown by RestCacheable interceptor
        log.info("operation=deleteSecureKeyDetails, serialNumber={}", serialNumber);
        secureKeyDetailsService.invalidate(serialNumber);
        log.info("operation=deleteSecureKeyDetails, message='Secure Key Details deleted.'");
        return Response.ok().entity("\"" + serialNumber + "\"").build();
    }

    @CanAddToCache
    @Override
    public Response addSecureKeyDetails(@NotEmpty final String serialNumber,
                                        @Valid @NotNull final SecureKeyDetailsParameters secureKeyDetailsParameters) throws
        SecureKeyDetailsCacheUnauthorizedException { //SecureKeyDetailsCacheUnauthorizedException thrown by interceptors
        log.info("operation=addSecureKeyDetails, serialNumber={}", serialNumber);
        secureKeyDetailsService.addSecureKeyDetails(serialNumber, secureKeyDetailsParameters);
        log.info("operation=addSecureKeyDetails, message='Secure Key Details persisted.'");
        return Response.ok().build();
    }

    @Override
    public Response deleteAllSecureKeyDetails()
        throws SecureKeyDetailsCacheUnauthorizedException { //SecureKeyDetailsCacheUnauthorizedException thrown by RestCacheable interceptor
        log.info("operation=deleteAllSecureKeyDetails");
        secureKeyDetailsService.invalidateAll();
        log.info("operation=deleteAllSecureKeyDetails, message='Secure Key Details cache cleaned'");
        return Response.ok().entity("\"" + "All secure key details elements deleted from cache successfully." + "\"").build();

    }

    @Override
    public Response getSecureKeyDetails(@NotEmpty final String serialNumber)
        throws SecureKeyDetailsCacheNotFoundException,
        SecureKeyDetailsCacheUnauthorizedException { //SecureKeyDetailsCacheUnauthorizedException thrown by RestCacheable interceptor
        log.info("operation=getSecureKeyDetails, serialNumber={}", serialNumber);
        final SecureKeyDetails secureKeyDetails = secureKeyDetailsService.getSecureKeyDetails(serialNumber);
        log.info("operation=getSecureKeyDetails, message='Secure Key Details loaded.'");
        return Response.ok().entity(secureKeyDetails).build();
    }

}
