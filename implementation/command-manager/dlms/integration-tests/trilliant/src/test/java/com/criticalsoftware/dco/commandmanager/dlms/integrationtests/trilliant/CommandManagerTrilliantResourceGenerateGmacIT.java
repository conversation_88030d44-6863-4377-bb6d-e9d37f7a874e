package com.criticalsoftware.dco.commandmanager.dlms.integrationtests.trilliant;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import java.util.Map;
import java.util.stream.Stream;

import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.response.Response;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@QuarkusTest
@QuarkusTestResource(value = CryptoWiremockResource.class)
class CommandManagerTrilliantResourceGenerateGmacIT {

    private static final String CLEAR_TEXT_AUTHENTICATION_CHALLENGE = "//8RIjNEVWZ3iJmqu8zd7g==";
    private static final String INITIALIZATION_VECTOR = "TU1NAAC8YU4BI0Vn";
    private static final String METER_ID = "00-00-00-00-00-00-02-0A";

    @BeforeEach
    void startCryptoWiremockServer() {
        CryptoWiremockResource.reset();
    }

    static Stream<Arguments> requestTypeMethodSource() {
        return Stream.of(
            arguments(CommandManagerTrilliantUtils.SESSION, "generateGmac"),
            arguments(CommandManagerTrilliantUtils.NOSRV_SESSION, "nosrv/generateGmac")
        );
    }

    @MethodSource("requestTypeMethodSource")
    @ParameterizedTest
    void sendGenerateGmacSuccess(final String sessionPath, final String generateGmacPath) {
        final String expectedGmacAuthenticationChallenge = "ba2yJeuvnItAh8fT";
        final JSONObject cryptoResponseBody = new JSONObject().put("gmacAuthenticationChallenge", expectedGmacAuthenticationChallenge);
        CommandManagerTrilliantUtils.mockCryptoCall("/dco/crypto/trilliant/generateGmac", cryptoResponseBody);

        final String sessionId = CommandManagerTrilliantUtils.openSession(CommandManagerTrilliantUtils.REQUEST_ID,
            CommandManagerTrilliantUtils.DEVICE_MODEL, sessionPath);
        final String subsessionId = CommandManagerTrilliantUtils.openSubsession(METER_ID, CommandManagerTrilliantUtils.CLIENT, sessionId,
            sessionPath);

        final Map<String, String> generateGmacHeaders = CommandManagerTrilliantUtils.buildRequestHeader(sessionId, subsessionId);
        final JSONObject authenticateBody = new JSONObject()
            .put("encryptionKey", CommandManagerTrilliantUtils.ENCRYPTION_KEY)
            .put("authenticationKey", CommandManagerTrilliantUtils.AUTHENTICATION_KEY)
            .put("cleartextAuthenticationChallenge", CLEAR_TEXT_AUTHENTICATION_CHALLENGE)
            .put("initialisationVector", INITIALIZATION_VECTOR);

        final Response authenticateResponse = CommandManagerTrilliantUtils.sendPostRequest(authenticateBody, generateGmacHeaders,
            CommandManagerTrilliantUtils.requestUrl(generateGmacPath));

        assertEquals(expectedGmacAuthenticationChallenge,
            CommandManagerTrilliantUtils.getMessageFromResponse(authenticateResponse, "gmacAuthenticationChallenge"));
        assertEquals(CommandManagerTrilliantUtils.SUCCESS, authenticateResponse.statusCode());
    }

}
