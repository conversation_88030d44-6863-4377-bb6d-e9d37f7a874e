import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.quarkus.test.common.QuarkusTestResource;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.response.Response;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

@QuarkusTest
@QuarkusTestResource(value = CryptoWiremockResource.class)
class CommandManagerIEResourceJoinPpmidIT {

    private static final String KEY_PACKAGES_ARRAY_MK_AK_GUEK =
        """
            ["PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnM\
            yPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjAtMDUtMT\
            JUMTI6MTg6MjguMjYyWiIgaWQ9ImQ2Y2QwYTBmLTVlZWEtNDc3My04N2Y0LWFjMTEzZmQ1MTkyYSIgc\
            2NoZW1hVmVyc2lvbj0iMS4wIj4KICAgIDxEZXZpY2VJZHM+CiAgICAgICAgPERldmljZUlkPjAwLTAwLTAwL\
            TAwLTAwLTAwLTAxLTBBPC9EZXZpY2VJZD4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMD\
            AtMDAtMDEtMEI8L0RldmljZUlkPgogICAgICAgIDxEZXZpY2VJZD4wMC0wMC0wMC0wMC0wMC0wMC0w\
            MS0wQzwvRGV2aWNlSWQ+CiAgICA8L0RldmljZUlkcz4KICAgIDxLZXlUeXBlPk1LPC9LZXlUeXBlPgogICAgP\
            EVuY3J5cHRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5BRVM8L01lY2hhbmlzbT4KICAgICAgICA8RW5jcnlwdGl\
            vbktleUlkPjE8L0VuY3J5cHRpb25LZXlJZD4KICAgICAgICA8S2V5Pm9kUjhkcFlQUnY0OUc1ZTJhSVJ5T3c9PT\
            wvS2V5PgogICAgPC9FbmNyeXB0aW9uPgogICAgPEF1dGhlbnRpY2F0aW9uPgogICAgICAgIDxNZWNoYW5\
            pc20+RUNDPC9NZWNoYW5pc20+CiAgICAgICAgPEF1dGhlbnRpY2F0aW9uS2V5SWQ+MzwvQXV0aGVudGl\
            jYXRpb25LZXlJZD4KICAgIDwvQXV0aGVudGljYXRpb24+CjxTaWduYXR1cmUgeG1sbnM9Imh0dHA6Ly93d3c\
            udzMub3JnLzIwMDAvMDkveG1sZHNpZyMiPjxTaWduZWRJbmZvPjxDYW5vbmljYWxpemF0aW9uTWV0aG\
            9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8xMC94bWwtZXhjLWMxNG4jIi8+PFNpZ25hdH\
            VyZU1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMDQveG1sZHNpZy1tb3JlI2VjZH\
            NhLXNoYTI1NiIvPjxSZWZlcmVuY2UgVVJJPSIiPjxUcmFuc2Zvcm1zPjxUcmFuc2Zvcm0gQWxnb3JpdGhtPSJ\
            odHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjZW52ZWxvcGVkLXNpZ25hdHVyZSIvPjwvVHJh\
            bnNmb3Jtcz48RGlnZXN0TWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8wNC94bW\
            xlbmMjc2hhMjU2Ii8+PERpZ2VzdFZhbHVlPmt5dmtMS25NZjk0M1BhZWZyektRRFNZaWFqLzNDVHJxMU1\
            GYmlBVldXTFU9PC9EaWdlc3RWYWx1ZT48L1JlZmVyZW5jZT48L1NpZ25lZEluZm8+PFNpZ25hdHVyZVZhb\
            HVlPjVqQ2VNZ3lzeGpadXhGbE1Ia0hjNXkwZjNZUEp1ZTF3UXliWW1GbEdqaXZvRXpacUQ1eExLS0dLLzJse\
            lovd3hoZmNxT3BTVjJhZkcKa3JMdW1yaklKdz09PC9TaWduYXR1cmVWYWx1ZT48L1NpZ25hdHVyZT48L0\
            RMTVNLZXlQYWNrYWdlPg==","PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0\
            tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdG\
            lvbkRhdGU9IjIwMjAtMDUtMTJUMTI6MjA6MzUuNzg4WiIgaWQ9IjE4ZGVjOWRiLTkzYzktNGU0Ny1hZGVjLT\
            QwODZjZmQzZmRhMSIgc2NoZW1hVmVyc2lvbj0iMS4wIj4KICAgIDxEZXZpY2VJZHM+CiAgICAgICAgPERld\
            mljZUlkPjAwLTAwLTAwLTAwLTAwLTAwLTAxLTBiPC9EZXZpY2VJZD4KICAgICAgICA8RGV2aWNlSWQ+MD\
            AtMDAtMDAtMDAtMDAtMDAtMDEtMGM8L0RldmljZUlkPgogICAgICAgIDxEZXZpY2VJZD4wMC0wMC0w\
            MC0wMC0wMC0wMC0wMS0wYTwvRGV2aWNlSWQ+CiAgICA8L0RldmljZUlkcz4KICAgIDxLZXlUeXBlPkFLP\
            C9LZXlUeXBlPgogICAgPERMTVNDbGllbnQ+MHgyMDwvRExNU0NsaWVudD4KICAgIDxFbmNyeXB0aW9u\
            PgogICAgICAgIDxNZWNoYW5pc20+QUVTPC9NZWNoYW5pc20+CiAgICAgICAgPEVuY3J5cHRpb25LZXlJZ\
            D4xPC9FbmNyeXB0aW9uS2V5SWQ+CiAgICAgICAgPEtleT5IZ1dDRGsvRlJSdnVtclNtWlk5c3ZRPT08L0tleT4\
            KICAgIDwvRW5jcnlwdGlvbj4KICAgIDxBdXRoZW50aWNhdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkVDQzw\
            vTWVjaGFuaXNtPgogICAgICAgIDxBdXRoZW50aWNhdGlvbktleUlkPjM8L0F1dGhlbnRpY2F0aW9uS2V5SWQ\
            +CiAgICA8L0F1dGhlbnRpY2F0aW9uPgo8U2lnbmF0dXJlIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDA\
            wLzA5L3htbGRzaWcjIj48U2lnbmVkSW5mbz48Q2Fub25pY2FsaXphdGlvbk1ldGhvZCBBbGdvcml0aG09Imh0\
            dHA6Ly93d3cudzMub3JnLzIwMDEvMTAveG1sLWV4Yy1jMTRuIyIvPjxTaWduYXR1cmVNZXRob2QgQWxnb\
            3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNlY2RzYS1zaGEyNTYiLz48U\
            mVmZXJlbmNlIFVSST0iIj48VHJhbnNmb3Jtcz48VHJhbnNmb3JtIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My\
            5vcmcvMjAwMC8wOS94bWxkc2lnI2VudmVsb3BlZC1zaWduYXR1cmUiLz48L1RyYW5zZm9ybXM+PERpZ2V\
            zdE1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMDQveG1sZW5jI3NoYTI1NiIvPjxE\
            aWdlc3RWYWx1ZT5Gc2pCc0RaaXorSFp3K1lLenFVSDBxcEZWdzBRNkJ2eDNieWFvbVlEWWg4PTwvRGlnZ\
            XN0VmFsdWU+PC9SZWZlcmVuY2U+PC9TaWduZWRJbmZvPjxTaWduYXR1cmVWYWx1ZT4vZFpNS3MyY3\
            diQmc3MUJYZWIwYVZDRlhTOVlYWGYwR1pPSkpwVXZROGQyMWQ1OEVJdkxVaXVzWHNBQUliOW92N\
            Vo0QmVFZFJtRlEyCitPa0J1Y09ZdHc9PTwvU2lnbmF0dXJlVmFsdWU+PC9TaWduYXR1cmU+PC9ETE1TS2V\
            5UGFja2FnZT4=","PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2\
            UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU\
            9IjIwMjAtMDUtMTJUMTI6MjM6MDkuOTY3WiIgaWQ9ImE3ZmVkNmUxLWVhNDEtNDllMy05YWU4LWQ2\
            YTk0YjM1NjNkNSIgc2NoZW1hVmVyc2lvbj0iMS4wIj4KICAgIDxEZXZpY2VJZHM+CiAgICAgICAgPERldmljZ\
            UlkPjAwLTAwLTAwLTAwLTAwLTAwLTAxLTBiPC9EZXZpY2VJZD4KICAgICAgICA8RGV2aWNlSWQ+MDAtM\
            DAtMDAtMDAtMDAtMDAtMDEtMEI8L0RldmljZUlkPgogICAgICAgIDxEZXZpY2VJZD4wMC0wMC0wMC0\
            wMC0wMC0wMC0wMS0wYzwvRGV2aWNlSWQ+CiAgICAgICAgPERldmljZUlkPjAwLTAwLTAwLTAwLTAwL\
            TAwLTAxLTBBPC9EZXZpY2VJZD4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMDAtMDAtMD\
            EtMEM8L0RldmljZUlkPgogICAgICAgIDxEZXZpY2VJZD4wMC0wMC0wMC0wMC0wMC0wMC0wMS0wYTw\
            vRGV2aWNlSWQ+CiAgICA8L0RldmljZUlkcz4KICAgIDxLZXlUeXBlPkdVRUs8L0tleVR5cGU+CiAgICA8RExNU0\
            NsaWVudD4weDIwPC9ETE1TQ2xpZW50PgogICAgPEVuY3J5cHRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5BR\
            VM8L01lY2hhbmlzbT4KICAgICAgICA8RW5jcnlwdGlvbktleUlkPjE8L0VuY3J5cHRpb25LZXlJZD4KICAgICAgIC\
            A8S2V5Pk55RVNUOHl1cGFwV1lBeUNkZ3Bjb1E9PTwvS2V5PgogICAgPC9FbmNyeXB0aW9uPgogICAgPEF1\
            dGhlbnRpY2F0aW9uPgogICAgICAgIDxNZWNoYW5pc20+RUNDPC9NZWNoYW5pc20+CiAgICAgICAgPEF1dG\
            hlbnRpY2F0aW9uS2V5SWQ+MzwvQXV0aGVudGljYXRpb25LZXlJZD4KICAgIDwvQXV0aGVudGljYXRpb24+C\
            jxTaWduYXR1cmUgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyMiPjxTaWduZW\
            RJbmZvPjxDYW5vbmljYWxpemF0aW9uTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwM\
            S8xMC94bWwtZXhjLWMxNG4jIi8+PFNpZ25hdHVyZU1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMu\
            b3JnLzIwMDEvMDQveG1sZHNpZy1tb3JlI2VjZHNhLXNoYTI1NiIvPjxSZWZlcmVuY2UgVVJJPSIiPjxUcmFuc2\
            Zvcm1zPjxUcmFuc2Zvcm0gQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjZ\
            W52ZWxvcGVkLXNpZ25hdHVyZSIvPjwvVHJhbnNmb3Jtcz48RGlnZXN0TWV0aG9kIEFsZ29yaXRobT0iaHR0c\
            DovL3d3dy53My5vcmcvMjAwMS8wNC94bWxlbmMjc2hhMjU2Ii8+PERpZ2VzdFZhbHVlPnErOUYwcUt6S211\
            Lyt6UEhFN0dyTUIxMXVzY1Q2aUZLbVRqQ2lQcng4MzQ9PC9EaWdlc3RWYWx1ZT48L1JlZmVyZW5jZT48L1N\
            pZ25lZEluZm8+PFNpZ25hdHVyZVZhbHVlPmNONExyVlYrdHROOXM0TDVhWUQxQXkvMVBMSmhvMXFXd\
            WhmL2h3R1VIVmhKbW1WYmU1SjcyTEVVOTdmYUlrSG1lUS84cXJ6bGs2aHIKR2F5cDdTc29XZz09PC9TaWdu\
            YXR1cmVWYWx1ZT48L1NpZ25hdHVyZT48L0RMTVNLZXlQYWNrYWdlPg=="]""";
    private static final String REQUEST_ID_FOR_SRV_8_7_1 = "90-B3-D5-1F-30-01-00-00:00-00-00-00-00-00-01-0C:1003";
    private static final List<String> EXPECTED_UPDATED_KEYS = Collections.singletonList("expectedUpdatedKeys");

    @BeforeEach
    void startCryptoWiremockServer() {
        CryptoWiremockResource.reset();
    }

    @Test
    void sendJoinPpmidSuccess() {
        final JSONObject cryptoResponseBody = new JSONObject().put("updatedKeys", EXPECTED_UPDATED_KEYS);
        CryptoWiremockResource.mockCryptoCall("/dco/crypto/ie/addDeviceIdToInstallation", cryptoResponseBody);

        final String sessionId = CommandManagerIEUtils.openSession(REQUEST_ID_FOR_SRV_8_7_1, CommandManagerIEUtils.SESSIONS);

        final Map<String, String> joinPpmidHeaders = new HashMap<>();
        joinPpmidHeaders.put("sessionId", sessionId);
        final JSONArray keyPackagesArray = new JSONArray(KEY_PACKAGES_ARRAY_MK_AK_GUEK);
        final JSONObject joinPpmidBody = new JSONObject().put("packagedKeys", keyPackagesArray);

        final Response joinPpmidResponse = CommandManagerIEUtils.sendPostRequest(joinPpmidBody, joinPpmidHeaders,
            CommandManagerIEUtils.requestUrl("joinppmid"));

        assertEquals(EXPECTED_UPDATED_KEYS.toString(), CommandManagerIEUtils.getMessageFromResponse(joinPpmidResponse, "updatedKeys"));
        assertEquals(CommandManagerIEUtils.SUCCESS, joinPpmidResponse.statusCode());
    }

}
