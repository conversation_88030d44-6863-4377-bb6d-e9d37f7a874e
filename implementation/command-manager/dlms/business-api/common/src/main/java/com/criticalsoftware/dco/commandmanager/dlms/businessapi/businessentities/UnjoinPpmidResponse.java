package com.criticalsoftware.dco.commandmanager.dlms.businessapi.businessentities;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

import org.eclipse.microprofile.openapi.annotations.media.Schema;

import com.criticalsoftware.dco.core.common.LogInfoConstant;
import com.criticalsoftware.dco.core.common.LoggerVerbose;

/**
 * Specifies the remove PPMID response.
 */
public class UnjoinPpmidResponse implements Serializable, LoggerVerbose {

    @Schema(description = "Updated keys",
            example =
                """
                    ['PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL\
                    zA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjAtMDQtMTRUMTE6MDc6MjMuMjU5KzAxOjAwIiBpZD0iNGIxZjViZGItNTkwYS00ZDlhLWFjODgtYzk1\
                    NjgzMmRkMjM2IiBzY2hlbWFWZXJzaW9uPSIxLjAiPgogICAgPERldmljZUlkcz4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMDAtMDAtMDEtMEE\
                    8L0RldmljZUlkPgogICAgICAgIDxEZXZpY2VJZD4wMC0wMC0wMC0wMC0wMC0wMC0wMS0wQjwvRGV2aWNlSWQ+CiAgICAgICAgPERldmljZUlkPjAwLTAwLT\
                    AwLTAwLTAwLTAwLTAxLTBDPC9EZXZpY2VJZD4KICAgIDwvRGV2aWNlSWRzPgogICAgPEtleVR5cGU+R1VFSzwvS2V5VHlwZT4KICAgIDxETE1TQ2xpZW50P\
                    jB4MjA8L0RMTVNDbGllbnQ+CiAgICA8RW5jcnlwdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkFFUzwvTWVjaGFuaXNtPgogICAgICAgIDxFbmNyeXB0aW9u\
                    S2V5SWQ+MjwvRW5jcnlwdGlvbktleUlkPgogICAgICAgIDxLZXk+TnlFU1Q4eXVwYXBXWUF5Q2RncGNvUT09PC9LZXk+CiAgICA8L0VuY3J5cHRpb24+CiA\
                    gICA8QXV0aGVudGljYXRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5FQ0M8L01lY2hhbmlzbT4KICAgICAgICA8QXV0aGVudGljYXRpb25LZXlJZD4zPC9BdX\
                    RoZW50aWNhdGlvbktleUlkPgogICAgPC9BdXRoZW50aWNhdGlvbj4KPFNpZ25hdHVyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc\
                    2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0\
                    biMiLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8wNC94bWxkc2lnLW1vcmUjZWNkc2Etc2hhMjU2Ii8+PFJ\
                    lZmVyZW5jZSBVUkk9IiI+PFRyYW5zZm9ybXM+PFRyYW5zZm9ybSBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNlbnZlbG\
                    9wZWQtc2lnbmF0dXJlIi8+PC9UcmFuc2Zvcm1zPjxEaWdlc3RNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGVuYyNza\
                    GEyNTYiLz48RGlnZXN0VmFsdWU+ZzJCWkFQNmlKYkxPWHdlM3B5WlI2aDlBc3RVUlhYMVAxRTdJckJkMUsxbz08L0RpZ2VzdFZhbHVlPjwvUmVmZXJlbmNl\
                    PjwvU2lnbmVkSW5mbz48U2lnbmF0dXJlVmFsdWU+MThyMmFQWmMwb0N6NDVBdVBrZ0lKOGpUQVpVR1F0NVcxQ2NtWGg0MFd0WDB5TmFTVWFnT0hVbHphakp\
                    kWWpaTjlGWEZ6aHlGWC9EbwptdTIralkwZXpnPT08L1NpZ25hdHVyZVZhbHVlPjwvU2lnbmF0dXJlPjwvRExNU0tleVBhY2thZ2U+' ,'PD94bWwgdmVyc2\
                    lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjI\
                    iBjcmVhdGlvbkRhdGU9IjIwMjAtMDQtMTRUMTE6MDk6MTUuNjkyKzAxOjAwIiBpZD0iMmQwZjgyNjEtY2QxMC00NzRjLTkwMGMtZmJmMTk1OTFlYjY0IiBz\
                    Y2hlbWFWZXJzaW9uPSIxLjAiPgogICAgPERldmljZUlkcz4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMDAtMDAtMDEtMEI8L0RldmljZUlkPgo\
                    gICAgICAgIDxEZXZpY2VJZD4wMC0wMC0wMC0wMC0wMC0wMC0wMS0wQzwvRGV2aWNlSWQ+CiAgICAgICAgPERldmljZUlkPjAwLTAwLTAwLTAwLTAwLTAwLT\
                    AxLTBBPC9EZXZpY2VJZD4KICAgIDwvRGV2aWNlSWRzPgogICAgPEtleVR5cGU+TUs8L0tleVR5cGU+CiAgICA8RW5jcnlwdGlvbj4KICAgICAgICA8TWVja\
                    GFuaXNtPkFFUzwvTWVjaGFuaXNtPgogICAgICAgIDxFbmNyeXB0aW9uS2V5SWQ+MTwvRW5jcnlwdGlvbktleUlkPgogICAgICAgIDxLZXk+M0h0VmxubU5R\
                    ZHQ2bUF3b25DM3BOUT09PC9LZXk+CiAgICA8L0VuY3J5cHRpb24+CiAgICA8QXV0aGVudGljYXRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5FQ0M8L01lY2h\
                    hbmlzbT4KICAgICAgICA8QXV0aGVudGljYXRpb25LZXlJZD4zPC9BdXRoZW50aWNhdGlvbktleUlkPgogICAgPC9BdXRoZW50aWNhdGlvbj4KPFNpZ25hdH\
                    VyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3Jpd\
                    GhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0biMiLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5v\
                    cmcvMjAwMS8wNC94bWxkc2lnLW1vcmUjZWNkc2Etc2hhMjU2Ii8+PFJlZmVyZW5jZSBVUkk9IiI+PFRyYW5zZm9ybXM+PFRyYW5zZm9ybSBBbGdvcml0aG0\
                    9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNlbnZlbG9wZWQtc2lnbmF0dXJlIi8+PC9UcmFuc2Zvcm1zPjxEaWdlc3RNZXRob2QgQWxnb3\
                    JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGVuYyNzaGEyNTYiLz48RGlnZXN0VmFsdWU+aHdCMm50V2FHZ29qMURKVE9SeksxMmdVNTdyS\
                    nRwc04wQW5oWVNlU0lNMD08L0RpZ2VzdFZhbHVlPjwvUmVmZXJlbmNlPjwvU2lnbmVkSW5mbz48U2lnbmF0dXJlVmFsdWU+UmIvZWt1TWVqL2RaSi9zYUpn\
                    VUQ3YmM2ZmN1SHI3QkdrNUFtWXZncmlVK1BtK25xZGRpbFdnSUs1NkNwRE9TOVhETktqWXV3TmN6VAplU0hVVm9mbldnPT08L1NpZ25hdHVyZVZhbHVlPjw\
                    vU2lnbmF0dXJlPjwvRExNU0tleVBhY2thZ2U+']""",
            required = true)
    private List<String> updatedKeys;

    /**
     * Constructs a new instance. Default constructor is needed during the from/to JSON mapping.
     */
    UnjoinPpmidResponse() {
    }

    /**
     * Constructs a new instance with all the required fields.
     *
     * @param updatedKeys a List of Base64 String containing an remove PPMID response.
     */
    public UnjoinPpmidResponse(final List<String> updatedKeys) {
        this.updatedKeys = updatedKeys;
    }

    public List<String> getUpdatedKeys() {
        return updatedKeys;
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        final UnjoinPpmidResponse that = (UnjoinPpmidResponse) o;
        return Objects.equals(updatedKeys, that.updatedKeys);
    }

    @Override
    public int hashCode() {
        return Objects.hash(updatedKeys);
    }

    @Override
    public String toString() {
        return "UnjoinPpmidResponse{"
               + "updatedKeys='" + LogInfoConstant.SENSITIVE_INFO + '\''
               + '}';
    }

    @Override
    public String toStringVerbose() {
        return "UnjoinPpmidResponse{"
               + "updatedKeys='" + updatedKeys + '\''
               + '}';
    }

}
