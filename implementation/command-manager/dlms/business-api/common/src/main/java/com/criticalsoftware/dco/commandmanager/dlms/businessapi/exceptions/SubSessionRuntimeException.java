package com.criticalsoftware.dco.commandmanager.dlms.businessapi.exceptions;

import com.criticalsoftware.dco.commandmanager.dlms.businessapi.business.common.enums.SubSessionErrorCode;
import com.criticalsoftware.dco.core.common.exceptions.AbstractRuntimeException;
import com.criticalsoftware.dco.core.common.exceptions.ErrorCode;

/**
 * Generic runtime exception for SubSessions.
 */
public final class SubSessionRuntimeException extends AbstractRuntimeException {

    private static final String NO_FIRMWARE_IMAGE_ASSOCIATED_WITH_HASH_MESSAGE = "Firmware image with the hash ''{0}'' not found on DB.";

    /**
     * Create a new instance of this exception.
     *
     * @param errorCode  resource error code
     * @param message    detailed message
     * @param parameters message parameters
     */
    private SubSessionRuntimeException(final ErrorCode errorCode, final String message, final Object... parameters) {
        super(errorCode, message, parameters);
    }

    /**
     * Creates a new instance of this exception with {@link SubSessionErrorCode#NO_FIRMWARE_IMAGE_ASSOCIATED_WITH_HASH_ERROR} error code.
     *
     * @param hash hash of the firmware
     *
     * @return instance of {@link SubSessionRuntimeException}
     */
    public static SubSessionRuntimeException newNoFirmwareImageAssociatedWithHash(final String hash) {
        return new SubSessionRuntimeException(SubSessionErrorCode.NO_FIRMWARE_IMAGE_ASSOCIATED_WITH_HASH_ERROR,
            NO_FIRMWARE_IMAGE_ASSOCIATED_WITH_HASH_MESSAGE, hash);
    }

}
