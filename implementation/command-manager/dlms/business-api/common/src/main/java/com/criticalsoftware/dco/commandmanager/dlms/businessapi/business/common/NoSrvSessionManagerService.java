package com.criticalsoftware.dco.commandmanager.dlms.businessapi.business.common;

import com.criticalsoftware.dco.commandmanager.dlms.businessapi.businessentities.SessionResponse;
import com.criticalsoftware.dco.commandmanager.dlms.businessapi.businessentities.SubSessionResponse;
import com.criticalsoftware.dco.commandmanager.dlms.businessapi.exceptions.SessionNotFoundException;
import com.criticalsoftware.dco.commandmanager.dlms.businessapi.exceptions.SubSessionException;
import com.criticalsoftware.dco.core.common.validation.constraints.NotEmpty;
import com.criticalsoftware.dco.dcodata.entities.command.NoSrvSession;
import com.criticalsoftware.dco.dcodata.entities.command.NoSrvSubSession;

/**
 * Interface that specifies the non service request (NoSrv) Session operations.
 */
public interface NoSrvSessionManagerService {

    /**
     * Service to create a session.
     *
     * @param requestId   the requestID
     * @param deviceModel the device model
     * @param s1sp        the s1sp
     *
     * @return SessionResponse with the id from the created session
     */
    SessionResponse createSession(@NotEmpty String requestId, @NotEmpty String deviceModel, @NotEmpty String s1sp);

    /**
     * Service to close a session.
     *
     * @param sessionId id from the session
     * @param s1sp      the s1sp
     *
     * @throws SessionNotFoundException if the session doesn't exist
     */
    void closeSession(String sessionId, String s1sp) throws SessionNotFoundException;

    /**
     * Retrieves a session from the cache.
     *
     * @param sessionId id from the session
     * @param s1sp      the s1sp
     *
     * @return NoSrvSession object with no srv session information
     *
     * @throws SessionNotFoundException if session doesn't exist
     */
    NoSrvSession getSession(String sessionId, String s1sp) throws SessionNotFoundException;

    /**
     * Service to create a SubSession.
     *
     * @param sessionId id of the session
     * @param meterId   id of the meter
     * @param client    the logical device (management logical device)
     * @param s1sp      the s1sp
     *
     * @return The SubSessionResponse with the id from the created SubSession
     *
     * @throws SessionNotFoundException if the session doesn't exist
     */
    SubSessionResponse createSubSession(String sessionId, @NotEmpty String meterId, @NotEmpty String client, @NotEmpty String s1sp)
        throws SessionNotFoundException;

    /**
     * Service to close a SubSession.
     *
     * @param sessionId    id of the session
     * @param subSessionId id of the SubSession
     * @param s1sp         the s1sp
     *
     * @throws SubSessionException if the SubSession is invalid
     */
    void closeSubSession(String sessionId, String subSessionId, String s1sp)
        throws SubSessionException;

    /**
     * Retrieves a SubSession data from the cache. This will also check if the SubSession is valid.
     *
     * @param sessionId    id from the session
     * @param subSessionId id from the SubSession
     * @param s1sp         the s1sp
     *
     * @return The NoSrvSubSession object with the no srv SubSession information
     *
     * @throws SubSessionException if the SubSession doesn't exist
     */
    NoSrvSubSession getSubSession(String sessionId, String subSessionId, String s1sp) throws SubSessionException;

}

