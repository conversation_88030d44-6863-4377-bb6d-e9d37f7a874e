<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.2.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.2.0 https://maven.apache.org/xsd/assembly-2.2.0.xsd">
  <id>rest-api-zip</id>
  <includeBaseDirectory>false</includeBaseDirectory>
  <formats>
    <format>zip</format>
  </formats>
  <files>
    <file>
      <source>${openapi.generator.ieDirectory}/${openapi.generator.generatedYamlFileName}.yaml</source>
      <outputDirectory>/</outputDirectory>
    </file>
    <file>
      <source>${openapi.generator.targetDirectory}/generatedAPI/command-manager-ie/${openapi.generator.outputFileName}.pdf</source>
      <outputDirectory>/</outputDirectory>
    </file>
    <file>
      <source>${openapi.generator.targetDirectory}/generatedAPI/dco-command-manager-ie-rest-api/index.html</source>
      <destName>${openapi.generator.outputFileName}.html</destName>
      <outputDirectory>/</outputDirectory>
    </file>
  </files>
</assembly>