dco:
  crypto:
    hsm:
      modules: 1
      kek:
        names: dco,dco-s1sp,smso,dco-dk,trilliant-dco-mk,trilliant-dco-guek,trilliant-enrolment-key,trilliant-dco-ak
      see:
        signer:
          hash: 329ee4620467f84a4aaf95d1eb373df378e4df6a
      estab:
        auth:
          hash: 30d85d0a62946715935ce73dacb70a09d1e306e3
      connection:
        chooser: ak
      # Recovery service
      recovery:
        strategy: disabled
        # Scheduled Recovery service
        scheduled:
          strategy: disabled
          enabled: false
          initial-delay: 0
          periodicity: 20
      # Validation service
      validation:
        strategy:
          ~: disabled
          default:
            command:
              ~: encrypt
              encrypt:
                kek: dco
        # Scheduled Validation service
        scheduled:
          strategy:
            ~: disabled
            default:
              command:
                ~: encrypt
                encrypt:
                  kek: dco
          enabled: false
          initial-delay: 0
          periodicity: 20
  keypackage:
    keystore-configuration-use-hsm: false
    hsmpublic-smso-file-path: src/test/resources/example_keystores/smso.pem
    local-keystore-configuration-file: src/test/resources/example_keystores/local-keystore-configuration.xml
    hsm-keystore-configuration-file: src/test/resources/example_keystores/hsm-keystore-configuration.xml
    keystore-configuration-schema-file: schemas/keystore-configuration-v1.xsd