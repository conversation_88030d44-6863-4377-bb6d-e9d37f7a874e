package com.criticalsoftware.dco.hsm.cli.commands;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.PublicKey;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.criticalsoftware.dco.commons.exceptions.DcoToolsException;
import com.criticalsoftware.dco.hsm.core.exceptions.ThalesException;
import com.criticalsoftware.hsm.client.HsmClient;

class GenerateRSAKeyCommandTest {

    @InjectMocks
    private GenerateRSAKeyCommand victim;
    @Mock
    private HsmClient cryptoAPI;
    private AutoCloseable autoCloseable;
    private static final String RSA_KEY_NAME = "dco-rsa";

    @BeforeEach
    public void testSetup() {
        autoCloseable = openMocks(this);
    }

    @AfterEach
    public void testTearDown() throws Exception {
        autoCloseable.close();
    }

    // ================================================================================================
    //  Valid tests
    // ================================================================================================

    @Test
    void generateRSAKeyValidData() throws Exception {
        Path path = Paths.get("./" + RSA_KEY_NAME + ".pem");
        when(cryptoAPI.generateRSATransport(anyString())).thenReturn(new PublicKeyMockImpl());
        victim.generateRSAKey(RSA_KEY_NAME);
        verify(cryptoAPI).generateRSATransport(anyString());
        Files.delete(path);
    }

    // ================================================================================================
    //  Fail tests
    // ================================================================================================

    @Test
    void generateRSAKeyValidDataException() throws Exception {
        when(cryptoAPI.generateRSATransport(anyString())).thenThrow(new ThalesException("message"));
        assertThrows(DcoToolsException.class, () -> victim.generateRSAKey(anyString()));
    }

    public static class PublicKeyMockImpl implements PublicKey {

        @Override
        public String getAlgorithm() {
            return "";
        }

        @Override
        public String getFormat() {
            return "";
        }

        @Override
        public byte[] getEncoded() {
            return new byte[10];
        }

    }

}