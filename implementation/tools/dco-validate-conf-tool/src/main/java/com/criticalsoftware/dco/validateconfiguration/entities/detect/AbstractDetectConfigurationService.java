package com.criticalsoftware.dco.validateconfiguration.entities.detect;

import java.util.List;
import java.util.Map;

import com.criticalsoftware.dco.validateconfiguration.exceptions.DetectInvalidConfigurationException;

/**
 * This Class Contains Similar codes to different types of meters(DLMS |Secure).
 *
 * @param <T> - Supported Parameter Definition (DLMS |Secure)
 */
public abstract class AbstractDetectConfigurationService<T> {

    /**
     * This method validate the required supported Params.
     *
     * @param handlerName                 - Handler name
     * @param ruleParameters              - Rule Parameters
     * @param requiredSupportedParameters - Required Supported Parameters
     *
     * @throws DetectInvalidConfigurationException - if configuration is invalid.
     */
    protected void validateRequiredSupportedParams(final String handlerName, final List<String> ruleParameters,
                                                   final List<String> requiredSupportedParameters)
        throws DetectInvalidConfigurationException {
        for (final String requiredSupportedParameter : requiredSupportedParameters) {
            if (!ruleParameters.contains(requiredSupportedParameter)) {
                throw DetectInvalidConfigurationException.newMissingRequiredParameterException(requiredSupportedParameter, handlerName);
            }
        }
    }

    /**
     * This method validate the supported Params.
     *
     * @param handlerName       - Handler name
     * @param handlerParameters - Handler Parameters
     * @param ruleParameters    - Rule Parameters
     *
     * @throws DetectInvalidConfigurationException - if configuration is invalid.
     */
    protected void validateRulesParameter(final String handlerName,
                                          final Map<String, T> handlerParameters,
                                          final List<String> ruleParameters) throws DetectInvalidConfigurationException {
        for (final String ruleParameter : ruleParameters) {
            if (!handlerParameters.containsKey(ruleParameter)) {
                throw DetectInvalidConfigurationException.newInvalidParameterConfigurationException(ruleParameter, handlerName);
            }
        }
    }

}
