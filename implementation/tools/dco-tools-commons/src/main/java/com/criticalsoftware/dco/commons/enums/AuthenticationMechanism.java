package com.criticalsoftware.dco.commons.enums;

/**
 * Specifies the authentication mechanism used during authentication.
 */
public enum AuthenticationMechanism {

    /**
     * No authentication.
     */
    NONE,

    /**
     * ECC: Elliptic-curve cryptography.
     */
    ECC,

    /**
     * RSA: <PERSON><PERSON><PERSON>–<PERSON><PERSON>–Adleman.
     */
    RSA,

    /**
     * HMAC: keyed-hash message authentication code.
     */
    HMAC,

    /**
     * GMAC: Galois Message Authentication Code.
     */
    GMAC
}
