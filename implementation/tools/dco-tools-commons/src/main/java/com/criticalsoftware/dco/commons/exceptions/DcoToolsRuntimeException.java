package com.criticalsoftware.dco.commons.exceptions;

/**
 * Exception to wrap unchecked exceptions.
 */
public class DcoToolsRuntimeException extends RuntimeException {

    /**
     * Constructs a new exception with specified cause.
     *
     * @param cause the cause
     */
    public DcoToolsRuntimeException(final Throwable cause) {
        super(cause);
    }

    /**
     * Constructs a new exception with specified message.
     *
     * @param message the message
     */
    public DcoToolsRuntimeException(final String message) {
        super(message);
    }

    /**
     * Constructs a new exception with specified message and cause.
     *
     * @param message the message
     * @param cause   the cause
     */
    public DcoToolsRuntimeException(final String message, final Throwable cause) {
        super(message, cause);
    }

}
