package com.criticalsoftware.dco.requestmanager.common.businessapi.exceptions;

import lombok.Getter;

import com.criticalsoftware.dco.core.common.exceptions.AbstractGenericException;
import com.criticalsoftware.dco.core.common.exceptions.ErrorCode;
import com.criticalsoftware.dco.requestmanager.common.businessapi.specificprocessing.enums.RequestManagerErrorCode;

/**
 * Exception for when the Service Request ID isn't found in the data store.
 */
@Getter
public final class ServiceRequestInvalidException extends AbstractGenericException {

    private static final String REQUEST_ID_NOT_FOUND_MESSAGE = "Service request ''{0}'' failed the attribute anomaly detect check.";

    private final String validationError;

    /**
     * Create a new instance of this exception.
     *
     * @param errorCode           resource error code
     * @param message             detailed message
     * @param validationErrorCode the validation error code
     * @param parameters          message parameters
     */
    private ServiceRequestInvalidException(final ErrorCode errorCode, final String message, final String validationErrorCode,
                                           final Object... parameters) {
        super(errorCode, message, parameters);
        validationError = validationErrorCode;
    }

    /**
     * Creates a new instance of this exception with {@link RequestManagerErrorCode#SERVICE_REQUEST_ID_NOT_FOUND_ERROR} error code.
     *
     * @param requestId           ID of the service request
     * @param validationErrorCode the validation error code field from the DB
     *
     * @return instance of {@link ServiceRequestInvalidException}
     */
    public static ServiceRequestInvalidException newServiceRequestNotFoundException(final String requestId,
                                                                                    final String validationErrorCode) {
        return new ServiceRequestInvalidException(RequestManagerErrorCode.SERVICE_REQUEST_ID_NOT_FOUND_ERROR, REQUEST_ID_NOT_FOUND_MESSAGE,
            validationErrorCode, requestId);
    }

}
