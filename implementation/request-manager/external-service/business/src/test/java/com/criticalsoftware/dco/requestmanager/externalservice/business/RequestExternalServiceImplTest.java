package com.criticalsoftware.dco.requestmanager.externalservice.business;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.criticalsoftware.dco.dcodata.persistence.request.RequestDAO;

class RequestExternalServiceImplTest {

    @InjectMocks
    private RequestExternalServiceImpl victim;

    @Mock
    private RequestDAO requestDAO;

    private final String S1SP = "IE";
    private final String REQUEST_ID = "1";
    private final String SRV_11_3 = "11.3";
    private final String SRV_11 = "11";

    private AutoCloseable autoCloseable;

    @BeforeEach
    public void setUp() {
        autoCloseable = openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        autoCloseable.close();
    }
    @Test
    void testGetRequestSrv11_3() {
        when(requestDAO.getSrv(REQUEST_ID, S1SP)).thenReturn(SRV_11_3);
        assertEquals(victim.getRequestSrv(REQUEST_ID, S1SP), SRV_11_3);
    }

    @Test
    void testGetRequestSrvNot11_3() {
        when(requestDAO.getSrv(REQUEST_ID, S1SP)).thenReturn(SRV_11);
        assertEquals(victim.getRequestSrv(REQUEST_ID, S1SP), SRV_11);
    }

}
