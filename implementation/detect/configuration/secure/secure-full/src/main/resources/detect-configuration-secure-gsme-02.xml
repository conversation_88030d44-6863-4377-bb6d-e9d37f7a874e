<?xml version="1.0" encoding="UTF-8" standalone="no"?><SecureDetectConfiguration xmlns="http://www.dccinterface.co.uk/SecureDetectConfiguration" author="CSW" creationDate="2019-02-04" deviceModel="1031C4A10501C4A1F002" s1sp="SECURE" schemaVersion="1.0" version="1.0.0">
  <EnrolmentKeyChange constraint="true" critical="true" expiryTimeOffset="10800"/>
  <KeyChange constraint="true" critical="true" expiryTimeOffset="43200"/>
  <SUA>
    <ServiceRequests>
      <!-- SRV name - Update Import Tariff -->
      <ServiceRequest serviceReferenceVariant="1.1.1">
        <SUAActions>
          <!-- SUA Action - Change Tariff Plan -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='GasTariffElements']" expiryTimeOffset="43200" id="2">
            <Rules>
              <!-- SUA Constraint - Max Standing Charge -->
              <Rule critical="true" handler="IntegerHandlerSecure" id="3">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='StandingCharge']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='BlockTariff']" handler="MaxPriceIntegerHandler" id="4">
                <Parameters>
                  <!--Price Rule for Block-->
                  <DUISParameter name="prices">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='BlockTariff']/*[local-name()='BlockPrice']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <!--Price Rule for TOU-->
              <Rule critical="true" duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='TOUTariff']" handler="MaxPriceIntegerHandler" id="4">
                <Parameters>
                  <DUISParameter name="prices">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='TOUTariff']/*[local-name()='TOUPrice']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="BooleanHandlerSecure" id="5">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>false</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="6">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumMeterBalanceThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>500000</Value>
                  </FixedParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="7">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumCreditThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>8000</Value>
                  </FixedParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - UpdatePrice (PrimaryElement) -->
      <ServiceRequest serviceReferenceVariant="1.2.1">
        <SUAActions>
          <!-- SUA Action - Change Prices -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']" expiryTimeOffset="43200" id="3">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="3">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='StandingCharge']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <!--Price Rule for Block-->
              <Rule critical="true" duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='BlockTariff']" handler="MaxPriceIntegerHandler" id="4">
                <Parameters>
                  <DUISParameter name="prices">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='BlockTariff']/*[local-name()='BlockPrice']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <!--Price Rule for TOU-->
              <Rule critical="true" duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='TOUTariff']" handler="MaxPriceIntegerHandler" id="4">
                <Parameters>
                  <DUISParameter name="prices">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='GasPriceElements']/*[local-name()='TOUTariff']/*[local-name()='TOUPrice']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Meter Balance -->
      <ServiceRequest serviceReferenceVariant="1.5">
        <SUAActions>
          <!--SUA Action - Adjust Meter Balance - Credit/Prepay Mode with  AdjustMeterBalance with non-negative value-->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='AdjustMeterBalance'][not(contains(text(),'-'))] | /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='AdjustMeterBalance'][not(contains(text(),'-'))] " expiryTimeOffset="43200" id="29">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="21">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>0</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Adjust Meter Balance - Credit/Prepay Mode with  AdjustMeterBalance with negative value-->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='AdjustMeterBalance'][contains(text(),'-')] | /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='AdjustMeterBalance'][contains(text(),'-')]" expiryTimeOffset="43200" id="29">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="21">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>1</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Adjust Meter Balance - Credit/Prepay Mode with ResetMeterBalance -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='ResetMeterBalance'] | /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='ResetMeterBalance']" expiryTimeOffset="43200" id="29">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="21">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Payment Mode -->
      <ServiceRequest serviceReferenceVariant="1.6">
        <SUAActions>
          <!-- SUA Action - Change Tariff Plan - Credit Mode -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Credit']" expiryTimeOffset="43200" id="2">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="1">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>0</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="3">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>StandingChargeGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>200000</Value>
                  </FixedParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="MaxIntegerHandler" id="4">
                <Parameters>
                  <AttributeAnomalyParameter name="value1">
                    <Value>TariffTOUPriceMatrixGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale1">
                    <Value>-5</Value>
                  </FixedParameter>
                  <AttributeAnomalyParameter name="value2">
                    <Value>TariffBlockPriceMatrixGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale2">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>150000</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackScale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="6">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumMeterBalanceThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>500000</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="7">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumCreditThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>8000</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Change Tariff Plan - Prepayment Mode -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']" expiryTimeOffset="43200" id="2">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="1">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>1</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="3">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>StandingChargeGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>200000</Value>
                  </FixedParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="MaxIntegerHandler" id="4">
                <Parameters>
                  <AttributeAnomalyParameter name="value1">
                    <Value>TariffTOUPriceMatrixGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale1">
                    <Value>-5</Value>
                  </FixedParameter>
                  <AttributeAnomalyParameter name="value2">
                    <Value>TariffBlockPriceMatrixGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale2">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>150000</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackScale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="6">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumMeterBalanceThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>500000</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="7">
                <Parameters>
                  <AttributeAnomalyParameter name="value">
                    <Value>MaximumCreditThresholdGas</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>8000</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Prepay Configuration -->
      <ServiceRequest serviceReferenceVariant="2.1">
        <SUAActions>
          <!-- SUA Action - Change Prepayment Configuration -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigGas']" expiryTimeOffset="43200" id="4">
            <Rules>
              <Rule critical="true" handler="BooleanHandlerSecure" id="5">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>false</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="6">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigGas']/*[local-name()='MaxMeterBalance']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="7">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigGas']/*[local-name()='MaxCreditThreshold']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="8">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigGas']/*[local-name()='EmergencyCreditLimit']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>down</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Change Debt Configuration -->
          <SUAAction expiryTimeOffset="43200" id="11">
            <Rules>
              <Rule critical="true" handler="MaxIntegerHandler" id="13">
                <Parameters>
                  <AttributeAnomalyParameter name="value1">
                    <Value>DebtRecoveryRates1GasHourly</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale1">
                    <Value>-5</Value>
                  </FixedParameter>
                  <AttributeAnomalyParameter name="value2">
                    <Value>DebtRecoveryRates1GasDaily</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale2">
                    <Value>-5</Value>
                  </FixedParameter>
                  <AttributeAnomalyParameter name="value3">
                    <Value>DebtRecoveryRates2GasHourly</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale3">
                    <Value>-5</Value>
                  </FixedParameter>
                  <AttributeAnomalyParameter name="value4">
                    <Value>DebtRecoveryRates2GasDaily</Value>
                  </AttributeAnomalyParameter>
                  <FixedParameter name="scale4">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackValue">
                    <Value>200000</Value>
                  </FixedParameter>
                  <FixedParameter name="fallbackScale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Debt -->
      <ServiceRequest serviceReferenceVariant="2.3">
        <SUAActions>
          <!-- SUA Action - Change Debt Configuration -->
          <SUAAction expiryTimeOffset="43200" id="11">
            <Rules>
              <Rule critical="true" handler="MaxDebtAmountHandler" id="12">
                <Parameters>
                  <DUISParameter name="timeDebt1">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='TimeDebtRegister1']
                    </Value>
                  </DUISParameter>
                  <DUISParameter name="timeDebt2">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='TimeDebtRegister2']
                    </Value>
                  </DUISParameter>
                  <DUISParameter name="paymentDebt">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='PaymentDebtRegister']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="MaxIntegerHandler" id="13">
                <Parameters>
                  <DUISParameter name="value1">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='GasDebtRecovery1']/*[local-name()='DebtRecoveryRate']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale1">
                    <Value>-5</Value>
                  </FixedParameter>
                  <DUISParameter name="value2">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='GasDebtRecovery2']/*[local-name()='DebtRecoveryRate']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale2">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="IntegerHandlerSecure" id="14">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='DebtRecoveryPerPayment']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="scale">
                    <Value>-2</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Restrict Access For Change Of Tenancy -->
      <ServiceRequest serviceReferenceVariant="3.2">
        <SUAActions>
          <!-- SUA Action - Publish Change of Tenancy -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='RestrictAccessForChangeOfTenancy']/*[local-name()='RestrictionDateTime']" expiryTimeOffset="43200" id="24">
            <Rules>
              <Rule critical="true" handler="BooleanHandlerSecure" id="2">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>false</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='RestrictAccessForChangeOfTenancy']/*[local-name()='RestrictionDateTime']
                    </Value>
                  </DUISParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Clear Event Log -->
      <ServiceRequest serviceReferenceVariant="3.3">
        <SUAActions>
          <!-- SUA Action - Clear Meter Data -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='ClearEventLog']" expiryTimeOffset="43200" id="19">
            <Rules>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Device Configuration (Gas Conversion) -->
      <ServiceRequest serviceReferenceVariant="6.6">
        <SUAActions>
          <!-- SUA Action - Change Gas Parameters -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasConversion']" expiryTimeOffset="43200" id="6">
            <Rules>
              <!-- Max Conversion Factor -->
              <Rule critical="true" handler="IntegerHandlerSecure" id="9">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasConversion']/*[local-name()='ConversionFactor']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="targetScale">
                    <Value>-5</Value>
                  </FixedParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <!-- Max Calorific Value -->
              <Rule critical="true" handler="IntegerHandlerSecure" id="10">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasConversion']/*[local-name()='CalorificValue']
                    </Value>
                  </DUISParameter>
                  <FixedParameter name="roundingMethod">
                    <Value>up</Value>
                  </FixedParameter>
                  <FixedParameter name="targetScale">
                    <Value>-5</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <!-- Activation Time -->
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Change Profile Configuration -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasConversion']" expiryTimeOffset="43200" id="17">
          <Rules>
          </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Device Configuration (Gas Flow) -->
      <ServiceRequest serviceReferenceVariant="6.7">
        <SUAActions>
          <!-- SUA Action - Change Event Configuration - True (SupplyDepletionState or SupplyTamperState  = Locked)-->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasFlow']/*[local-name()='SupplyDepletionState'][text()='Locked'] | /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasFlow']/*[local-name()='SupplyTamperState'][text()='Locked']" expiryTimeOffset="43200" id="15">
            <Rules>
              <Rule critical="true" handler="BooleanHandlerSecure" id="2">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>true</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Change Event Configuration - False (SupplyDepletionState and SupplyTamperState  = Unchanged)-->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasFlow']/*[local-name()='SupplyDepletionState'][text()='Unchanged'] and /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationGasFlow']/*[local-name()='SupplyTamperState'][text()='Unchanged']" expiryTimeOffset="43200" id="15">
            <Rules>
              <Rule critical="true" handler="BooleanHandlerSecure" id="2">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>false</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
          <!-- SUA Action - Change System Parameters  -->
          <SUAAction expiryTimeOffset="43200" id="18">
            <Rules>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Update Device Configuration (Billing Calendar) -->
      <ServiceRequest serviceReferenceVariant="6.8">
        <SUAActions>
          <!-- SUA Action - Change Billing Dates -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='GasBillingCalendar']" expiryTimeOffset="43200" id="5">
            <Rules>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <DUISParameter name="value">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='GasBillingCalendar']/*[local-name()='BillingPeriodStart']
                    </Value>
                  </DUISParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Disable Supply -->
      <ServiceRequest serviceReferenceVariant="7.2">
        <SUAActions>
          <!-- SUA Action - Supply Control -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='DisableSupply']" expiryTimeOffset="43200" id="26">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="19">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>0</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Arm Supply -->
      <ServiceRequest serviceReferenceVariant="7.3">
        <SUAActions>
          <!-- SUA Action - Supply Control -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='ArmSupply']" expiryTimeOffset="43200" id="26">
            <Rules>
              <Rule critical="true" handler="IntegerHandlerSecure" id="19">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>1</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
      <!-- SRV name - Activate Firmware -->
      <ServiceRequest serviceReferenceVariant="11.3">
        <SUAActions>
          <!-- SUA Action - Update Firmware -->
          <SUAAction duisFilter="/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='ActivateFirmware']" expiryTimeOffset="43200" id="28">
            <Rules>
              <Rule critical="true" handler="FirmwareHashBytesHandler" id="23">
                <Parameters>
                  <DUISParameter name="requestID">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Header']/*[local-name()='RequestID']
                    </Value>
                  </DUISParameter>
                  <DUISParameter name="smets2Hash">
                    <Value>
                      /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='ActivateFirmware']/*[local-name()='FirmwareHash']
                    </Value>
                  </DUISParameter>
                </Parameters>
              </Rule>
              <Rule critical="true" handler="TimestampHandler" id="25">
                <Parameters>
                  <FixedParameter name="value">
                    <Value>2000-01-01T00:00:00.00Z</Value>
                  </FixedParameter>
                </Parameters>
              </Rule>
            </Rules>
          </SUAAction>
        </SUAActions>
      </ServiceRequest>
    </ServiceRequests>
    <NoServiceRequest>
      <SUAActions>
        <!-- SUA Action - Adjust Meter Time -->
        <SUAAction expiryTimeOffset="86400" id="0">
          <Rules>
            <Rule critical="true" handler="IntegerHandlerSecure" id="0">
              <Parameters>
                <FixedParameter name="value">
                  <Value>7200</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </SUAAction>
        <!-- SUA Action - Adjust Meter Time -->
        <SUAAction expiryTimeOffset="86400" id="100" suaId="0">
          <Rules>
            <Rule critical="true" handler="IntegerHandlerSecure" id="0">
              <Parameters>
                <FixedParameter name="value">
                  <Value>-7200</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </SUAAction>
      </SUAActions>
    </NoServiceRequest>
  </SUA>
  <Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><DigestValue>B16AkxmV4EIPmG4pDf1U9awsyQdet0Q7aDB1j98dI5Y=</DigestValue></Reference></SignedInfo><SignatureValue>gCsL7a3h8K3d9S+Row5xLFc03EDE0jSGcPfZ5u7yHEXGt/g9zI2CifciTqstyw7mWtQ5ADyzKf1t&#13;
JRN13di2Hg==</SignatureValue></Signature></SecureDetectConfiguration>