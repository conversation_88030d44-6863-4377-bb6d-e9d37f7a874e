package com.criticalsoftware.dco.detect.attributeanomaly.business.engine;

import java.util.Map;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.Validate;
import uk.co.dccinterface.s1sp.SMETS1RequestType;

import com.criticalsoftware.dco.detect.attributeanomaly.business.common.AttributeInfoService;
import com.criticalsoftware.dco.detect.attributeanomaly.business.common.enums.AttributeName;
import com.criticalsoftware.dco.detect.attributeanomaly.business.common.enums.SrvList;
import com.criticalsoftware.dco.detect.attributeanomaly.business.common.utils.AttributeInfo;
import com.criticalsoftware.dco.detect.attributeanomaly.businessapi.business.engine.AttributeAnomalyDetectEngineService;
import com.criticalsoftware.dco.detect.attributeanomaly.businessapi.exceptions.AttributeAnomalyDetectException;
import com.criticalsoftware.dco.detect.attributeanomaly.handlers.AttributeAnomalyDetectHandler;

/**
 * This is the Anomaly Attribute detect configuration interface implementation.
 */
@ApplicationScoped
@Slf4j
public class AttributeAnomalyDetectEngineServiceImpl implements AttributeAnomalyDetectEngineService {

    
    private final AttributeInfoService attributeInfoService;

    private final AttributeAnomalyHandlerLocatorService handlerService;

    /**
     * AttributeAnomalyDetectEngineServiceImpl constructor.
     *
     * @param attributeInfoService attribute info data service
     * @param handlerService       handler locator service
     */
    public AttributeAnomalyDetectEngineServiceImpl(final AttributeInfoService attributeInfoService,
                                                   final AttributeAnomalyHandlerLocatorService handlerService) {
        this.attributeInfoService = attributeInfoService;
        this.handlerService = handlerService;
    }

    @Override
    public void detect(final SMETS1RequestType serviceRequest, final String serviceReferenceVariant)
        throws AttributeAnomalyDetectException {
        log.debug("operation=AttributeAnomalyDetectEngineService, message='Start operation anomaly detect.'");
        Validate.notNull(serviceRequest, "The validated 'serviceRequest' is null");

        // Check if the SRV was extracted correctly
        if (serviceReferenceVariant != null && !serviceReferenceVariant.isEmpty()) {
            final SrvList srv = SrvList.getByString(serviceReferenceVariant);
            if (srv != null) {
                performAttributeAnomalyDetection(serviceRequest, serviceReferenceVariant, srv);
            }
        }
    }

    private void performAttributeAnomalyDetection(final SMETS1RequestType serviceRequest,
                                                  final String serviceReferenceVariant,
                                                  final SrvList srv)
        throws AttributeAnomalyDetectException {

        final Map<AttributeName, AttributeInfo> srvInfo = attributeInfoService.getSRVInfo(srv);
        if (srvInfo == null || srvInfo.isEmpty()) {
            log.debug(
                "operation=AttributeAnomalyDetectEngineService, message='No attribute anomaly validation for the given SRV.', "
                    + "serviceReferenceVariant={}",
                serviceReferenceVariant);
            return;
        }

        for (final Map.Entry<AttributeName, AttributeInfo> srvInfoEntrySet : srvInfo.entrySet()) {
            final AttributeName attributeName = srvInfoEntrySet.getKey();
            final AttributeInfo attributeInfo = srvInfoEntrySet.getValue();

            log.debug("operation=AttributeAnomalyDetectEngineService.detect, "
                + "message='Performing attribute anomaly detection.', attributeName={}", attributeName);

            // use the locator to define which handler to use
            final AttributeAnomalyDetectHandler handler = handlerService.getHandler(attributeInfo.getHandler())
                .orElseThrow(AttributeAnomalyDetectException::newAttributeAnomalyDetectHandlersException);

            // execute anomaly detection with the valid handler
            if (!handler.detect(serviceRequest, attributeInfo.getLimitType(), attributeInfo.getAttributeValuesSeekers(),
                attributeInfo.getLimitValue())) {
                log.error("operation=AttributeAnomalyDetectEngineService, "
                        + "message='Invalid DUIS values', "
                        + "serviceReferenceVariant={},"
                        + "attributeName={},",
                    serviceReferenceVariant, attributeName);
                throw AttributeAnomalyDetectException.newAttributeAnomalyDetectDuisDetectionException(attributeName.toString());
            }
            log.debug("operation=AttributeAnomalyDetectEngineService, message='Success of"
                    + " anomaly detect operation.',"
                    + "serviceReferenceVariant={},"
                    + "attributeName={},",
                serviceReferenceVariant, attributeName);
        }
    }

}
