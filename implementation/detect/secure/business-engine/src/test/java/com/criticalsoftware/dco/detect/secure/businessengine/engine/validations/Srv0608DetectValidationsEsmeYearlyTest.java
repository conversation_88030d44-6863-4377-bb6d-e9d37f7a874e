package com.criticalsoftware.dco.detect.secure.businessengine.engine.validations;

import static org.junit.jupiter.api.Assertions.assertThrows;

import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.xml.sax.SAXException;

import com.criticalsoftware.dco.core.common.exceptions.XmlOperationsException;
import com.criticalsoftware.dco.detect.secure.businessengine.engine.enums.SrvEnum;
import com.criticalsoftware.dco.detect.secure.businessengine.service.SuaConstraintImpl;
import com.criticalsoftware.dco.detect.secure.exceptions.DetectSecureGenericException;
import com.criticalsoftware.dco.detect.secure.service.SuaConstraint;
import com.criticalsoftware.dco.detect.secure.srvtestexamples.enums.SrvFileEnum;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Srv0608DetectValidationsEsmeYearlyTest extends SrvDetectAbstractValidationsTest {

    private static final List<String> VALID_ACTION = Collections.singletonList("5");
    private static final List<String> INVALID_ACTION = Collections.singletonList("3");
    private static final String VALID_SERVICE_REFERENCE_VARIANT = SrvEnum.SRV6_8.getSrv();
    private static final String INVALID_SERVICE_REFERENCE_VARIANT = "10.1";
    private static final String DETECT_CONFIGURATION = "/detect-configuration-secure-esme.xml";
    private static final String INVALID_DETECT_CONFIGURATION = "/detect-configuration-secure-esme_INVALID.xml";

    private DetectTestObject testObject;

    private static byte[] convert(Date value) {
        final Date referenceDate = new GregorianCalendar(2000, Calendar.JANUARY, 1).getTime();
        long l = (value.getTime() - referenceDate.getTime()) / 1000;
        return convert(l);
    }

    private static byte[] convert(long value) {
        return convert(BigInteger.valueOf(value));
    }

    private static byte[] convert(BigInteger value) {
        return value.toByteArray();
    }

    @BeforeAll
    void setup() throws SAXException, XmlOperationsException, IOException {
        testObject = new DetectTestObject(VALID_ACTION, INVALID_ACTION, VALID_SERVICE_REFERENCE_VARIANT,
            INVALID_SERVICE_REFERENCE_VARIANT, DETECT_CONFIGURATION, INVALID_DETECT_CONFIGURATION,
            SrvFileEnum.DUIS_6_8_ESME_YEARLY.getDuisXmlPath());
        initTest(testObject);
    }

    @Test
    void validateConfigurationSrvSuccessTestSrv0608Esme() throws DetectSecureGenericException {
        validateConfigurationServiceReferenceVariantSuccessTest(testObject);
    }

    @Test
    void validateConfigurationSuaActionSuccessTestSrv0608Esme() throws DetectSecureGenericException {
        validateConfigurationSUAActionSuccessTest(testObject);
    }

    @Test
    void validateConfigurationSrvFailsTestSrv0608Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationServiceReferenceVariantFailTest(testObject),
            "Configuration not found for .*serviceReferenceVariant.*"
        );
    }

    @Test
    void validateConfigurationSuaActionFailsTestSrv0608Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationSUAActionFailTest(testObject),
            "ActionID.*is not authorised for deviceModel.*and serviceReferenceVariant.*"
        );
    }

    @Test
    void validateConfigurationDuisFilterFailTestSrv0608Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationDuisFilterFailTest(testObject),
            "Invalid DUIS filter for actionID.*"
        );
    }

    @Test
    void validConstraintValueSrv0608EsmeQuarterly() throws Exception {
        List<SuaConstraint> constraints = new ArrayList<>();

        Calendar now = Calendar.getInstance();
        Calendar tmp = (Calendar) now.clone();
        tmp.set(Calendar.HOUR_OF_DAY, 0);
        tmp.set(Calendar.MINUTE, 0);
        tmp.set(Calendar.SECOND, 0);
        tmp.set(Calendar.MILLISECOND, 0);

        tmp.set(Calendar.DAY_OF_MONTH, 2);
        tmp.set(Calendar.MONTH, 0);


        if (tmp.after(now)) {
            tmp.add(Calendar.YEAR, -1);
        }

        Instant instantInUtc = Instant.ofEpochMilli(tmp.getTimeInMillis());
        Date dateResult = Date.from(instantInUtc);

        constraints.add(new SuaConstraintImpl(25, convert(dateResult), true));

        validateConstraintsSuccess("5", constraints);
    }

}