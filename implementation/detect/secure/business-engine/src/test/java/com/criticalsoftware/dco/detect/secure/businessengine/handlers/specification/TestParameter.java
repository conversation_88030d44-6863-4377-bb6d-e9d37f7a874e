package com.criticalsoftware.dco.detect.secure.businessengine.handlers.specification;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TestParameter {

    VALUE("value"),
    SCALE("scale"),
    VALUE_1("value1"),
    SCALE_1("scale1"),
    VALUE_2("value2"),
    SCALE_2("scale2"),
    VALUE_3("value3"),
    SCALE_3("scale3"),
    VALUE_4("value4"),
    SCALE_4("scale4"),
    FALLBACK_VALUE("fallbackValue"),
    FALLBACK_SCALE("fallbackScale"),
    TARGET_SCALE("targetScale"),
    ROUNDING_METHOD("roundingMethod"),
    PRICES("prices"),
    TIME_DEBT1("timeDebt1"),
    TIME_DEBT2("timeDebt2"),
    PAYMENT_DEBT("paymentDebt"),
    REQUEST_ID("requestID"),
    SMETS_2_HASH("smets2Hash");

    private final String type;
}
