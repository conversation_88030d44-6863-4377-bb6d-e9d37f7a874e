package com.criticalsoftware.dco.detect.secure.businessengine.engine.validations;

import static org.junit.jupiter.api.Assertions.assertThrows;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;
import javax.xml.parsers.DocumentBuilderFactory;

import jakarta.xml.bind.DatatypeConverter;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import com.criticalsoftware.dco.core.common.exceptions.XmlOperationsException;
import com.criticalsoftware.dco.detect.secure.businessengine.service.SuaConstraintImpl;
import com.criticalsoftware.dco.detect.secure.exceptions.DetectSecureGenericException;
import com.criticalsoftware.dco.detect.secure.service.SuaConstraint;
import com.criticalsoftware.dco.detect.secure.srvtestexamples.enums.SrvFileEnum;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class Srv0612DetectValidationsEsmeTest extends SrvDetectAbstractValidationsTest {

    private static final List<String> VALID_ACTION = Collections.singletonList("12");
    private static final List<String> INVALID_ACTION = Collections.singletonList("3");
    private static final String VALID_SERVICE_REFERENCE_VARIANT = "6.12";
    private static final String INVALID_SERVICE_REFERENCE_VARIANT = "10.1";
    private static final String DETECT_CONFIGURATION = "/detect-configuration-secure-esme.xml";
    private static final String INVALID_DETECT_CONFIGURATION = "/detect-configuration-secure-esme_INVALID.xml";
    private static final String VALID_DUIS_XML_PATH = SrvFileEnum.DUIS_6_12_ESME.getDuisXmlPath();
    private static final String VALID_DUIS_XML_PATH_WITH_WHITESPACES = SrvFileEnum.DUIS_6_12_ESME_WITH_WHITESPACES.getDuisXmlPath();

    private DetectTestObject testObject;

    static Stream<Arguments> validDuisXmlPathMethodSource() {
        return Stream.of(Arguments.of(VALID_DUIS_XML_PATH), Arguments.of(VALID_DUIS_XML_PATH_WITH_WHITESPACES));
    }

    @BeforeAll
    void setup() throws SAXException, XmlOperationsException, IOException {

        testObject = new DetectTestObject(VALID_ACTION, INVALID_ACTION, VALID_SERVICE_REFERENCE_VARIANT,
            INVALID_SERVICE_REFERENCE_VARIANT, DETECT_CONFIGURATION, INVALID_DETECT_CONFIGURATION,
            SrvFileEnum.DUIS_6_12_ESME.getDuisXmlPath());

        initTest(testObject);
    }

    @Test
    void validateConfigurationSrvSuccessTestSrv0612Esme() throws DetectSecureGenericException {

        validateConfigurationServiceReferenceVariantSuccessTest(testObject);
    }

    @Test
    void validateConfigurationSuaActionSuccessTestSrv0612Esme() throws DetectSecureGenericException {

        validateConfigurationSUAActionSuccessTest(testObject);
    }

    @Test
    void validateConfigurationSrvFailsTestSrv0612Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationServiceReferenceVariantFailTest(testObject),
            "Configuration not found for .*serviceReferenceVariant.*"
        );
    }

    @Test
    void validateConfigurationSuaActionFailsTestSrv0612Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationSUAActionFailTest(testObject),
            "ActionID.*is not authorised for deviceModel.*and serviceReferenceVariant.*"
        );
    }

    @Test
    void validateConfigurationDuisFilterFailTestSrv0612Esme() {
        assertThrows(DetectSecureGenericException.class,
            () -> validateConfigurationDuisFilterFailTest(testObject),
            "Invalid DUIS filter for actionID.*"
        );
    }

    @MethodSource("validDuisXmlPathMethodSource")
    @ParameterizedTest
    void validConstraintValueSrv0612Esme(final String duisPath) throws Exception {
        final List<SuaConstraint> constraints = new ArrayList<>();
        constraints.add(new SuaConstraintImpl(15, DatatypeConverter.parseHexBinary("01"), true));
        validateConstraintsSuccess(duisPath, "12", constraints);
    }


    @MethodSource("validDuisXmlPathMethodSource")
    @ParameterizedTest
    void validConstraintValueEmptyAnomalySrv0612Esme(final String duisPath) throws Exception {
        final List<SuaConstraint> constraints = new ArrayList<>();
        constraints.add(new SuaConstraintImpl(15, DatatypeConverter.parseHexBinary("01"), true));
        final DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);

        final Document duisDoc;
        try (InputStream is = getClass().getResourceAsStream(duisPath)) {
            duisDoc = dbf.newDocumentBuilder().parse(is);
        }

        validateConstraintsEmptyAnomalySuccess(duisDoc, "12", constraints);
    }

}