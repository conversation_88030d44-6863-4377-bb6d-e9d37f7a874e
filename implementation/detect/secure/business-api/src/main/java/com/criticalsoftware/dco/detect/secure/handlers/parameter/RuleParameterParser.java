package com.criticalsoftware.dco.detect.secure.handlers.parameter;

import org.w3c.dom.Document;

import com.criticalsoftware.dco.detect.attributeanomaly.businessapi.business.engine.AttributeAnomalyDetectLimitValueService;
import com.criticalsoftware.dco.detect.secure.businessentities.generated.RuleParameter;

/**
 * This is the interface for all DUIS parameters kind parsers.
 */
public interface RuleParameterParser {

    /**
     * This method implements the parser for the specific data type.
     *
     * @param requestDocument         the DUIS document.
     * @param ruleParameter           the rule parameter.
     * @param parameterDefinitionType the parameter data type
     * @param anomalyService          the service that provides the anomaly values.
     * @param <T>                     the generic type.
     *
     * @return the parser object.
     */
    <T> T parseRuleParameter(Document requestDocument, RuleParameter ruleParameter, Class<T> parameterDefinitionType,
                             AttributeAnomalyDetectLimitValueService anomalyService);

}
