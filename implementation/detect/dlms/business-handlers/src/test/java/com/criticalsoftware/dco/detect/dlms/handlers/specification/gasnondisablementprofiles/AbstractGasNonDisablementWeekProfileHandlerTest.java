package com.criticalsoftware.dco.detect.dlms.handlers.specification.gasnondisablementprofiles;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.criticalsoftware.smartmetering.codecs.cosem.converters.ArrayConverter;

class AbstractGasNonDisablementWeekProfileHandlerTest {

    private AbstractGasNonDisablementWeekProfileHandler victim;

    @BeforeEach
    void setUp() {
        victim = new AbstractGasNonDisablementWeekProfileHandlerFake();
    }

    @Test
    void getConverterSuccess() {
        assertInstanceOf(ArrayConverter.class, victim.getConverter());
    }

}

