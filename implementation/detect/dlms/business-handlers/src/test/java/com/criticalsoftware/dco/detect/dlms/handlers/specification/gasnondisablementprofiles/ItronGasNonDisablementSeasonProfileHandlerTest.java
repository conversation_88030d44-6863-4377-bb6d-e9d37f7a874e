package com.criticalsoftware.dco.detect.dlms.handlers.specification.gasnondisablementprofiles;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.params.provider.Arguments.arguments;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import uk.co.dccinterface.serviceusergateway.GasDateWithWildcards;
import uk.co.dccinterface.serviceusergateway.GasDayOfMonthWithWildcards;
import uk.co.dccinterface.serviceusergateway.GasDayOfWeekWithWildcards;
import uk.co.dccinterface.serviceusergateway.GasMonthWithWildcards;
import uk.co.dccinterface.serviceusergateway.GasNonDisablementCalendar;
import uk.co.dccinterface.serviceusergateway.GasSeasonNonDisablement;
import uk.co.dccinterface.serviceusergateway.GasSeasonsNonDisablement;
import uk.co.dccinterface.serviceusergateway.GasYearWithWildcards;
import uk.co.dccinterface.serviceusergateway.NoType;

import com.criticalsoftware.dco.detect.dlms.handlers.DetectParameters;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.ArrayConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.types.Season;

/**
 * This is a test class for ItronGasNonDisablementSeasonProfileHandlerTest
 */
class ItronGasNonDisablementSeasonProfileHandlerTest {

    private ItronGasNonDisablementSeasonProfileHandler victim;

    static Stream<Arguments> testParamsMethodSource() {
        final GasNonDisablementCalendar successTestProfile =
            makeGasNonDisablementCalendar(BigInteger.valueOf(2018), 10, 28, null, 1, BigInteger.valueOf(2018), 3, 25, null, 2);
        final GasNonDisablementCalendar incorrectDatesTestProfile =
            makeGasNonDisablementCalendar(BigInteger.valueOf(2018), 10, 27, null, 1, BigInteger.valueOf(2018), 3, 25, null, 2);
        final GasNonDisablementCalendar wildcardYearTestProfile =
            makeGasNonDisablementCalendar(BigInteger.valueOf(2018), 10, 28, null, 1, null, 3, 25, null, 2);
        final GasNonDisablementCalendar wildcardMonthTestProfile =
            makeGasNonDisablementCalendar(BigInteger.valueOf(2018), 10, 28, null, 1, BigInteger.valueOf(2018), null, 25, null, 2);
        final GasNonDisablementCalendar wildcardDayOfMonthTestProfile =
            makeGasNonDisablementCalendar(BigInteger.valueOf(2018), 10, 28, null, 1, BigInteger.valueOf(2018), 3, null, null, 2);

        return Stream.of(
            // Detect Success
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(successTestProfile, Boolean.FALSE), true),
            // Detect Success - different dayOfMonth
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(incorrectDatesTestProfile, Boolean.FALSE), true),
            // Detect Fail - earliestSeason does not match
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x0A, 0x1C, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(successTestProfile, Boolean.FALSE), false),
            // Detect Fail - cosem array has wrong size
            arguments(makeWrongCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1},
                new byte[] {0, 0, 0, 0, 0, 0, 0, 1}, new byte[] {0x07, (byte) 0xE2, 0x0A, 0x1C, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0, 0, 0, 0, 0, 0, 0, 2}),
                makeParameters(successTestProfile, Boolean.FALSE), false),
            // Detect Fail - start date wrong size
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0},
                new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(successTestProfile, Boolean.FALSE), false),
            // Detect Fail - wildcard year
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {(byte) 0xFF, (byte) 0xFF, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(wildcardYearTestProfile, Boolean.FALSE), false),
            // Detect Fail - wildcard month
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(wildcardMonthTestProfile, Boolean.FALSE), false),
            // Detect Fail - wildcard day of month
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 1}),
                makeParameters(wildcardDayOfMonthTestProfile, Boolean.FALSE), false),
            // Detect Fail - wrong cosem week id
            arguments(makeCosemData(new byte[] {0, 0, 0, 0, 0, 0, 0, 0},
                new byte[] {0x07, (byte) 0xE2, 0x03, 0x19, (byte) 0xFF, 0, 0, 0, 0, 0, 0, 0}, new byte[] {0, 0, 0, 0, 0, 0, 0, 2}),
                makeParameters(successTestProfile, Boolean.FALSE), false)

        );
    }

    @BeforeEach
    void setUp() {
        victim = new ItronGasNonDisablementSeasonProfileHandler();
    }

    @MethodSource("testParamsMethodSource")
    @ParameterizedTest
    void testDetect(final List<Season> cosemData, final DetectParameters parameters, final boolean result) {
        assertEquals(result, victim.detect(cosemData, parameters));
    }

    @Test
    void getConverterSuccess() {
        assertInstanceOf(ArrayConverter.class, victim.getConverter());
    }

    private static GasNonDisablementCalendar makeGasNonDisablementCalendar(final BigInteger firstYear, final Integer firstMonth,
                                                                           final Integer firstDayOfMonth, final Integer firstDayOfWeek,
                                                                           final Integer firstReferencedDayName,
                                                                           final BigInteger secondYear,
                                                                           final Integer secondMonth, final Integer secondDayOfMonth,
                                                                           final Integer secondDayOfWeek,
                                                                           final Integer secondReferencedDayName) {
        final GasNonDisablementCalendar gasNonDisablementCalendar = new GasNonDisablementCalendar();
        final GasSeasonsNonDisablement gasSeasonsNonDisablement =
            makeSeasonsProfiles(firstYear, firstMonth, firstDayOfMonth, firstDayOfWeek, firstReferencedDayName, secondYear, secondMonth,
                secondDayOfMonth, secondDayOfWeek, secondReferencedDayName);

        gasNonDisablementCalendar.setSeasonProfiles(gasSeasonsNonDisablement);

        return gasNonDisablementCalendar;
    }


    private static GasSeasonsNonDisablement makeSeasonsProfiles(final BigInteger firstYear, final Integer firstMonth,
                                                                final Integer firstDayOfMonth, final Integer firstDayOfWeek,
                                                                final Integer firstReferencedDayName, final BigInteger secondYear,
                                                                final Integer secondMonth, final Integer secondDayOfMonth,
                                                                final Integer secondDayOfWeek, final Integer secondReferencedDayName) {
        final GasSeasonsNonDisablement seasons = new GasSeasonsNonDisablement();
        final List<GasSeasonNonDisablement> seasonList = seasons.getSeason();

        seasonList.add(makeParameterSeason(firstYear, firstMonth, firstDayOfMonth, firstDayOfWeek, firstReferencedDayName));
        seasonList.add(makeParameterSeason(secondYear, secondMonth, secondDayOfMonth, secondDayOfWeek, secondReferencedDayName));

        return seasons;
    }

    private static GasSeasonNonDisablement makeParameterSeason(final BigInteger year, final Integer month, final Integer dayOfMonth,
                                                               final Integer dayOfWeek, final Integer referencedDayName) {
        final GasSeasonNonDisablement season = new GasSeasonNonDisablement();
        final GasDateWithWildcards date = new GasDateWithWildcards();
        date.setGasYearWithWildcards(makeYear(year));
        date.setGasMonthWithWildcards(makeMonth(month));
        date.setGasDayOfMonthWithWildcards(makeDayOfMonth(dayOfMonth));
        date.setGasDayOfWeekWithWildcards(makeDayOfWeek(dayOfWeek));
        season.setSeasonStartDate(date);
        season.setReferencedWeekName(referencedDayName);
        return season;
    }

    private static GasDayOfWeekWithWildcards makeDayOfWeek(final Integer dayOfWeek) {
        final GasDayOfWeekWithWildcards dayOfWeekObject = new GasDayOfWeekWithWildcards();
        if (dayOfWeek != null) {
            dayOfWeekObject.setSpecifiedDayOfWeek(dayOfWeek);
        } else {
            dayOfWeekObject.setNonSpecifiedDayOfWeek(new NoType());
        }
        return dayOfWeekObject;
    }

    private static GasDayOfMonthWithWildcards makeDayOfMonth(final Integer dayOfMonth) {
        final GasDayOfMonthWithWildcards dayOfMonthObject = new GasDayOfMonthWithWildcards();
        if (dayOfMonth != null) {
            dayOfMonthObject.setSpecifiedDayOfMonth(dayOfMonth);
        } else {
            dayOfMonthObject.setGasNonSpecifiedDayOfMonth(new NoType());
        }
        return dayOfMonthObject;
    }

    private static GasMonthWithWildcards makeMonth(final Integer month) {
        final GasMonthWithWildcards monthObject = new GasMonthWithWildcards();
        if (month != null) {
            monthObject.setSpecifiedMonth(month);
        } else {
            monthObject.setNonSpecifiedMonth(new NoType());
        }
        return monthObject;
    }

    private static GasYearWithWildcards makeYear(final BigInteger year) {
        final GasYearWithWildcards yearObject = new GasYearWithWildcards();
        if (year != null) {
            yearObject.setSpecifiedYear(year);
        } else {
            yearObject.setNonSpecifiedYear(new NoType());
        }
        return yearObject;
    }

    private static DetectParameters makeParameters(final Object... params) {
        return new DetectParameters() {
            {
                put(ItronGasNonDisablementSeasonProfileHandler.Parameter.GAS_NON_DISABLEMENT_CALENDAR.getName(), params[0]);
                put(ItronGasNonDisablementSeasonProfileHandler.Parameter.DEACTIVATE_EXTRA_VALIDATIONS.getName(), params[1]);
            }
        };
    }

    private static List<Season> makeCosemData(final Object... params) {
        final byte[] SeasonProfileName = (byte[]) params[0];
        final byte[] SeasonStart = (byte[]) params[1];
        final byte[] WeekName = (byte[]) params[2];

        final List<Season> seasonList = new ArrayList<>();

        seasonList.add(makeCosemSeason(SeasonProfileName, SeasonStart, WeekName));

        return seasonList;
    }

    private static List<Season> makeWrongCosemData(final Object... params) {
        final byte[] firstSeasonProfileName = (byte[]) params[0];
        final byte[] firstSeasonStart = (byte[]) params[1];
        final byte[] firstWeekName = (byte[]) params[2];
        final byte[] secondSeasonProfileName = (byte[]) params[3];
        final byte[] secondSeasonStart = (byte[]) params[4];
        final byte[] secondWeekName = (byte[]) params[5];

        final List<Season> seasonList = new ArrayList<>();

        seasonList.add(makeCosemSeason(firstSeasonProfileName, firstSeasonStart, firstWeekName));
        seasonList.add(makeCosemSeason(secondSeasonProfileName, secondSeasonStart, secondWeekName));

        return seasonList;
    }

    private static Season makeCosemSeason(final byte[] seasonProfileName, final byte[] seasonStart, final byte[] weekName) {
        final Season season = new Season();
        season.setSeasonProfileName(seasonProfileName);
        season.setSeasonStart(seasonStart);
        season.setWeekName(weekName);
        return season;
    }

}