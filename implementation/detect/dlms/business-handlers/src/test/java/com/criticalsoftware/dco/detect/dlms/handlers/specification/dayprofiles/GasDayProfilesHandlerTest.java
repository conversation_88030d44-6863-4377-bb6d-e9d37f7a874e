package com.criticalsoftware.dco.detect.dlms.handlers.specification.dayprofiles;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.ArrayUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import uk.co.dccinterface.serviceusergateway.GasDayProfile;
import uk.co.dccinterface.serviceusergateway.GasDayProfiles;

import com.criticalsoftware.dco.detect.dlms.handlers.DetectParameters;
import com.criticalsoftware.dco.detect.dlms.handlers.SupportedParameterDefinition;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.ArrayConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.types.DayProfile;
import com.criticalsoftware.smartmetering.codecs.cosem.types.DayProfileAction;

/**
 * This is a test class to test the {@link GasDayProfilesHandler}.
 */
class GasDayProfilesHandlerTest {

    private GasDayProfilesHandler victim;

    static Object[][] testParams() {
        final Object[][] provider = new Object[][] {
            // Detect Success TOU
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                true},
            // Detect Error TOU: duplicated element in COSEM list at the end of file
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                false},
            // Detect Error TOU: a new profileAction was added to daySchedule COSEM at the end of file
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                false},
            // Detect Success block
            {makeCorrectCosemData((short) 1, 1, (short) 2, 1, (short) 3, 1),
                makeParameters(makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), true},
            // Detect Success disordered dayProfiles TOU
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(4, 2), makeGasDayProfile(1, 2), makeGasDayProfile(3, 1), makeGasDayProfile(2, 1)),
                true},
            // Detect Success disordered dayProfiles blocks
            {makeCorrectCosemData((short) 1, 1, (short) 2, 1, (short) 3, 1),
                makeParameters(makeGasDayProfile(3, null), makeGasDayProfile(2, null), makeGasDayProfile(1, null)), true},
            // Detect error wrong dayName TOU
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(5, 2)),
                false},
            // Detect error wrong dayName block
            {makeCorrectCosemData((short) 1, 1, (short) 2, 1, (short) 4, 1),
                makeParameters(makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), false},
            // Detect error wrong scriptSelector TOU
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 1), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                false},
            // Detect error wrong scriptSelector block
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1),
                makeParameters(makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), false},
            // Detect error mismatched sizes
            {makeCorrectCosemData((short) 1, 2, (short) 2, 1, (short) 3, 1, (short) 4, 2),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1)), false},
            // Detect error wrong startTime TOU
            {makeCosemData((short) 1, 2, new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 2, 1,
                new byte[] {12, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 3, 1, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 4, 2, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                false},
            // Detect error wrong startTime block
            {makeCosemData((short) 1, 1, new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 2, 1,
                new byte[] {0, 10, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 3, 1, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), false},
            // Detect error wrong scriptLogicalName TOU
            {makeCosemData((short) 1, 2, new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 2, 1,
                new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 3, 1, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 11, 0, (byte) 100, (byte) 255}, (short) 4, 2, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, 2), makeGasDayProfile(2, 1), makeGasDayProfile(3, 1), makeGasDayProfile(4, 2)),
                false},
            // Detect error wrong scriptLogicalName block
            {makeCosemData((short) 1, 1, new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}, (short) 2, 1,
                new byte[] {0, 0, 0, 0}, new byte[] {0, 0, 10, 0, (byte) 100, 0}, (short) 3, 1, new byte[] {0, 0, 0, 0},
                new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), false},
            // Detect Success with blockTariff parameter
            {makeCorrectCosemData((short) 1, 25, (short) 2, 25, (short) 3, 25), addBlockTariffParameter(makeParameters
                (makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), 25), true},
            // Detect error with blockTariff parameter
            {makeCorrectCosemData((short) 1, 25, (short) 2, 25, (short) 3, 25), addBlockTariffParameter(makeParameters
                (makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), 10), false},
            // Detect Success with fine startTime block
            {makeCosemData((short) 1, 1, new byte[] {0, 0, 0, 100}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, null)),
                true},
            // Detect error wrong startTime block (second byte not equal to zero)
            {makeCosemData((short) 1, 1, new byte[] {0, 1, 0, (byte) 255}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, null)),
                false},
            // Detect error wrong startTime block size
            {makeCosemData((short) 1, 1, new byte[] {0, 1}, new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255}),
                makeParameters(makeGasDayProfile(1, null)),
                false},
            // Detect error with blockTariff parameter
            {makeCorrectCosemData((short) 1, 25, (short) 2, 25, (short) 3, 25), addBlockTariffParameter(makeParameters
                (makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), 10), false},
            // Detect error empty COSEM data list
            {
                new ArrayList<>(), addBlockTariffParameter(makeParameters
                (makeGasDayProfile(1, null), makeGasDayProfile(2, null), makeGasDayProfile(3, null)), 10), false
            },
            // Success: both seasonProfiles's list are empty
            {
                new ArrayList<>(), new DetectParameters() {{
                put(GasDayProfilesHandler.Parameter.DAY_PROFILES.getName(), new GasDayProfiles());
            }}, true
            }
        };
        // Detect Error: duplicated element in COSEM list
        ((List<DayProfile>) provider[1][0]).get(1).setDayId((short) 1);
        ((List<DayProfile>) provider[1][0]).get(1).getDaySchedule().get(0).setScriptSelector(2);
        // Detect Error: new element will be added to daySchedule COSEM
        ((List<DayProfile>) provider[2][0]).get(0).getDaySchedule().add(1, new DayProfileAction() {{
            setStartTime(new byte[] {0, 0, 0, 0});
            setScriptSelector(5);
            setScriptLogicalName(new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255});
        }});
        return provider;
    }

    @BeforeEach
    void setUp() {
        victim = new GasDayProfilesHandler();
    }

    @Test
    void getConverter() {
        assertInstanceOf(ArrayConverter.class, victim.getConverter());
    }

    @MethodSource("testParams")
    @ParameterizedTest
    void testDetect(final List<DayProfile> dayProfiles, final DetectParameters detectParameters, final boolean result) {
        assertEquals(victim.detect(dayProfiles, detectParameters), result);
    }

    @Test
    void testSupportedParameters() {
        final SupportedParameterDefinition[] params = victim.getSupportedParameters();

        final int dayProfilesIndex = ArrayUtils.indexOf(params, GasDayProfilesHandler.Parameter.DAY_PROFILES);
        assertNotEquals(ArrayUtils.INDEX_NOT_FOUND, dayProfilesIndex);
        assertEquals(GasDayProfilesHandler.Parameter.DAY_PROFILES.getType(), params[dayProfilesIndex].getType());
    }

    private static GasDayProfile makeGasDayProfile(final int dayName, final Integer TOUTarriffAction) {
        final GasDayProfile dayProfile = new GasDayProfile();
        dayProfile.setDayName(dayName);
        dayProfile.setTOUTariffAction(TOUTarriffAction);
        return dayProfile;
    }

    private static DetectParameters makeParameters(final GasDayProfile... params) {
        return new DetectParameters() {
            {
                final GasDayProfiles dayProfiles = new GasDayProfiles();
                for (final GasDayProfile param : params) {
                    dayProfiles.getDayProfile().add(param);
                }
                put(GasDayProfilesHandler.Parameter.DAY_PROFILES.getName(), dayProfiles);
            }
        };
    }

    private static DetectParameters addBlockTariffParameter(final DetectParameters detectParameters, final int blockTariff) {
        detectParameters.put(GasDayProfilesHandler.Parameter.BLOCK_TARIFF.getName(), blockTariff);
        return detectParameters;
    }

    private static List<DayProfile> makeCorrectCosemData(final Object... params) {
        final byte[] midnight = new byte[] {0, 0, 0, 0};
        final byte[] logicalName = new byte[] {0, 0, 10, 0, (byte) 100, (byte) 255};
        final List<DayProfile> dayProfileList = new ArrayList<>();
        final int pair = 2;
        for (int i = 0; i < params.length / pair; ++i) {
            final DayProfile dayProfile = new DayProfile();
            final List<DayProfileAction> daySchedule = new ArrayList<>();
            final DayProfileAction dayProfileAction = new DayProfileAction();
            dayProfileAction.setStartTime(midnight);
            dayProfileAction.setScriptLogicalName(logicalName);
            dayProfileAction.setScriptSelector((int) params[i * pair + 1]);
            daySchedule.add(dayProfileAction);
            dayProfile.setDaySchedule(daySchedule);
            dayProfile.setDayId((short) params[i * pair]);
            dayProfileList.add(dayProfile);
        }
        return dayProfileList;
    }

    private static List<DayProfile> makeCosemData(final Object... params) {
        final List<DayProfile> dayProfileList = new ArrayList<>();
        final int quartet = 4;
        for (int i = 0; i < params.length / quartet; ++i) {
            final DayProfile dayProfile = new DayProfile();
            final List<DayProfileAction> daySchedule = new ArrayList<>();
            final DayProfileAction dayProfileAction = new DayProfileAction();
            dayProfileAction.setStartTime((byte[]) params[i * quartet + 2]);
            dayProfileAction.setScriptLogicalName((byte[]) params[i * quartet + 3]);
            dayProfileAction.setScriptSelector((int) params[i * quartet + 1]);
            daySchedule.add(dayProfileAction);
            dayProfile.setDaySchedule(daySchedule);
            dayProfile.setDayId((short) params[i * quartet]);
            dayProfileList.add(dayProfile);
        }
        return dayProfileList;
    }

}
