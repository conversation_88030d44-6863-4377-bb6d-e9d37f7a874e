package com.criticalsoftware.dco.detect.dlms.handlers.parameter.parser;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.function.BiPredicate;
import java.util.function.Function;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.w3c.dom.Document;

import com.criticalsoftware.dco.detect.dlms.businessentities.generated.FixedParameter;
import com.criticalsoftware.dco.detect.dlms.businessentities.generated.RuleParameter;
import com.criticalsoftware.dco.detect.dlms.handlers.parameter.RuleParameterParser;
import com.criticalsoftware.dco.detect.dlms.utils.asn.cosem.BooleanStringUtils;

/**
 * This is the Fixed parameter parser.
 */
public class FixedParameterParser implements RuleParameterParser {

    @Override
    public <T> T parseRuleParameter(final Document requestDocument,
                                    final RuleParameter ruleParameter,
                                    final Class<T> parameterDefinitionType) {
        final FixedParameter fixedParameter = (FixedParameter) ruleParameter;
        return fixedParameter.getValue() != null ? parameterDefinitionType.cast(
            Arrays.stream(FixedParser.values())
                .filter(p -> p.getTest().test(parameterDefinitionType, fixedParameter.getValue()))
                .findFirst()
                .orElse(FixedParser.OBJECT)
                .getFunction().apply(fixedParameter.getValue())) : null;
    }

    /**
     * This enum enumerates the supported parameters data types for FixedParam.
     */
    @Getter
    @AllArgsConstructor
    private enum FixedParser {
        BOOLEAN((clazz, value) -> Boolean.class.equals(clazz)
            && (value == null || BooleanStringUtils.testBooleanString(value)), Boolean::parseBoolean),
        BYTE((clazz, value) -> Byte.class.equals(clazz), Byte::parseByte),
        SHORT((clazz, value) -> Short.class.equals(clazz), Short::parseShort),
        INTEGER((clazz, value) -> Integer.class.equals(clazz), Integer::parseInt),
        LONG((clazz, value) -> Long.class.equals(clazz), Long::parseLong),
        FLOAT((clazz, value) -> Float.class.equals(clazz), Float::parseFloat),
        DOUBLE((clazz, value) -> Double.class.equals(clazz), Double::parseDouble),
        BIG_DECIMAL((clazz, value) -> BigDecimal.class.equals(clazz), BigDecimal::new),
        OBJECT((clazz, value) -> true, value -> value);

        private final BiPredicate<Class<?>, String> test;
        private final Function<String, Object> function;
    }

}
