package com.criticalsoftware.dco.detect.dlms.handlers.specification.numeric;

import java.math.BigDecimal;
import java.util.function.BiPredicate;
import java.util.function.Function;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.criticalsoftware.dco.detect.dlms.handlers.AbstractDetectHandler;
import com.criticalsoftware.dco.detect.dlms.handlers.DetectParameters;
import com.criticalsoftware.dco.detect.dlms.handlers.SupportedParameterDefinition;
import com.criticalsoftware.dco.detect.dlms.utils.NumericUtils;

/**
 * Abstract numeric handler centralizing the process of numeric handlers (integer, long, etc).
 *
 * @param <T> the customized Numeric class.
 */
public abstract class AbstractNumericDetectHandler<T extends Number> extends AbstractDetectHandler<T> {

    /**
     * This predicate tests the parameter COSEM Data.
     */
    private final BiPredicate<T, DetectParameters> valuePredicate = (cosemData, detectParameters) -> {
        final BigDecimal parameterValue = NumericUtils.applyMultiplierAndOrDivisor(
            detectParameters.getBigDecimalValue(Parameter.VALUE),
            detectParameters.getBigDecimalValue(Parameter.MULTIPLIER),
            detectParameters.getBigDecimalValue(Parameter.DIVISOR));
        return testFail(BigDecimal.ZERO.compareTo(BigDecimal.valueOf(parameterValue.doubleValue() % 1)) == 0
            && retrieveSpecificFunctionForCosemValue().apply(parameterValue).equals(cosemData), "T.value");
    };

    @Override
    protected boolean detect(final T cosemData, final DetectParameters detectParameters) {
        return cosemDataPredicate.test(cosemData) && valuePredicate.test(cosemData, detectParameters);
    }

    @Override
    public SupportedParameterDefinition[] getSupportedParameters() {
        return Parameter.values();
    }

    /**
     * Retrieves the function responsible for converting the BigDecimal, used in the operation, to the specific COSEM type.
     *
     * @return a function to be used by the specific COSEM type
     */
    protected abstract Function<BigDecimal, T> retrieveSpecificFunctionForCosemValue();

    /**
     * Enum defining the parameters of the {@link AbstractNumericDetectHandler}.
     */
    @Getter
    @AllArgsConstructor
    public enum Parameter implements SupportedParameterDefinition {
        MULTIPLIER("multiplier", BigDecimal.class, false),
        DIVISOR("divisor", BigDecimal.class, false),
        ROUND_METHOD("roundMethod", String.class, false),
        VALUE("value", BigDecimal.class, true);

        private final String name;
        private final Class<?> type;
        private final boolean required;
    }

}
