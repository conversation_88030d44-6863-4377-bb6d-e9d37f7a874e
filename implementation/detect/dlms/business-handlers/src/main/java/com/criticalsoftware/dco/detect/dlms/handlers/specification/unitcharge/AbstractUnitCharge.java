package com.criticalsoftware.dco.detect.dlms.handlers.specification.unitcharge;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.Predicate;

import uk.co.dccinterface.serviceusergateway.ElecBlocks;
import uk.co.dccinterface.serviceusergateway.ElecPriceElementsPrimary;
import uk.co.dccinterface.serviceusergateway.ElecPrimaryTOUPrice;
import uk.co.dccinterface.serviceusergateway.GasBlockPrice;
import uk.co.dccinterface.serviceusergateway.GasPriceElements;
import uk.co.dccinterface.serviceusergateway.GasTOUPrice;

import com.criticalsoftware.dco.detect.dlms.handlers.AbstractDetectHandler;
import com.criticalsoftware.smartmetering.codecs.cosem.types.ChargeTableElement;

/**
 * This class is responsible for centralizing the code common by UnitCharge handlers.
 *
 * @param <T> represents the generic COSEM type.
 */
public abstract class AbstractUnitCharge<T> extends AbstractDetectHandler<T> {

    private static final String UNIT_CHARGE_CHARGE_TABLE_ELEMENT_CHARGE_PER_UNIT = "UnitCharge.ChargeTableElement.chargePerUnit";

    /**
     * This predicate is responsible to verify that ElecPriceElementsPrimary JAXB element is not hybrid_type.
     */
    protected final Predicate<ElecPriceElementsPrimary> elecPriceElementsPrimaryPredicate = elecPriceElements ->
        testFail((elecPriceElements.isSetTOUTariff() && !elecPriceElements.isSetBlockTariff())
                || (!elecPriceElements.isSetTOUTariff() && elecPriceElements.isSetBlockTariff()),
            "ElecPriceElementsPrimary.BlockTariff && ElecPriceElementsPrimary.TOUTariff");

    /**
     * This predicate is responsible to validate the ElecPrimaryTOUPrice list from the parameter JAXB element.
     */
    protected final Predicate<List<ElecPrimaryTOUPrice>> elecTOUTariffPredicate = elecPrimaryTOUPrices ->
        testFail(elecPrimaryTOUPrices != null && !elecPrimaryTOUPrices.isEmpty(),
            "ElecPriceElementsPrimary.ElecTOUTariff.touPrice");

    /**
     * This predicate is responsible to validate the ElecBlocks list from the parameter JAXB element.
     */
    protected final Predicate<List<ElecBlocks>> elecBlockTariffPredicate = elecBlocks ->
        testFail(elecBlocks != null && !elecBlocks.isEmpty(),
            "ElecPriceElementsPrimary.ElecBlockTariff.blockPrices");

    /**
     * This predicate is responsible to verify that GasPriceElements JAXB element is not hybrid_type.
     */
    protected final Predicate<GasPriceElements> gasPriceElementsPredicate = gasPriceElements ->
        testFail((gasPriceElements.isSetTOUTariff() && !gasPriceElements.isSetBlockTariff())
                || (!gasPriceElements.isSetTOUTariff() && gasPriceElements.isSetBlockTariff()),
            "GasPriceElements.isSetTOUTariff || GasPriceElements.isSetBlockTariff");

    /**
     * This predicate is responsible to validate the GasTOUPrice list from the parameter JAXB element.
     */
    protected final Predicate<List<GasTOUPrice>> gasTOUTariffPredicate = gasTOUs ->
        testFail(gasTOUs != null && !gasTOUs.isEmpty(),
            "GasTOUPriceMatrix.touPrice");

    /**
     * This predicate is responsible to validate the GasBlockPrice list from the parameter JAXB element.
     */
    protected final Predicate<List<GasBlockPrice>> gasBlockTariffPredicate = gasBlocks ->
        testFail(gasBlocks != null && !gasBlocks.isEmpty(),
            "GasBlockPriceMatrix.blockPrices");

    /**
     * This predicate tests if COSEM unit_charge.charge_table_element.charge_per_unit is not null.
     */
    protected final Predicate<Number> chargePerUnitPredicate = chargePerUnit ->
        testFail(chargePerUnit != null, UNIT_CHARGE_CHARGE_TABLE_ELEMENT_CHARGE_PER_UNIT);

    /**
     * This predicate tests COSEM unit_charge.charge_table_element.charge_per_unit versus the DUIS price from JAXB element.
     */
    protected final BiPredicate<BigDecimal, BigDecimal> chargePerUnitMatchPredicate = (chargePerUnit, chargePerUnitParam) ->
        testFail(chargePerUnit.compareTo(chargePerUnitParam) <= 0,
            UNIT_CHARGE_CHARGE_TABLE_ELEMENT_CHARGE_PER_UNIT);

    /**
     * This predicate tests if the remaining prices that belong to COSEM unit_charge.charge_table are zero.
     */
    protected final Predicate<List<?>> compareOthersChargePerUnitPredicate = chargeTableElements ->
        testFail(chargeTableElements.stream().allMatch(
            e -> "0".equals(((ChargeTableElement<?>) e).getChargePerUnit().toString())),
            UNIT_CHARGE_CHARGE_TABLE_ELEMENT_CHARGE_PER_UNIT);

}
