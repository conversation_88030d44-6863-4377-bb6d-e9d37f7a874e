package com.criticalsoftware.dco.detect.dlms.handlers.specification.scripts;

import java.util.List;
import java.util.Optional;
import java.util.function.BiPredicate;
import java.util.function.IntPredicate;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Named;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;

import com.criticalsoftware.dco.detect.dlms.handlers.AbstractDetectHandler;
import com.criticalsoftware.dco.detect.dlms.handlers.DetectParameters;
import com.criticalsoftware.dco.detect.dlms.handlers.SupportedParameterDefinition;
import com.criticalsoftware.smartmetering.codecs.asn1.xdlms.types.XDLMSData;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.AbstractConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.ArrayConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.ScriptConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.smets1.converters.ArbitratorActionConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.smets1.types.ArbitratorAction;
import com.criticalsoftware.smartmetering.codecs.cosem.types.ActionSpecification;
import com.criticalsoftware.smartmetering.codecs.cosem.types.Script;

/**
 * Handler TrilliantSyntacticCorrelationOfTariffScriptHandler for the Script array type present in Trilliant SRV1.1.1.
 */
@Slf4j
@ApplicationScoped
@Named("TrilliantSyntacticCorrelationOfTariffScriptHandler")
public class TrilliantSyntacticCorrelationOfTariffScriptHandler extends AbstractDetectHandler<List<Script>> {

    private static final int HEXADECIMAL_RADIX = 16;

    private final IntPredicate scriptHasValidNumberOfActionsPredicate = size ->
        testFail(size == 1 || size == 2, "Script.actions");

    private final BiPredicate<Short, Short> serviceIdPredicate = (cosemIdentifier, idParameter) ->
        testFail(Short.compare(cosemIdentifier, idParameter) == 0, "ServiceID");

    private final BiPredicate<Short, DetectParameters> rateServiceIdPredicate = (cosemIdentifier, detectParameters) ->
        testFail(serviceIdPredicate.test(cosemIdentifier, detectParameters.getShortValue(Parameter.RATE_SERVICE_ID)), "ClassID");

    private final BiPredicate<Short, DetectParameters> disconnectServiceIdPredicate = (cosemIdentifier, detectParameters) ->
        testFail(serviceIdPredicate.test(
            cosemIdentifier, detectParameters.getShortValue(Parameter.DISCONNECT_SERVICE_ID)), "ServiceID");

    private final BiPredicate<Integer, Integer> classIdPredicate = (cosemClass, classParameter) ->
        testFail(Integer.compare(cosemClass, classParameter) == 0, "ClassID");

    private final BiPredicate<Integer, DetectParameters> rateClassIdPredicate = (cosemClass, detectParameters) ->
        testFail(classIdPredicate.test(cosemClass, detectParameters.getIntegerValue(Parameter.RATE_CLASS_ID)), "RateClassID");

    private final BiPredicate<Integer, DetectParameters> disconnectClassIdPredicate = (cosemClass, detectParameters) ->
        testFail(classIdPredicate.test(
            cosemClass, detectParameters.getIntegerValue(Parameter.DISCONNECT_CLASS_ID)), "DisconnectClassID");

    private final BiPredicate<byte[], String> obisCodePredicate = (cosemObject, obisCode) ->
        Hex.encodeHexString(cosemObject).equalsIgnoreCase(obisCode);

    private final BiPredicate<byte[], DetectParameters> rateObisCodePredicate = (cosemObject, detectParameters) -> {
        final String[] obisCodes = detectParameters.getStringValue(Parameter.RATE_OBIS_CODE_LIST).split(",");
        return testFail((obisCodePredicate.test(cosemObject, obisCodes[0])
            || obisCodePredicate.test(cosemObject, obisCodes[1])), "RateObisCodeList");
    };

    private final BiPredicate<byte[], DetectParameters> disconnectObisCodePredicate = (cosemObject, detectParameters) ->
        testFail(obisCodePredicate.test(
            cosemObject, detectParameters.getStringValue(Parameter.DISCONNECT_OBIS_CODE)), "DisconnectObisCode");

    private final BiPredicate<Byte, Byte> attrIdPredicate = (cosemMethod, attrAttribute) ->
        testFail(Byte.compare(cosemMethod, attrAttribute) == 0, "AttrID");

    private final BiPredicate<Byte, DetectParameters> rateAttrIdPredicate = (cosemObject, detectParameters) ->
        testFail(attrIdPredicate.test(cosemObject, detectParameters.getByteValue(Parameter.RATE_ATTR_ID)), "RateAttrID");

    private final BiPredicate<Byte, DetectParameters> disconnectAttrIdPredicate = (cosemObject, detectParameters) ->
        testFail(attrIdPredicate.test(cosemObject, detectParameters.getByteValue(Parameter.DISCONNECT_ATTR_ID)), "DisconnectAttrID");

    private final BiPredicate<Short, DetectParameters> requestActorPredicate = (requestActor, detectParameters) ->
        testFail(Short.compare(requestActor,
            detectParameters.getShortValue(Parameter.DISCONNECT_PARAMETER_ACTOR)) == 0, "DisconnectParameterActor");

    private final BiPredicate<Short, DetectParameters> requestActionPredicate = (requestAction, detectParameters) -> {
        final String[] requestActions = detectParameters.getStringValue(Parameter.DISCONNECT_PARAMETER_ACTION_LIST).split(",");
        return testFail(Short.compare(
            requestAction, Short.parseShort(requestActions[0], HEXADECIMAL_RADIX)) == 0 || Short.compare(requestAction,
            Short.parseShort(requestActions[1], HEXADECIMAL_RADIX)) == 0, "DisconnectParameterActionList");
    };

    private boolean parameterIsOctetString(final ActionSpecification rateAction) {
        final XDLMSData actionParameter = rateAction.getParameter();
        final boolean result = actionParameter.isOctetString();
        if (!result) {
            log.error("operation=processScriptList, message='Error converting handler specific XDLMS data.'");
        }
        return result;
    }

    private Optional<ArbitratorAction> getDisconnectedActionParameter(final ActionSpecification disconnectAction) {
        Optional<ArbitratorAction> arbitratorActionOptional = Optional.empty();
        final XDLMSData actionParameter = disconnectAction.getParameter();
        try {
            arbitratorActionOptional = Optional.ofNullable(new ArbitratorActionConverter().fromData(actionParameter));
        } catch (final Exception e) {
            log.error("operation=getDisconnectedActionParameter, message='Error converting handler specific XDLMS data.'");
            log.debug("operation=getDisconnectedActionParameter", e);
        }
        return arbitratorActionOptional;
    }

    protected boolean correlateScript(final List<Script> cosemData, final DetectParameters detectParameters) {
        return testFail(processScriptList(cosemData, detectParameters), "Scripts.size");
    }

    private boolean processScriptList(final List<Script> cosemData, final DetectParameters detectParameters) {
        boolean result = true;
        for (final Script script : cosemData) {
            if (!scriptHasValidNumberOfActionsPredicate.test(script.getActions().size())
                || !correlateActions(detectParameters, script)) {
                result = false;
                break;
            }
        }
        return result;
    }

    private boolean correlateActions(final DetectParameters detectParameters, final Script script) {
        boolean result = false;
        final ActionSpecification firstAction = script.getActions().get(0);
        if (script.getActions().size() == 1) {
            result = correlateRateAction(firstAction, detectParameters) || correlateDisconnectAction(firstAction, detectParameters);
        } else if (script.getActions().size() == 2) {
            final ActionSpecification secondAction = script.getActions().get(1);
            result = correlateRateAction(firstAction, detectParameters)
                && correlateDisconnectAction(secondAction, detectParameters);
        }
        return result;
    }

    private boolean correlateRateAction(final ActionSpecification rateAction, final DetectParameters detectParameters) {
        return testRatePredicates(rateAction, detectParameters) && parameterIsOctetString(rateAction);
    }

    private boolean testRatePredicates(final ActionSpecification rateAction, final DetectParameters detectParameters) {
        return rateServiceIdPredicate.test(rateAction.getServiceId(), detectParameters)
            && rateClassIdPredicate.test(rateAction.getClassId(), detectParameters)
            && rateObisCodePredicate.test(rateAction.getLogicalName(), detectParameters)
            && rateAttrIdPredicate.test(rateAction.getIndex(), detectParameters);
    }

    private boolean correlateDisconnectAction(final ActionSpecification disconnectAction, final DetectParameters detectParameters) {
        boolean result = disconnectServiceIdPredicate.test(disconnectAction.getServiceId(), detectParameters)
            && disconnectClassIdPredicate.test(disconnectAction.getClassId(), detectParameters)
            && disconnectObisCodePredicate.test(disconnectAction.getLogicalName(), detectParameters)
            && disconnectAttrIdPredicate.test(disconnectAction.getIndex(), detectParameters);
        if (result) {
            final Optional<ArbitratorAction> arbitratorActionOptional = getDisconnectedActionParameter(disconnectAction);
            result = arbitratorActionOptional.isPresent()
                && requestActorPredicate.test(arbitratorActionOptional.get().getRequestActor(), detectParameters)
                && requestActionPredicate.test(arbitratorActionOptional.get().getRequestAction(), detectParameters);
        }
        return result;
    }

    @Override
    public SupportedParameterDefinition[] getSupportedParameters() {
        return Parameter.values();
    }

    @Override
    protected AbstractConverter<List<Script>> getConverter() {
        return new ArrayConverter<>(new ScriptConverter());
    }

    @Override
    protected boolean detect(final List<Script> cosemData, final DetectParameters detectParameters) {
        return cosemDataPredicate.test(cosemData) && correlateScript(cosemData, detectParameters);
    }

    /**
     * Enum defining the parameters of the {@link TrilliantSyntacticCorrelationOfTariffScriptHandler}.
     */
    @Getter
    @AllArgsConstructor
    public enum Parameter implements SupportedParameterDefinition {
        RATE_SERVICE_ID("rateServiceID", Short.class, true),
        RATE_CLASS_ID("rateClassID", Integer.class, true),
        RATE_OBIS_CODE_LIST("rateObisCodeList", String.class, true),
        RATE_ATTR_ID("rateAttrID", Byte.class, true),
        DISCONNECT_SERVICE_ID("disconnectServiceID", Short.class, true),
        DISCONNECT_CLASS_ID("disconnectClassID", Integer.class, true),
        DISCONNECT_OBIS_CODE("disconnectObisCode", String.class, true),
        DISCONNECT_ATTR_ID("disconnectAttrID", Byte.class, true),
        DISCONNECT_PARAMETER_ACTOR("disconnectParameterActor", Short.class, true),
        DISCONNECT_PARAMETER_ACTION_LIST("disconnectParameterActionList", String.class, true);

        private final String name;
        private final Class<?> type;
        private final boolean required;
    }

}
