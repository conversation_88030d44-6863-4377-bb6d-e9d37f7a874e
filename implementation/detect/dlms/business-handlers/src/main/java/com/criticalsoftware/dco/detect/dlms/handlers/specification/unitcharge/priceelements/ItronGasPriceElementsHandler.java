package com.criticalsoftware.dco.detect.dlms.handlers.specification.unitcharge.priceelements;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Named;

import com.criticalsoftware.smartmetering.codecs.cosem.converters.AbstractConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.LongConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.UnitChargeConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.types.UnitCharge;

/**
 * Handler ItronGasPriceElementsHandler for UnitCharge COSEM type Long.
 */
@ApplicationScoped
@Named("ItronGasPriceElementsHandler")
public class ItronGasPriceElementsHandler extends AbstractGasPriceElementsHandler<Short> {

    @Override
    protected AbstractConverter<UnitCharge<Short>> getConverter() {
        return new UnitChargeConverter<>(LongConverter::new);
    }

}
