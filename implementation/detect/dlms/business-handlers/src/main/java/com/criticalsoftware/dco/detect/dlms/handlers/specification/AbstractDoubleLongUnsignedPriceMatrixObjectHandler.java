package com.criticalsoftware.dco.detect.dlms.handlers.specification;

import java.util.ArrayList;
import java.util.List;
import java.util.function.BiPredicate;
import java.util.function.Function;

import lombok.AllArgsConstructor;
import lombok.Getter;
import uk.co.dccinterface.serviceusergateway.GasPriceElements;

import com.criticalsoftware.dco.detect.dlms.handlers.AbstractDetectHandler;
import com.criticalsoftware.dco.detect.dlms.handlers.DetectParameters;
import com.criticalsoftware.dco.detect.dlms.handlers.SupportedParameterDefinition;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.AbstractConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.ArrayConverter;
import com.criticalsoftware.smartmetering.codecs.cosem.converters.DoubleLongUnsignedConverter;

/**
 * Handler DoubleLongUnsignedPriceMatrixObjectHandler for DoubleLongUnsigned COSEM type.
 *
 * @param <T> List of GasPrices
 */
public abstract class AbstractDoubleLongUnsignedPriceMatrixObjectHandler<T> extends AbstractDetectHandler<List<Long>> {

    /**
     * This predicate tests the parameter VALUE.
     */
    private final BiPredicate<List<Long>, DetectParameters> gasPriceElementsPredicate = (cosemValue, detectParameters) -> {

        final GasPriceElements gasPriceElements = (GasPriceElements) detectParameters.getObjectValue(Parameter.GAS_PRICE_ELEMENTS);
        final List<T> sortedGasPriceMatrix = sort(retrieveGasPriceMatrixFromDuis(gasPriceElements));

        boolean success = cosemValue.size() >= sortedGasPriceMatrix.size();

        for (int i = 0; i < cosemValue.size() && success; i++) {
            if (i < sortedGasPriceMatrix.size()) {
                success &= isSuccess(cosemValue, sortedGasPriceMatrix, i);
            } else if (cosemValue.get(i) != 0) {
                success = false;
            }
            if (!success) {
                break;
            }
        }

        return testFail(success, "gasPriceElements");
    };

    private boolean isSuccess(final List<Long> cosemValue, final List<T> sortedGasPriceMatrix, final int i) {
        boolean isSuccess = true;
        if (sortedGasPriceMatrix.get(i) == null) {
            if (cosemValue.get(i) != 0) {
                isSuccess = false;
            }
        } else {
            if ((!cosemValue.get(i).equals(getValue().apply(sortedGasPriceMatrix.get(i))))) {
                isSuccess = false;
            }
        }
        return isSuccess;
    }

    private List<T> sort(final List<T> gasPriceMatrix) {
        final List<T> toReturn = new ArrayList<>();

        for (final T priceMatrix : gasPriceMatrix) {
            while (getIndex().apply(priceMatrix) >= toReturn.size() + 1) {
                toReturn.add(null);
            }
            toReturn.set(getIndex().apply(priceMatrix) - 1, priceMatrix);
        }
        return toReturn;
    }

    @Override
    protected AbstractConverter<List<Long>> getConverter() {
        return new ArrayConverter<>(new DoubleLongUnsignedConverter());
    }

    @Override
    protected boolean detect(final List<Long> cosemData, final DetectParameters detectParameters) {
        return detectParameters.isEmpty() || (
            cosemDataPredicate.test(cosemData) && gasPriceElementsPredicate.test(cosemData, detectParameters));
    }

    @Override
    public SupportedParameterDefinition[] getSupportedParameters() {
        return Parameter.values();
    }

    protected abstract List<T> retrieveGasPriceMatrixFromDuis(GasPriceElements gasPriceElements);

    protected abstract Function<T, Integer> getIndex();

    protected abstract Function<T, Long> getValue();

    /**
     * Enum defining the parameters of the {@link AbstractDoubleLongUnsignedPriceMatrixObjectHandler}.
     */
    @Getter
    @AllArgsConstructor
    public enum Parameter implements SupportedParameterDefinition {

        GAS_PRICE_ELEMENTS("gasPriceElements", GasPriceElements.class, true);

        private final String name;
        private final Class<?> type;
        private final boolean required;
    }

}
