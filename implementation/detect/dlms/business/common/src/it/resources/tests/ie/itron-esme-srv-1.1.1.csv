PARTIAL_DETECT_CONFIGURATION_FILE          ,CONFIGURATIONS_PARTIAL,FULL_DETECT_CONFIGURATION_FILE ,CONFIGURATIONS_FULL,SRV  ,DUIS                                                                                               ,REQUEST_TYPE,CLASS_ID,INSTANCE_ID     ,ATTRIBUTE_METHOD_ID,PAY<PERSON>OA<PERSON>                                                                                       ,SUCCE<PERSON>,FAIL_MESSAGE                                                                                                                                                                           ,TEST_DESCRIPTION

# This is the test suit for Itron srv 1.1.1

#block future success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-01.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-41.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-42.hex                           ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-02.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-05.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (a) dlmsPrice*10^dlmsScale < anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis-2.xml                                 ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-44.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (a) dlmsPrice*10^dlmsScale == anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis-2.xml                                 ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-43.hex                           ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:19.2.0.255 and attributeMethodId 6.",Test SRV 1.1.1 fail (a) dlmsPrice*10^dlmsScale > anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-09.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-10.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-11.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-12.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-13.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-14.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-15.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-16.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-17.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-18.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-19.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-20.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-24.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-25.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-26.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-27.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-28.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-29.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-30.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-31.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-32.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-33.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-34.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-35.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-36.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-37.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-38.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-39.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-3-actions-duis.xml                         ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-40.hex                           ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 future dated blocks ItronElecTariffAndAuxRelayScriptTableHandler

#block present success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-01.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-40.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-41.hex                          ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-02.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,ACTION      ,113     ,0-0:**********  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-04.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-05.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (b) dlmsPrice*10^dlmsScale < anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis-2.xml                                ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-43.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (b) dlmsPrice*10^dlmsScale == anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis-2.xml                                ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-42.hex                          ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:19.2.0.255 and attributeMethodId 6.",Test SRV 1.1.1 fail (b) dlmsPrice*10^dlmsScale > anomaly block price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,ACTION      ,113     ,0-0:19.2.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-07.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-09.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-10.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-11.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-12.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-13.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-14.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-15.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-16.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-17.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-18.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-19.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-20.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,ACTION      ,20      ,0-0:**********  ,1                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-22.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-23.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-24.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-25.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-26.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-27.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-28.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-29.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-30.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-31.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-32.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-33.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-34.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-35.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-36.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-37.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-38.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-3-actions-duis.xml                        ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-39.hex                          ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 present dated blocks ItronElecTariffAndAuxRelayScriptTableHandler

#tou future success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-01.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-43.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-44.hex                             ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-02.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-05.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (c) dlmsPrice*10^dlmsScale < anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis-2.xml                                   ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-46.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (c) dlmsPrice*10^dlmsScale == anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis-2.xml                                   ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-45.hex                             ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:19.2.0.255 and attributeMethodId 6.",Test SRV 1.1.1 fail (c) dlmsPrice*10^dlmsScale > anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-09.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-10.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-11.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-12.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-13.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-14.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-15.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-16.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-17.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-18.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-19.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-20.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-24.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-25.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-26.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-27.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-28.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-29.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-30.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-31.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-32.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-33.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-34.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-35.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-36.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-37.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-38.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-39.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-duis.xml                                     ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-40.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 future dated blocks ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-1-action-duis.xml                            ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-41.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 future dated blocks ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-2-actions-duis.xml                           ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-42.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 future dated blocks ItronElecTariffAndAuxRelayScriptTableHandler

#tou present success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-01.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-49.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-48.hex                            ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-02.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,ACTION      ,113     ,0-0:**********  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-04.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-05.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (d) dlmsPrice*10^dlmsScale < anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis-2.xml                                  ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-51.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (d) dlmsPrice*10^dlmsScale == anomaly tou price limit
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis-2.xml                                  ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-50.hex                            ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:19.2.0.255 and attributeMethodId 6.",Test SRV 1.1.1 fail (d) dlmsPrice*10^dlmsScale > anomaly tou price limit 1
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,ACTION      ,113     ,0-0:19.2.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-07.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-09.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-10.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-11.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-12.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-13.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-14.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-15.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-16.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-17.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-18.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-19.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-20.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,ACTION      ,20      ,0-0:**********  ,1                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-22.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-23.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-24.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-25.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-26.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-27.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-28.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-29.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-30.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-31.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-32.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-33.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-34.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-35.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-36.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-37.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-38.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-duis.xml                                    ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-39.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-1-action-duis.xml                           ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-40.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-2-actions-duis.xml                          ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-41.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-2-actions-2-duis.xml                        ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-42.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-3-actions-duis.xml                                  ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-43.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-2-actions-same-price-duis.xml               ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-44.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-2-actions-duplicated-duis.xml               ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-45.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-1-dayprofile-2-actions.xml                  ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-46.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-1-action-duis.xml                           ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-47.hex                            ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 9, instanceId 0-0:************ and attributeMethodId 2.",Test FAIL SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler 1 Switch with only 1 action

detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-non-specified-day-of-month-duis.xml,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-success-seasons-non-specified-day-of-month-dlms-01.hex     ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Non specified Day of month SeasonProfilesElecSeasonProfilesHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-last-day-of-month-duis.xml         ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-success-seasons-last-day-of-month-dlms-01.hex              ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Last Day of month SeasonProfilesElecSeasonProfilesHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-second-last-day-of-month-duis.xml  ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-success-seasons-second-last-day-of-month-dlms-01.hex       ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Second Last Day of month SeasonProfilesElecSeasonProfilesHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-non-specified-day-of-month-duis.xml,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-success-special-days-non-specified-day-of-month-dlms-01.hex,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Non specified Day of month SpecDaysElecSpecialDaysHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-last-day-of-month-duis.xml         ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-success-special-days-last-day-of-month-dlms-01.hex         ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Last Day of month SpecDaysElecSpecialDaysHandler
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-success-seasons-special-days-second-last-day-of-month-duis.xml  ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-success-special-days-second-last-day-of-month-dlms-01.hex  ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success Second Last Day of month SpecDaysElecSpecialDaysHandler

# Whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-01.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-41.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-42.hex                           ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-02.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-05.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (a) dlmsPrice*10^dlmsScale < anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-09.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-10.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-11.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-12.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-13.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-14.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-15.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-16.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-17.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-18.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-19.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-20.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-24.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-25.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-26.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-27.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-28.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-29.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-30.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-31.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-32.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-33.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-34.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-35.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-36.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-37.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-38.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-future-success-whitespaces-duis.xml                                   ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-future-success-dlms-39.hex                           ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces

detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-01.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-40.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-41.hex                          ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-02.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,ACTION      ,113     ,0-0:**********  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-04.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-05.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (b) dlmsPrice*10^dlmsScale < anomaly block price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,ACTION      ,113     ,0-0:19.2.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-07.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-09.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-10.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-11.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-12.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-13.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-14.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-15.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-16.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-17.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-18.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-19.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-20.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,ACTION      ,20      ,0-0:**********  ,1                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-22.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-23.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-24.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-25.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-26.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-27.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-28.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-29.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-30.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-31.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-32.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-33.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-34.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-35.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-36.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-37.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-block-present-success-whitespaces-duis.xml                                  ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-block-present-success-dlms-38.hex                          ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces

detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-01.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-43.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-44.hex                             ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-02.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-05.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (c) dlmsPrice*10^dlmsScale < anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-47.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-10.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-11.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-12.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-13.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-14.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-15.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-16.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-17.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-18.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-19.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-20.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-24.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-25.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-26.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-27.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-28.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-29.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-30.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-31.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-32.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-33.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-34.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-35.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-36.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-37.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-38.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-39.hex                             ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-whitespaces-duis.xml                                     ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-future-success-dlms-40.hex                             ,true   ,		                                                                                                                                                                                     ,Test SRV 1.1.1 future dated blocks ItronElecTariffAndAuxRelayScriptTableHandler with whitespaces

detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-01.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale == anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-49.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success dlmsPrice*10^dlmsScale < anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,113     ,0-0:**********  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-48.hex                            ,false  ,"The detect operation failed for deviceModel detect-configuration-itron-esme, service variant 1.1.1, request type set, classId 113, instanceId 0-0:********** and attributeMethodId 6.",Test SRV 1.1.1 fail dlmsPrice*10^dlmsScale > anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,113     ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-02.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,ACTION      ,113     ,0-0:**********  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-04.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,113     ,0-0:19.2.0.255  ,6                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-05.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success (d) dlmsPrice*10^dlmsScale < anomaly tou price limit with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,ACTION      ,113     ,0-0:19.2.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-07.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,20      ,0-0:**********  ,7                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-52.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,20      ,0-0:**********  ,8                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-10.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,20      ,0-0:**********  ,9                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-11.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,11      ,0-0:11.0.0.255  ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-12.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-13.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-14.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-15.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-16.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-17.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-18.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-19.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-20.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,ACTION      ,20      ,0-0:**********  ,1                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-22.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-23.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-24.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-25.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.14.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-26.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.15.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-27.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.16.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-28.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.17.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-29.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.18.255 ,3                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-30.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.11.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-31.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.12.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-32.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.13.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-33.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.14.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-34.hex                            ,true   ,	                                                                                                                                                                                      ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.15.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-35.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.16.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-36.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.17.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-37.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,21      ,0-0:16.1.18.255 ,4                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-38.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 success with whitespaces
detect-configuration-itron-esme            ,ie-partial            ,detect-configuration-itron-esme,ie-full            ,1.1.1,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-whitespaces-duis.xml                                    ,SET         ,9       ,0-0:************,2                  ,detect-ie-full-itron-esme-srv1.1.1-tou-present-success-dlms-39.hex                            ,true   ,                                                                                                                                                                                       ,Test SRV 1.1.1 present tou ItronElecTariffAndAuxRelayScriptTableHandler with whitespaces
