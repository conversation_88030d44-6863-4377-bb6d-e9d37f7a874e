<s1sp:S1SPRequest xmlns:s1sp="http://www.dccinterface.co.uk/S1SP" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0" xsi:schemaLocation="http://www.dccinterface.co.uk/S1SP"><s1sp:SMETS1Header><s1sp:BusinessOriginator>90-B3-D5-1F-30-01-00-00</s1sp:BusinessOriginator></s1sp:SMETS1Header><sr:Request xmlns:sr="http://www.dccinterface.co.uk/ServiceUserGateway" schemaVersion="3.0">
  <sr:Header>
    <sr:RequestID>90-B3-D5-1F-30-01-00-00:00-00-00-00-00-00-00-0A:1000</sr:RequestID>
    <sr:CommandVariant>4</sr:CommandVariant>
    <sr:ServiceReference>2.1</sr:ServiceReference>
    <sr:ServiceReferenceVariant>2.1</sr:ServiceReferenceVariant>
  </sr:Header>

  <sr:Body>
    <sr:UpdatePrepayConfiguration>
      <sr:UpdatePrepayConfigElectricity>
        <sr:DebtRecoveryRateCap>4321</sr:DebtRecoveryRateCap>
        <sr:EmergencyCreditLimit>3333</sr:EmergencyCreditLimit>
        <sr:EmergencyCreditThreshold>4444</sr:EmergencyCreditThreshold>
        <sr:LowCreditThreshold>2222</sr:LowCreditThreshold>
        <sr:ElectricityNonDisablementCalendar>
          <sr:ElectricitySpecialDays>
            <sr:SpecialDay index="1">
              <sr:Date>
                <sr:Year>
                  <sr:NonSpecifiedYear/>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>01</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>01</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
            <sr:SpecialDay index="2">
              <sr:Date>
                <sr:Year>
                  <sr:NonSpecifiedYear/>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>12</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>25</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
          </sr:ElectricitySpecialDays>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>1</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>2</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
            </sr:SpecialDaysApplicability>
            <sr:DaysOfWeekApplicability/>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>00:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-28Z</sr:StartDate>
              <sr:EndDate>2018-10-28Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>1</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>2</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
            </sr:SpecialDaysApplicability>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>18:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-28Z</sr:StartDate>
              <sr:EndDate>2019-03-31Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>STOP</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>18:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-28Z</sr:StartDate>
              <sr:EndDate>2019-03-31Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
        </sr:ElectricityNonDisablementCalendar>
        <sr:MaxMeterBalance>5000</sr:MaxMeterBalance>
        <sr:MaxCreditThreshold>1000</sr:MaxCreditThreshold>
      </sr:UpdatePrepayConfigElectricity>
    </sr:UpdatePrepayConfiguration>
  </sr:Body>

  <ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/><ds:Reference URI=""><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>TKTd0XsoTT/WhlEsNohyCd1pGL/DDATGXW5EhbrfE4A=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue>z4mPvM8qqnasQ2p/+e94SVC+A2X4EhRvjrKKD/WwqY9S+ycAWaSWuSKVESvalm4btoBUTFEWw+al
    eoVhW/gPNg==</ds:SignatureValue><ds:KeyInfo><ds:X509Data><ds:X509IssuerSerial><ds:X509IssuerName>CN=Z1,OU=07</ds:X509IssuerName><ds:X509SerialNumber>105986833131214866166891566273223584671</ds:X509SerialNumber></ds:X509IssuerSerial></ds:X509Data></ds:KeyInfo></ds:Signature></sr:Request><ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/><ds:Reference URI=""><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>7FoecMGtyAcuZwxMrA30mUjPlc6NIhV4TayAc7XD8XM=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue>VzypOZi8tYLHG5xQSgljL6sRj9CqCxxy259OKQ42mNPPX+KX9RYYBS4yBrj1dOuxyI11s2XozpoJ
  PrZgIUu4Cw==</ds:SignatureValue><ds:KeyInfo><ds:X509Data><ds:X509IssuerSerial><ds:X509IssuerName>CN=Z1,OU=07</ds:X509IssuerName><ds:X509SerialNumber>***************************************</ds:X509SerialNumber></ds:X509IssuerSerial></ds:X509Data></ds:KeyInfo></ds:Signature></s1sp:S1SPRequest>
