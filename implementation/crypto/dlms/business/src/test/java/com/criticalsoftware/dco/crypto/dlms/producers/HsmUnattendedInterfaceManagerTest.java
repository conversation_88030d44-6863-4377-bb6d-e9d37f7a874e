package com.criticalsoftware.dco.crypto.dlms.producers;

import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.stream.Stream;

import jakarta.enterprise.inject.Instance;
import jakarta.enterprise.inject.spi.CDI;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import com.criticalsoftware.dco.crypto.dlms.business.DlmsCryptoMockHsmImpl;
import com.criticalsoftware.dco.crypto.dlms.businessapi.enums.HsmMode;
import com.criticalsoftware.dco.crypto.dlms.businessapi.exceptions.CryptographyRuntimeException;
import com.criticalsoftware.dco.hsm.HSMUnattendedInterface;
import com.criticalsoftware.dco.hsm.HSMUnattendedInterfaceImpl;

class HsmUnattendedInterfaceManagerTest {

    @Mock
    HSMUnattendedInterfaceImpl hsmUnattendedInterfaceImpl;

    @Mock
    Instance<HSMUnattendedInterfaceImpl> hsmUnattendedInterfaceInstance;

    @Mock
    Instance<DlmsCryptoMockHsmImpl> dlmsCryptoMockHsmInstance;

    @Mock
    DlmsCryptoMockHsmImpl dlmsCryptoMockHsmImpl;

    @Mock
    CDI<HSMUnattendedInterface> dlmsCryptoMockHsmCDI;

    private MockedStatic<CDI> cdi;

    HsmUnattendedInterfaceManager hsmUnattendedInterfaceManager;

    @BeforeEach
    void setUp() {
        cdi = mockStatic(CDI.class);
        MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    void tearDown() {
        cdi.close();
    }

    static Stream<Arguments> hsmModesMethodSource() {
        return Stream.of(
            arguments(HsmMode.HARD),
            arguments(HsmMode.MOCK),
            arguments(HsmMode.SOFT)
        );
    }

    @ParameterizedTest
    @MethodSource("hsmModesMethodSource")
    void createHsmUnattendedInterfaceOrThrowExceptionBasedOnHsmMode(final HsmMode hsmMode) {
        hsmUnattendedInterfaceManager = new HsmUnattendedInterfaceManager(hsmMode, 1000);
        if (HsmMode.HARD.equals(hsmMode)) {
            createMocks(hsmMode);
            final HSMUnattendedInterface instance = hsmUnattendedInterfaceManager.createHsmUnattendedInterface();
            assertInstanceOf(HSMUnattendedInterfaceImpl.class, instance);
        } else if (HsmMode.MOCK.equals(hsmMode)) {
            createMocks(hsmMode);
            final HSMUnattendedInterface instance = hsmUnattendedInterfaceManager.createHsmUnattendedInterface();
            assertInstanceOf(DlmsCryptoMockHsmImpl.class, instance);
        } else {
            assertThrows(CryptographyRuntimeException.class, () -> hsmUnattendedInterfaceManager.createHsmUnattendedInterface());
        }
    }

    @ParameterizedTest
    @MethodSource("hsmModesMethodSource")
    void testInitForDifferentHsmMode(final HsmMode hsmMode) {
        hsmUnattendedInterfaceManager = new HsmUnattendedInterfaceManager(hsmMode, 0);
        createMocks(hsmMode);
        hsmUnattendedInterfaceManager.init();
        if (HsmMode.HARD.equals(hsmMode)) {
            verify(hsmUnattendedInterfaceImpl, times(1)).getVersions();
        } else {
            verify(hsmUnattendedInterfaceInstance, never()).get();
        }
    }

    private void createMocks(final HsmMode hsmMode) {
        cdi.when(CDI::current).thenReturn(dlmsCryptoMockHsmCDI);
        if (HsmMode.MOCK.equals(hsmMode)) {
            when(dlmsCryptoMockHsmCDI.select(DlmsCryptoMockHsmImpl.class)).thenReturn(dlmsCryptoMockHsmInstance);
            when(dlmsCryptoMockHsmInstance.get()).thenReturn(dlmsCryptoMockHsmImpl);
        } else if (HsmMode.HARD.equals(hsmMode)) {
            when(dlmsCryptoMockHsmCDI.select(HSMUnattendedInterfaceImpl.class)).thenReturn(hsmUnattendedInterfaceInstance);
            when(hsmUnattendedInterfaceInstance.get()).thenReturn(hsmUnattendedInterfaceImpl);
        }
    }

}