package com.criticalsoftware.dco.crypto.dlms.business;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.params.provider.Arguments.arguments;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import java.security.Key;
import java.util.List;
import java.util.stream.Stream;

import jakarta.enterprise.inject.Instance;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.w3._2000._09.xmldsig_.SignatureType;

import com.criticalsoftware.dco.core.common.exceptions.ValidationException;
import com.criticalsoftware.dco.core.common.exceptions.XmlOperationsException;
import com.criticalsoftware.dco.crypto.dlms.businessapi.builders.AuthenticationBuilder;
import com.criticalsoftware.dco.crypto.dlms.businessapi.builders.DlmsKeyPackageBuilder;
import com.criticalsoftware.dco.crypto.dlms.businessapi.builders.EncryptionBuilder;
import com.criticalsoftware.dco.crypto.dlms.businessapi.business.DlmsCryptoProvider;
import com.criticalsoftware.dco.crypto.dlms.businessapi.business.KeyPackageService;
import com.criticalsoftware.dco.crypto.dlms.businessapi.businessentities.DlmsKeyPackageWithContext;
import com.criticalsoftware.dco.crypto.dlms.businessapi.businessentities.EncryptedKeyWithCryptoContext;
import com.criticalsoftware.dco.crypto.dlms.businessapi.businessentities.KeyPackageAuthentication;
import com.criticalsoftware.dco.crypto.dlms.businessapi.businessentities.KeyPackageEncryption;
import com.criticalsoftware.dco.crypto.dlms.businessentities.generated.Authentication;
import com.criticalsoftware.dco.crypto.dlms.businessentities.generated.DLMSKeyPackage;
import com.criticalsoftware.dco.crypto.dlms.businessentities.generated.Encryption;
import com.criticalsoftware.dco.crypto.dlms.common.enums.AuthenticationMechanism;
import com.criticalsoftware.dco.crypto.dlms.common.enums.DlmsClient;
import com.criticalsoftware.dco.crypto.dlms.common.enums.EncryptionMechanism;
import com.criticalsoftware.dco.crypto.dlms.common.enums.KeyType;

class KeyPackageOperationsImplTest {

    private static final Integer SIGNING_KEY_ID = 3;

    private static final String KEY_SIGNATURE_MECHANISM = "ECC";

    private static final String KEY_ENCRYPTION_MECHANISM = "AES";

    @Mock
    private Instance<DlmsCryptoProvider> dlmsCryptoProviderInstance;

    @Mock
    private DlmsCryptoProvider dlmsCryptoProvider;

    @Mock
    private KeyPackageService keyPackageService;

    @InjectMocks
    private KeyPackageOperationsImpl victim;

    private AutoCloseable autoCloseable;

    @BeforeEach
    void setUp() {
        autoCloseable = openMocks(this);
        when(dlmsCryptoProviderInstance.get()).thenReturn(dlmsCryptoProvider);
    }

    @AfterEach
    void tearDown() throws Exception {
        autoCloseable.close();
    }

    static Stream<Arguments> validateSigningKeyIsNotEnrolmentOnlyMethodSource() {
        final DLMSKeyPackage keyPackageSignedEnrolmentOnlyTrue =
            DlmsKeyPackageBuilder.builder()
                .keyType(KeyType.GUEK)
                .encryption(EncryptionBuilder.builder()
                    .encryptionKeyId(2)
                    .mechanism(EncryptionMechanism.AES)
                    .build())
                .authentication(AuthenticationBuilder.builder()
                    .authenticationKeyId(SIGNING_KEY_ID)
                    .mechanism(KEY_SIGNATURE_MECHANISM)
                    .build()
                ).build();

        return Stream.of(
            arguments(keyPackageSignedEnrolmentOnlyTrue)
        );
    }

    @MethodSource("validateSigningKeyIsNotEnrolmentOnlyMethodSource")
    @ParameterizedTest
    void validateSigningKeyIsNotEnrolmentOnlyFailureTest(final DLMSKeyPackage keyPackage) throws Exception {
        when(dlmsCryptoProvider.keyAuthenticationKeyIsForEnrolmentOnly(keyPackage)).thenReturn(true);
        assertThrows(ValidationException.class, () -> victim.validateSigningKeyIsNotEnrolmentOnly(keyPackage));
    }

    static Stream<Arguments> keyPackageGenericMethodSourceProviderErrorMethodSource() {
        final SignatureType signature = new SignatureType();
        return Stream.of(
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.NONE, 1, null, null),
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.NONE, 1, null, signature),
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.NONE, null, 1, null),
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.NONE, 1, 1, null),
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.ECC, 1, null, signature),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.NONE, null, 1, null),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.ECC, null, null, signature),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.ECC, null, 321, signature),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.NONE, 123, null, signature),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.ECC, 123, null, signature)
        );
    }

    @MethodSource("keyPackageGenericMethodSourceProviderErrorMethodSource")
    @ParameterizedTest
    void keyPackageGenericValidationTestError(final KeyType keyType,
                                              final EncryptionMechanism encryptionMechanism,
                                              final AuthenticationMechanism authenticationMechanism,
                                              final Integer encryptKeyId,
                                              final Integer authKeyId,
                                              final SignatureType signature) {
        assertThrows(ValidationException.class,
            () -> keyPackageGenericValidationTest(keyType, encryptionMechanism, authenticationMechanism, encryptKeyId, authKeyId,
                signature));
    }

    static Stream<Arguments> keyPackageGenericValidationMethodSourceSuccessMethodSource() {
        final SignatureType signature = new SignatureType();
        return Stream.of(
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.NONE, null, null, null),
            arguments(KeyType.MK, EncryptionMechanism.NONE, AuthenticationMechanism.ECC, null, 1, signature),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.NONE, 2, null, null),
            arguments(KeyType.MK, EncryptionMechanism.AES, AuthenticationMechanism.ECC, 123, 321, signature)
        );
    }

    @MethodSource("keyPackageGenericValidationMethodSourceSuccessMethodSource")
    @ParameterizedTest
    void keyPackageGenericValidationTestSuccess(final KeyType keytype,
                                                final EncryptionMechanism encryptionMechanism,
                                                final AuthenticationMechanism authenticationMechanism,
                                                final Integer encryptKeyId,
                                                final Integer authKeyId,
                                                final SignatureType signature) {
        keyPackageGenericValidationTest(keytype, encryptionMechanism, authenticationMechanism, encryptKeyId, authKeyId, signature);
    }


    private void keyPackageGenericValidationTest(final KeyType keytype,
                                                 final EncryptionMechanism encryptionMechanism,
                                                 final AuthenticationMechanism authenticationMechanism,
                                                 final Integer encryptKeyId,
                                                 final Integer authKeyId,
                                                 final SignatureType signature) {
        final DLMSKeyPackage keyPackage = DlmsKeyPackageBuilder.builder()
            .authentication(
                AuthenticationBuilder.builder()
                    .mechanism(authenticationMechanism)
                    .authenticationKeyId(authKeyId)
                    .build())
            .encryption(
                EncryptionBuilder.builder()
                    .mechanism(encryptionMechanism)
                    .encryptionKeyId(encryptKeyId)
                    .build())
            .keyType(keytype)
            .build();
        keyPackage.setSignature(signature);
        victim.keyPackageGenericValidation(keyPackage);
    }

    @Test
    void getEncryptedKeyWithCryptoContextReturnsCorrectContext() {
        final byte[] keyData = "keyData".getBytes();

        final Encryption encryption = new Encryption();
        encryption.setMechanism(KEY_ENCRYPTION_MECHANISM);
        encryption.setEncryptionKeyId(1);
        encryption.setKey(keyData);

        final Authentication authentication = new Authentication();
        authentication.setAuthenticationKeyId(2);
        authentication.setMechanism(KEY_SIGNATURE_MECHANISM);

        final DLMSKeyPackage dlmsKeyPackage = new DLMSKeyPackage();
        dlmsKeyPackage.setEncryption(encryption);
        dlmsKeyPackage.setKeyType(KeyType.GUEK.name());
        dlmsKeyPackage.setAuthentication(authentication);

        final DlmsKeyPackageWithContext dlmsKeyPackageWithContext = new DlmsKeyPackageWithContext(dlmsKeyPackage, null, null);

        final KeyPackageEncryption keyPackageEncryption = new KeyPackageEncryption(KEY_ENCRYPTION_MECHANISM, 1);
        final KeyPackageAuthentication keyPackageAuthentication = new KeyPackageAuthentication(KEY_SIGNATURE_MECHANISM, 2);

        final EncryptedKeyWithCryptoContext encryptedKeyWithCryptoContext = new EncryptedKeyWithCryptoContext(
            keyData,
            KeyType.GUEK.name(),
            keyPackageEncryption,
            keyPackageAuthentication
        );

        final EncryptedKeyWithCryptoContext result = victim.getEncryptedKeyWithCryptoContext(dlmsKeyPackageWithContext);
        assertEquals(encryptedKeyWithCryptoContext, result);
    }

    static Stream<Arguments> provideValidDLMSClientStringsMethodSource() {
        return Stream.of(
            arguments("0x20", DlmsClient.DATA_COLLECTION),
            arguments("0x30", DlmsClient.EXTENDED_DATA_COLLECTION),
            arguments("0x40", DlmsClient.MANAGEMENT),
            arguments("0x50", DlmsClient.FIRMWARE),
            arguments("0x01", DlmsClient.TRILLIANT),
            arguments("", DlmsClient.NONE)
        );
    }

    @ParameterizedTest
    @MethodSource("provideValidDLMSClientStringsMethodSource")
    void getDlmsClientFromStringReturnsCorrectDlmsClient(final String dlmsClientStr, final DlmsClient expectedDlmsClient) {
        final DlmsClient result = victim.getDlmsClientFromString(dlmsClientStr);
        assertEquals(expectedDlmsClient, result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"invalid", "123"})
    void getDlmsClientFromStringThrowsValidationExceptionForInvalidStrings(final String dlmsClientStr) {
        assertThrows(ValidationException.class, () -> victim.getDlmsClientFromString(dlmsClientStr));
    }

    @Test
    void validateKeyTypeIsApplicableContainsValidKeyType() {
        final DlmsKeyPackageWithContext keyPackageWContext = mock(DlmsKeyPackageWithContext.class);
        when(keyPackageWContext.getMeterKeyType()).thenReturn(KeyType.MK);
        final List<KeyType> validMeterKeyTypes = List.of(KeyType.MK, KeyType.AK);

        assertDoesNotThrow(() -> victim.validateKeyTypeIsApplicable(keyPackageWContext, validMeterKeyTypes));
        verify(keyPackageWContext).getMeterKeyType();
    }

    @Test
    void validateKeyTypeIsApplicableThrowsValidationExceptionForInvalidKeyType() {
        final DlmsKeyPackageWithContext keyPackageWContext = mock(DlmsKeyPackageWithContext.class);
        when(keyPackageWContext.getMeterKeyType()).thenReturn(KeyType.GUEK);
        final List<KeyType> validMeterKeyTypes = List.of(KeyType.MK, KeyType.AK);

        assertThrows(ValidationException.class, () -> victim.validateKeyTypeIsApplicable(keyPackageWContext, validMeterKeyTypes));
    }

    @Test
    void validateKeyPackageSignatureWithAuthenticationMechanismNone() throws Exception {
        final String packageXml = "<packageXml>";
        final Integer publicSignatureKeyId = 1;
        final Key publicSignatureKey = mock(Key.class);

        victim.validateKeyPackageSignature(packageXml, AuthenticationMechanism.NONE.name(), publicSignatureKeyId);
        verify(dlmsCryptoProvider, times(0)).getPublicSignatureKey(publicSignatureKeyId);
        verify(keyPackageService, times(0)).validateSignature(packageXml, publicSignatureKey);
    }

    @Test
    void validateKeyPackageSignatureWithAuthenticationMechanismDifferentOfNone() throws Exception {
        final String packageXml = "<packageXml>";
        final Integer publicSignatureKeyId = 1;
        final Key publicSignatureKey = mock(Key.class);

        when(dlmsCryptoProvider.getPublicSignatureKey(publicSignatureKeyId)).thenReturn(publicSignatureKey);
        victim.validateKeyPackageSignature(packageXml, AuthenticationMechanism.RSA.name(), publicSignatureKeyId);
        verify(dlmsCryptoProvider).getPublicSignatureKey(publicSignatureKeyId);
        verify(keyPackageService).validateSignature(packageXml, publicSignatureKey);
    }

    @Test
    void getMeterKeyTypeFromStringReturnsCorrectKeyType() {
        final String meterKeyTypeStr = "MK";
        final KeyType result = victim.getMeterKeyTypeFromString(meterKeyTypeStr);
        assertEquals(KeyType.MK, result);
    }

    @ValueSource(strings = {"invalid"})
    @NullSource
    @ParameterizedTest
    void getMeterKeyTypeFromStringThrowsValidationExceptionForInvalidMeterKeyType(final String meterKeyTypeStr) {
        assertThrows(ValidationException.class, () -> victim.getMeterKeyTypeFromString(meterKeyTypeStr));
    }

    @Test
    void extractKeyPackageReturnsDlmsKeyPackageWithContext() throws XmlOperationsException {
        final String keyPackageXml = "<keyPackageXml>";
        final DLMSKeyPackage keyPackage = mock(DLMSKeyPackage.class);
        when(keyPackageService.getKeyPackage(keyPackageXml)).thenReturn(keyPackage);
        when(keyPackage.getKeyType()).thenReturn("MK");
        when(keyPackage.getDLMSClient()).thenReturn("0x20");

        final DlmsKeyPackageWithContext result = victim.extractKeyPackage(keyPackageXml);

        verify(keyPackageService).getKeyPackage(keyPackageXml);
        assertNotNull(result);
        assertEquals(keyPackage, result.getKeyPackage());
        assertEquals(KeyType.MK, result.getMeterKeyType());
        assertEquals(DlmsClient.DATA_COLLECTION, result.getDlmsClient());
    }

}