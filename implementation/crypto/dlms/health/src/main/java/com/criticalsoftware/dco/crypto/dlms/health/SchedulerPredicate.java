package com.criticalsoftware.dco.crypto.dlms.health;

import io.quarkus.scheduler.Scheduled;
import io.quarkus.scheduler.ScheduledExecution;
import jakarta.inject.Singleton;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.criticalsoftware.dco.crypto.dlms.businessapi.enums.CryptoConfigKey;

/**
 * Class used to skip CryptoSigningChecker scheduler execution.
 */
@Singleton
public class SchedulerPredicate implements Scheduled.SkipPredicate {

    @ConfigProperty(name = CryptoConfigKey.SIGNING_HEALTH_CHECK_SCHEDULER_ENABLED, defaultValue = "true")
    boolean isScheduledEnable;

    @Override
    public boolean test(final ScheduledExecution execution) {
        return !isScheduledEnable;
    }

}
