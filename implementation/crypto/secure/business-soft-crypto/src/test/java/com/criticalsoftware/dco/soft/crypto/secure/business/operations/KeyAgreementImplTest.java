package com.criticalsoftware.dco.soft.crypto.secure.business.operations;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import java.math.BigInteger;
import java.security.AlgorithmParameters;
import java.security.InvalidAlgorithmParameterException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.ECParameterSpec;
import java.security.spec.ECPrivateKeySpec;

import jakarta.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.criticalsoftware.dco.core.common.utils.UncompressedKeyUtils;
import com.criticalsoftware.dco.crypto.secure.businessapi.exceptions.CryptographySecureRuntimeException;

/**
 * Validates the implementation of the KeyAgreementImpl with JCE.
 */
@Slf4j
class KeyAgreementImplTest {

    private static final String CURVE = "secp256r1";
    private static final String KEY_ALG = "EC";
    private static final String PRIVATE_KEY = "6E5C1FC1F845611D18BE661879049AE5DA344974A7CC165B0F0576AED62DFFC5";
    private static final String PUBLIC_KEY_X = "43BAA66C95F5ED9876D14EAA4ECBA44F54A0D90111161BA81646472722247469";
    private static final String PUBLIC_KEY_Y = "167094FCFAA2F9FA4CC69A9DD92B7BE3FCC0499223A0AAB62648ED2DC838CCB8";
    private static final String EXPECTED_SECURE_SHARED_SECRET = "5B0CB5A65CF59F0FB800FEE4CC8239198FF9B62523BBA608F4BAF81F903FEACC";
    private KeyAgreementImpl keyAg;

    @BeforeEach
    void init() {
        keyAg = new KeyAgreementImpl();
    }

    /**
     * Tests that calculating a shared secret between private Key A and public Key B, has the same result has calculating a shared secret
     * between private Key B and public Key A.
     */
    @Test
    void testSharedKeyIsEqual()
        throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
        final KeyPairGenerator kpg = KeyPairGenerator.getInstance(KEY_ALG);
        kpg.initialize(new ECGenParameterSpec(CURVE));
        final KeyPair myKeys = kpg.generateKeyPair();
        final KeyPair otherKeys = kpg.generateKeyPair();

        final byte[] bob = keyAg.createSharedSecret(myKeys.getPrivate(), otherKeys.getPublic());
        final byte[] alice = keyAg.createSharedSecret(otherKeys.getPrivate(), myKeys.getPublic());

        final String hexBob = DatatypeConverter.printHexBinary(bob);
        final String hexAlice = DatatypeConverter.printHexBinary(alice);

        assertEquals(hexBob, hexAlice);

    }

    /**
     * Tests that {@link CryptographySecureRuntimeException} is thrown when using the wrong key
     */
    @Test
    void testSharedKeyIsEqualCreateSharedKeyWrongKeyFail()
        throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
        final KeyPairGenerator kpg = KeyPairGenerator.getInstance(KEY_ALG);
        kpg.initialize(new ECGenParameterSpec(CURVE));
        final PrivateKey myPrivateKey = kpg.generateKeyPair().getPrivate();
        final PrivateKey otherPrivateKey = kpg.generateKeyPair().getPrivate();

        assertThrows(CryptographySecureRuntimeException.class, () -> keyAg.createSharedSecret(myPrivateKey, otherPrivateKey));

    }


    /**
     * Tests that the key agreement is the one expected by Secure with validated data by them.
     *
     * @throws Exception if KA fails.
     */
    @Test
    void testWithSecureVerifiedValues() throws Exception {

        log.info("Validating ECDH Key Agreement with test vectors validated by Secure. Test Shared Secret = {}",
            EXPECTED_SECURE_SHARED_SECRET);

        final byte[] privateKeyBytes = DatatypeConverter.parseHexBinary(PRIVATE_KEY);
        final byte[] publicKeyBytes = DatatypeConverter.parseHexBinary("04" + PUBLIC_KEY_X + PUBLIC_KEY_Y);

        final AlgorithmParameters parameters = AlgorithmParameters.getInstance(KEY_ALG);
        parameters.init(new ECGenParameterSpec(CURVE));
        final ECParameterSpec ecParameterSpec = parameters.getParameterSpec(ECParameterSpec.class);
        final ECPrivateKeySpec privateKeySpec = new ECPrivateKeySpec(new BigInteger(privateKeyBytes), ecParameterSpec);

        final KeyFactory kf = KeyFactory.getInstance(KEY_ALG);
        final ECPrivateKey privateKey = (ECPrivateKey) kf.generatePrivate(privateKeySpec);
        final ECPublicKey publicKey = UncompressedKeyUtils.uncompressedToKeyObject(publicKeyBytes, "Public key");

        final byte[] sharedSecret = keyAg.createSharedSecret(privateKey, publicKey);

        assertEquals(EXPECTED_SECURE_SHARED_SECRET, DatatypeConverter.printHexBinary(sharedSecret));

        log.info("ECDH Key Agreement = {} is as expected", DatatypeConverter.printHexBinary(sharedSecret));
    }

}