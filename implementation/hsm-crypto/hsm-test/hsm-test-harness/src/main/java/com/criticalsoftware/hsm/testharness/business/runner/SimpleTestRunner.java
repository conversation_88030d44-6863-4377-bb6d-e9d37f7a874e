package com.criticalsoftware.hsm.testharness.business.runner;

import java.util.List;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.Getter;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.criticalsoftware.hsm.testharness.business.enums.TestRunnerConfigKey;
import com.criticalsoftware.hsm.testharness.business.metrics.SimpleTestMetrics;
import com.criticalsoftware.hsm.testharness.core.entities.common.TestResultObject;

/**
 * Class that runs the simple tests.
 */
@ApplicationScoped
public class SimpleTestRunner extends AbstractTestRunner {

    @Getter
    private final int threadPoolSize;

    private final SimpleTestMetrics simpleTestMetrics;

    /**
     * Constructor for SimpleTestRunner.
     *
     * @param threadPoolSize    the size of the thread pool, injected from configuration
     * @param simpleTestMetrics the metrics for the simple tests
     */
    public SimpleTestRunner(@ConfigProperty(name = TestRunnerConfigKey.TEST_SIMPLE_THREADS, defaultValue = "1") final int threadPoolSize,
                            final SimpleTestMetrics simpleTestMetrics) {
        this.threadPoolSize = threadPoolSize;
        this.simpleTestMetrics = simpleTestMetrics;
    }

    @Override
    protected void reportTestMetrics(final List<TestResultObject> results) {
        simpleTestMetrics.reportTestRateMetrics(results);
    }

}

