package com.criticalsoftware.hsm.testharness.core.entities.common;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * Common Test Parameters Validator Class.
 */
public class DurationXorExecutionsParameterValidator implements ConstraintValidator<ValidDurationXorExecutions, CommonTestParameters> {

    @Override
    public boolean isValid(final CommonTestParameters commonTestParameters, final ConstraintValidatorContext constraintValidatorContext) {
        return (commonTestParameters.getTestDuration() == null) != (commonTestParameters.getNumberOfExecutions() == null);
    }

}
