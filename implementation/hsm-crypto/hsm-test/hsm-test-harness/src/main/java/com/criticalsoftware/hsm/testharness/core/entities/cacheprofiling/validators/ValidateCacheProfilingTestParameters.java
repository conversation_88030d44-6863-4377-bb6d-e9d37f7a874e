package com.criticalsoftware.hsm.testharness.core.entities.cacheprofiling.validators;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

/**
 * Test Parameter Validator Interface.
 */
@Target({ElementType.TYPE, ElementType.FIELD, ElementType.PARAMETER, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {CacheProfilingValidator.class})
@Documented
public @interface ValidateCacheProfilingTestParameters {

    /**
     * Get error message.
     *
     * @return error message.
     */
    String message() default "Invalid test execution parameters";

    /**
     * Attribute groups that allows the specification of validation groups.
     *
     * @return attribute groups.
     */
    Class<?>[] groups() default {};

    /**
     * Attribute payload that can be used by clients of the Bean Validation API to assign custom payload objects to a constraint.
     *
     * @return attribute payload.
     */
    Class<? extends Payload>[] payload() default {};

}