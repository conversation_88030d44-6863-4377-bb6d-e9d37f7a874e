package com.criticalsoftware.dco.hsm.connection.chooser;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import java.util.stream.Stream;

import jakarta.enterprise.inject.Instance;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.criticalsoftware.dco.hsm.configs.HsmConfig;
import com.criticalsoftware.dco.hsm.connection.chooser.random.RandomConnectionChooserService;
import com.criticalsoftware.dco.hsm.connection.chooser.simple.AKSimpleConnectionChooserService;
import com.criticalsoftware.dco.hsm.exceptions.HSMRuntimeException;

class ConnectionChooserProducerTest {

    private AutoCloseable autoCloseable;

    @Mock
    private HsmConfig hsmConfig;

    @Mock
    private Instance<ConnectionChooserService> connectionChooserServices;

    @InjectMocks
    private ConnectionChooserProducer victim;

    @BeforeEach
    void setUp() {
        autoCloseable = openMocks(this);
    }

    @AfterEach
    void tearDown() throws Exception {
        autoCloseable.close();
    }

    static Stream<Arguments> getValidConnectionChooserMethodSource() {
        return Stream.of(
            Arguments.of(mock(RandomConnectionChooserService.class), "random"),
            Arguments.of(mock(AKSimpleConnectionChooserService.class), "ak")
        );
    }

    @MethodSource("getValidConnectionChooserMethodSource")
    @ParameterizedTest
    void Should_GetConnectionChooser_When_ConnectionChooserIsValid(final ConnectionChooserService connectionChooserServiceMock,
                                                                   final String param) {
        when(hsmConfig.connectionChooser()).thenReturn(param);
        final Instance mockInstance = mock(Instance.class);
        when(mockInstance.get()).thenReturn(connectionChooserServiceMock);
        when(connectionChooserServices.select(connectionChooserServiceMock.getClass())).thenReturn(mockInstance);

        final ConnectionChooserService connectionChooserService = victim.getConnectionChooserService();

        assertSame(connectionChooserService, connectionChooserServiceMock);
    }

    @Test
    void Should_ThrowException_When_ConnectionChooserIsInvalid() {
        when(hsmConfig.connectionChooser()).thenReturn("unknown");
        assertThrows(HSMRuntimeException.class, victim::getConnectionChooserService);
    }

}