package com.criticalsoftware.dco.hsm.dtos.see.utils;

import com.ncipher.nfast.marshall.M_ByteBlock;
import com.ncipher.nfast.marshall.M_Ticket;
import com.ncipher.nfast.marshall.MarshallContext;
import com.ncipher.nfast.marshall.MarshallTypeError;
import com.ncipher.nfast.marshall.Marshallable;
import com.ncipher.nfast.marshall.UnmarshallContext;

import com.criticalsoftware.dco.hsm.exceptions.HSMRuntimeException;

/**
 * Utility methods for marshal/unmarshal operations.
 */
public final class MarshalUtils {

    private MarshalUtils() {
    }

    /**
     * Marshals the provided marshallable object. This method must not be used in case the marshallable object is a ticket. For tickets, the
     * {@link MarshalUtils#marshalTicket} method shall be used.
     *
     * @param marshallable the object to marshal.
     *
     * @return the marshalled payload.
     */
    public static byte[] marshal(final Marshallable marshallable) {
        try {
            return MarshallContext.marshall(marshallable, true);
        } catch (final MarshallTypeError e) {
            throw HSMRuntimeException.createMarshalException(e);
        }
    }

    /**
     * Marshals the provided ticket.
     *
     * @param ticket the ticket to marshal.
     *
     * @return the marshalled payload.
     */
    public static byte[] marshalTicket(final M_Ticket ticket) {
        try {
            return MarshallContext.marshall(new M_ByteBlock(marshal(ticket)), false);
        } catch (final MarshallTypeError e) {
            throw HSMRuntimeException.createMarshalException(e);
        }
    }

    /**
     * Asserts the unmarshal operation is done and there are no left bytes to unmarshal.
     *
     * @param unmarshallContext the unmarshal context.
     * @param payload           the unmarshalled payload.
     */
    public static void assertUnmarshalDone(final UnmarshallContext unmarshallContext, final byte[] payload) {
        if (unmarshallContext.getPos() < payload.length) {
            throw HSMRuntimeException.createUnmarshalIncompleteException(unmarshallContext.getPos(), payload.length);
        }
    }

}