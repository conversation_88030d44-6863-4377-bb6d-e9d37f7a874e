<?xml version="1.0" encoding="UTF-8" standalone="no"?><DLMSDetectConfiguration xmlns="http://www.dccinterface.co.uk/DLMSDetectConfiguration" author="CSW" creationDate="2018-12-12" deviceModel="E4700000000000000000" s1sp="TRILLIANT" schemaVersion="1.0" version="1.0.0">
  <ServiceRequests>
    <ServiceRequest serviceReferenceVariant="1.1.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="10" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.11.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=1]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.12.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=2]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.13.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=3]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.14.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=4]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.15.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=5]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.16.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=6]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.17.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=7]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="21" instanceId="0-0:16.1.18.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantBlockThresholdsElecThresholdsHandler">
              <Parameters>
                <DUISParameter name="thresholds">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='ElecTariffElements']/*[local-name()='ThresholdMatrix']/*[local-name()='Thresholds'][@index=8]
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-0:19.2.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantElecPriceElementsHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="beginTOU">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="beginBlock">
                  <Value>48</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='PriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="electricityPriceElements">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>TariffTOUPriceMatrixElec</Value>
                  <Value>TariffBlockPriceMatrixElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.0.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-0:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingChargeScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingCharge']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>StandingChargeElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="EnumHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>1</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingChargeScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateImportTariffPrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingCharge']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>StandingChargeElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>86400</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="9000" instanceId="0-0:63.1.1.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="9000" instanceId="0-0:63.1.1.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantTariffInformationHandler">
              <Parameters>
                <FixedParameter name="priceMatrixChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="standingChargeChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="calendarChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="blockConfigurationChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="blockThresholdsChange">
                  <Value>111111111</Value>
                </FixedParameter>
                <FixedParameter name="rateLabelChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="specialDaysChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="currencyChange">
                  <Value>false</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="9500" instanceId="0-0:13.1.0.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>1</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="9500" instanceId="0-0:13.1.0.255" requestType="set">
          <Rules>
            <Rule handler="ScalerUnitHandler">
              <Parameters>
                <FixedParameter name="scaler">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="unit">
                  <Value>4</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="9500" instanceId="0-0:13.1.0.255" requestType="set">
          <Rules>
            <Rule handler="EnumHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>2</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="10" classId="9500" instanceId="0-0:13.1.0.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="11" instanceId="0-1:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="9" instanceId="0-0:************" requestType="set">
          <Rules>
            <Rule handler="TrilliantSyntacticCorrelationOfTariffScriptHandler">
              <Parameters>
                <FixedParameter name="rateServiceID">
                  <Value>1</Value>
                </FixedParameter>
                <FixedParameter name="rateClassID">
                  <Value>6</Value>
                </FixedParameter>
                <FixedParameter name="rateObisCodeList">
                  <Value>00000E0001FF,00010E0001FF</Value>
                </FixedParameter>
                <FixedParameter name="rateAttrID">
                  <Value>4</Value>
                </FixedParameter>
                <FixedParameter name="disconnectServiceID">
                  <Value>2</Value>
                </FixedParameter>
                <FixedParameter name="disconnectClassID">
                  <Value>68</Value>
                </FixedParameter>
                <FixedParameter name="disconnectObisCode">
                  <Value>0000320100FF</Value>
                </FixedParameter>
                <FixedParameter name="disconnectAttrID">
                  <Value>1</Value>
                </FixedParameter>
                <FixedParameter name="disconnectParameterActor">
                  <Value>4</Value>
                </FixedParameter>
                <FixedParameter name="disconnectParameterActionList">
                  <Value>10,00</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="1.2.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-0:19.2.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantElecPriceElementsHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="beginTOU">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="beginBlock">
                  <Value>48</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='PriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="electricityPriceElements">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>TariffTOUPriceMatrixElec</Value>
                  <Value>TariffBlockPriceMatrixElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.0.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-0:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingChargeScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingCharge']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>StandingChargeElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="EnumHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>1</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0100010800FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingChargeScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePricePrimaryElement']/*[local-name()='PriceElements']/*[local-name()='ElectricityPriceElements']/*[local-name()='StandingCharge']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>StandingChargeElec</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" instanceId="0-1:19.2.4.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>86400</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="9000" instanceId="0-0:63.1.1.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="9000" instanceId="0-0:63.1.1.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantTariffInformationHandler">
              <Parameters>
                <FixedParameter name="priceMatrixChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="standingChargeChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="calendarChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="blockConfigurationChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="blockThresholdsChange">
                  <Value>000000000</Value>
                </FixedParameter>
                <FixedParameter name="rateLabelChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="specialDaysChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="currencyChange">
                  <Value>false</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="1.5">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="9" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='ResetMeterBalance'])" instanceId="0-0:10.0.64.255" requestType="action">
          <Rules>
            <Rule handler="LongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>2</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="9" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='ResetMeterBalance'])" instanceId="0-0:10.0.64.255" requestType="action">
          <Rules>
            <Rule handler="LongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>2</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="112" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='AdjustMeterBalance'])" instanceId="0-0:19.10.0.255" requestType="action">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='CreditMode']/*[local-name()='AdjustMeterBalance']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="112" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='AdjustMeterBalance'])" instanceId="0-0:19.10.0.255" requestType="action">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateMeterBalance']/*[local-name()='PrepaymentMode']/*[local-name()='AdjustMeterBalance']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="1.6">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Credit'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="AccountModeAndStatusHandler">
              <Parameters>
                <FixedParameter name="paymentMode">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="accountStatus">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="AccountModeAndStatusHandler">
              <Parameters>
                <FixedParameter name="paymentMode">
                  <Value>1</Value>
                </FixedParameter>
                <FixedParameter name="accountStatus">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="112" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment'])" instanceId="0-0:19.10.0.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='DisablementThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="10" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="ChargeConfigurationsHandler">
              <Parameters>
                <FixedXMLParameter name="configurations">
                  <ChargeConfigurations>
                    <ChargeConfiguration>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <ChargeReference>0000130203FF</ChargeReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                    </ChargeConfiguration>
                  </ChargeConfigurations>
                </FixedXMLParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="11" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtDisabled'][normalize-space()='false' or normalize-space()='0']) and boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtEmergency'][normalize-space()='true' or normalize-space()='1'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="CreditChargeConfigurationsHandler">
              <Parameters>
                <FixedXMLParameter name="configurations">
                  <CreditChargeConfigurations>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A02FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                  </CreditChargeConfigurations>
                </FixedXMLParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="11" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtDisabled'][normalize-space()='true' or normalize-space()='1']) and boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtEmergency'][normalize-space()='false' or normalize-space()='0'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="CreditChargeConfigurationsHandler">
              <Parameters>
                <FixedXMLParameter name="configurations">
                  <CreditChargeConfigurations>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>011</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>011</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0001130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A02FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                  </CreditChargeConfigurations>
                </FixedXMLParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="11" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtDisabled'][normalize-space()='true' or normalize-space()='1']) and boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtEmergency'][normalize-space()='true' or normalize-space()='1'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="CreditChargeConfigurationsHandler">
              <Parameters>
                <FixedXMLParameter name="configurations">
                  <CreditChargeConfigurations>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>011</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>011</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A02FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                  </CreditChargeConfigurations>
                </FixedXMLParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="11" classId="111" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtDisabled'][normalize-space()='false' or normalize-space()='0']) and boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePaymentMode']/*[local-name()='Prepayment']/*[local-name()='SuspendDebtEmergency'][normalize-space()='false' or normalize-space()='0'])" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="CreditChargeConfigurationsHandler">
              <Parameters>
                <FixedXMLParameter name="configurations">
                  <CreditChargeConfigurations>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A00FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A01FF</CreditReference>
                      <ChargeReference>0001130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130204FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130201FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A03FF</CreditReference>
                      <ChargeReference>0000130202FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>0000130A02FF</CreditReference>
                      <ChargeReference>0000130200FF</ChargeReference>
                      <CollectionConfiguration>111</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                    <CreditChargeConfiguration>
                      <CreditReference>000000000000</CreditReference>
                      <ChargeReference>000000000000</ChargeReference>
                      <CollectionConfiguration>000</CollectionConfiguration>
                    </CreditChargeConfiguration>
                  </CreditChargeConfigurations>
                </FixedXMLParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="13" classId="111" instanceId="0-1:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="2.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="10" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="18" classId="111" instanceId="0-0:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="LongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='DebtRecoveryRateCap']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="19" classId="111" instanceId="0-0:19.0.0.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>604800</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="5" classId="112" instanceId="0-0:19.10.0.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='LowCreditThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="112" instanceId="0-0:19.10.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='EmergencyCreditLimit']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="112" instanceId="0-0:19.10.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='EmergencyCreditThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="112" instanceId="0-1:19.10.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='EmergencyCreditLimit']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="112" instanceId="0-1:19.10.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdatePrepayConfiguration']/*[local-name()='UpdatePrepayConfigElectricity']/*[local-name()='EmergencyCreditThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="20" instanceId="0-0:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="11" instanceId="0-1:**********" requestType="set">
          <Rules>
            <!-- NMI -->
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="9" instanceId="0-0:************" requestType="set">
          <Rules>
            <Rule handler="TrilliantSyntacticCorrelationOfTariffScriptHandler">
              <Parameters>
                <FixedParameter name="rateServiceID">
                  <Value>1</Value>
                </FixedParameter>
                <FixedParameter name="rateClassID">
                  <Value>6</Value>
                </FixedParameter>
                <FixedParameter name="rateObisCodeList">
                  <Value>00000E0001FF,00010E0001FF</Value>
                </FixedParameter>
                <FixedParameter name="rateAttrID">
                  <Value>4</Value>
                </FixedParameter>
                <FixedParameter name="disconnectServiceID">
                  <Value>2</Value>
                </FixedParameter>
                <FixedParameter name="disconnectClassID">
                  <Value>68</Value>
                </FixedParameter>
                <FixedParameter name="disconnectObisCode">
                  <Value>0000320100FF</Value>
                </FixedParameter>
                <FixedParameter name="disconnectAttrID">
                  <Value>1</Value>
                </FixedParameter>
                <FixedParameter name="disconnectParameterActor">
                  <Value>4</Value>
                </FixedParameter>
                <FixedParameter name="disconnectParameterActionList">
                  <Value>10,00</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="9000" instanceId="0-0:63.1.1.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="9000" instanceId="0-0:63.1.1.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantTariffInformationHandler">
              <Parameters>
                <FixedParameter name="priceMatrixChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="standingChargeChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="calendarChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="blockConfigurationChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="blockThresholdsChange">
                  <Value>000000000</Value>
                </FixedParameter>
                <FixedParameter name="rateLabelChange">
                  <Value>false</Value>
                </FixedParameter>
                <FixedParameter name="specialDaysChange">
                  <Value>true</Value>
                </FixedParameter>
                <FixedParameter name="currencyChange">
                  <Value>false</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="2.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="115" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Header'][*[local-name()='CommandVariant']!=1])" instanceId="0-0:30.0.0.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="115" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Header'][*[local-name()='CommandVariant']=1])" instanceId="0-0:30.0.0.255" requestType="action">
          <Rules>
            <Rule handler="OctetStringUTRNHandler">
              <Parameters>
                <DUISParameter name="commandVariant">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Header']/*[local-name()='CommandVariant']
                  </Value>
                </DUISParameter>
                <DUISParameter name="utrn">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='TopUpDevice']/*[local-name()='UTRN']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="2.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="113" instanceId="0-0:19.2.1.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="113" instanceId="0-0:19.2.1.255" requestType="action">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='TimeDebtRegister1']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRatePeriod']='DAILY')" instanceId="0-0:19.2.1.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0000130201FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRatePriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRate']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>DebtRecoveryRates1ElecDaily</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRatePeriod']='HOURLY')" instanceId="0-0:19.2.1.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0000130201FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRatePriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1']/*[local-name()='DebtRecoveryRate']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>DebtRecoveryRates1ElecHourly</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.1.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1'][*[local-name()='DebtRecoveryRatePeriod']='DAILY'])" instanceId="0-0:19.2.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>86400</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery1'][*[local-name()='DebtRecoveryRatePeriod']='HOURLY'])" instanceId="0-0:19.2.1.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>3600</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="113" instanceId="0-0:19.2.2.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="113" instanceId="0-0:19.2.2.255" requestType="action">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='TimeDebtRegister2']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRatePeriod']='DAILY')" instanceId="0-0:19.2.2.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0000130202FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRatePriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRate']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>DebtRecoveryRates2ElecDaily</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="6" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRatePeriod']='HOURLY')" instanceId="0-0:19.2.2.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantDoubleLongUnitChargeSingleChargeHandler">
              <Parameters>
                <FixedParameter name="commodityScale">
                  <Value>0</Value>
                </FixedParameter>
                <FixedParameter name="commodityReference">
                  <Value>0000130202FF</Value>
                </FixedParameter>
                <FixedParameter name="index">
                  <Value>3030</Value>
                </FixedParameter>
                <DUISParameter name="priceScale">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRatePriceScale']
                  </Value>
                </DUISParameter>
                <DUISParameter name="chargePerUnit">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2']/*[local-name()='DebtRecoveryRate']
                  </Value>
                </DUISParameter>
                <AttributeAnomalyDetectionParameter name="anomalyMaxPrice">
                  <Value>DebtRecoveryRates2ElecHourly</Value>
                </AttributeAnomalyDetectionParameter>
                <FixedParameter name="anomalyScale">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="7" classId="113" instanceId="0-0:19.2.2.255" requestType="set">
          <Rules>
            <Rule handler="NoFutureDatedHandler">
              <Parameters>
                <FixedParameter name="farFuture">
                  <Value>100</Value>
                </FixedParameter>
                <FixedParameter name="nearPresent">
                  <Value>60</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2'][*[local-name()='DebtRecoveryRatePeriod']='DAILY'])" instanceId="0-0:19.2.2.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>86400</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="113" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='ElecDebtRecovery2'][*[local-name()='DebtRecoveryRatePeriod']='HOURLY'])" instanceId="0-0:19.2.2.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>3600</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="113" instanceId="0-0:19.2.3.255" requestType="action">
          <Rules>
            <Rule handler="DoubleLongHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='PaymentDebtRegister']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="13" classId="113" instanceId="0-0:19.2.3.255" requestType="set">
          <Rules>
            <Rule handler="LongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDebt']/*[local-name()='DebtRecoveryPerPayment']
                  </Value>
                </DUISParameter>
                <FixedParameter name="divisor">
                  <Value>100</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="2.5">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="3" classId="112" instanceId="0-0:19.10.1.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="3.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="8" classId="114" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="OctetStringDateTimeHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='RestrictAccessForChangeOfTenancy']/*[local-name()='RestrictionDateTime']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="9" classId="114" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="ActionScriptListHandler">
              <Parameters>
                <FixedParameter name="script">
                  <Value>00000A0040FF</Value>
                </FixedParameter>
                <FixedParameter name="scriptNumber">
                  <Value>6</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="11" classId="114" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="LongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>65535</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="14" classId="114" instanceId="0-0:**********" requestType="set">
          <Rules>
            <Rule handler="OctetStringDateTimeHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='RestrictAccessForChangeOfTenancy']/*[local-name()='RestrictionDateTime']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="3.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="7" instanceId="0-0:99.98.0.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="7" instanceId="0-0:99.98.4.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="7" instanceId="0-0:99.98.6.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="7" instanceId="0-0:99.98.11.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.1.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.1.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.1.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.4.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.4.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.4.4">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.4.5">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.6.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.8.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.8.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.8.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.10">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.11.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.13">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.15">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.16">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="4.18">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.2.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.2.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.2.4">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.2.5">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.2.9">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.4.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="71" instanceId="0-0:17.0.0.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationLoadLimitingGeneralSettings']/*[local-name()='LoadLimitPowerThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.4.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="3" instanceId="0-0:94.44.42.255" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.5">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="2" classId="3" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='SinglePhaseVoltageSettings']/*[local-name()='AverageRMSVoltageMeasurementPeriod'] &lt;=86400)" instanceId="1-0:0.8.2.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedRoundHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='SinglePhaseVoltageSettings']/*[local-name()='AverageRMSVoltageMeasurementPeriod']
                  </Value>
                </DUISParameter>
                <FixedParameter name="roundMethod">
                  <Value>ceil</Value>
                </FixedParameter>
                <FixedParameter name="divisor">
                  <Value>10</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='SinglePhaseVoltageSettings']/*[local-name()='AverageRMSVoltageMeasurementPeriod'] &gt;86400)" instanceId="1-0:0.8.2.255" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>86400</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.31.0.0" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSVoltageSagThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.31.0.1" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='SinglePhaseVoltageSettings']/*[local-name()='AverageRMSUnderVoltageThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.31.0.2" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSExtremeUnderVoltageThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.35.0.0" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSVoltageSwellThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.35.0.1" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='SinglePhaseVoltageSettings']/*[local-name()='AverageRMSOverVoltageThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.35.0.2" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSExtremeOverVoltageThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.43.0.0" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSVoltageSagMeasurementPeriod']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.43.0.2" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSExtremeUnderVoltageMeasurementPeriod']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.44.0.0" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSVoltageSwellMeasurementPeriod']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="1-0:12.44.0.2" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationVoltage']/*[local-name()='RMSVoltageSettings']/*[local-name()='RMSExtremeOverVoltageMeasurementPeriod']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.8">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Daily'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>Daily</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Weekly'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>Weekly</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
                <DUISParameter name="startDay">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Weekly']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Monthly'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>Monthly</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
                <DUISParameter name="startDay">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Monthly']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Quarterly'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>Quarterly</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
                <DUISParameter name="startDay">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Quarterly']/*[local-name()='DayOfMonth']
                  </Value>
                </DUISParameter>
                <DUISParameter name="startMonth">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Quarterly']/*[local-name()='BillingPeriodStartMonth']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='SixMonthly'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>SixMonthly</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
                <DUISParameter name="startDay">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='SixMonthly']/*[local-name()='DayOfMonth']
                  </Value>
                </DUISParameter>
                <DUISParameter name="startMonth">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='SixMonthly']/*[local-name()='BillingPeriodStartMonth']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="4" classId="22" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Yearly'])" instanceId="0-0:15.0.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantExecutionTimeDatesElecBillingCalendarHandler">
              <Parameters>
                <FixedParameter name="periodicity">
                  <Value>Yearly</Value>
                </FixedParameter>
                <FixedParameter name="billingTime">
                  <Value>00:00:00.00Z</Value>
                </FixedParameter>
                <DUISParameter name="startDay">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Yearly']/*[local-name()='DayOfMonth']
                  </Value>
                </DUISParameter>
                <DUISParameter name="startMonth">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationBillingCalendar']/*[local-name()='ElectricityBillingCalendar']/*[local-name()='Yearly']/*[local-name()='BillingPeriodStartMonth']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.11">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.12">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="0-0:93.44.36.1" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationInstantaneousPowerThreshold']/*[local-name()='LowMediumPowerThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="3" instanceId="0-0:93.44.36.10" requestType="set">
          <Rules>
            <Rule handler="DoubleLongUnsignedHandler">
              <Parameters>
                <DUISParameter name="value">
                  <Value>
                    /*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='UpdateDeviceConfigurationInstantaneousPowerThreshold']/*[local-name()='MediumHighPowerThreshold']
                  </Value>
                </DUISParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.13">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.25">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="68" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='SetElectricitySupplyTamperState']/*[local-name()='SupplyTamperState' and text()='Locked'])" instanceId="0-0:50.1.0.255" requestType="set">
          <Rules>
            <Rule handler="FirstBitOfBitStringHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>true</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="2" classId="68" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='SetElectricitySupplyTamperState']/*[local-name()='SupplyTamperState' and text()='Unchanged'])" instanceId="0-0:50.1.0.255" requestType="set">
          <Rules>
            <Rule handler="FirstBitOfBitStringHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>false</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="3" classId="68" duisFilter="boolean(/*[local-name()='S1SPRequest']/*[local-name()='Request']/*[local-name()='Body']/*[local-name()='SetElectricitySupplyTamperState']/*[local-name()='SupplyTamperState' and text()='Unchanged'])" instanceId="0-0:50.1.0.255" requestType="set">
          <Rules>
            <Rule handler="FirstBitOfBitStringHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>false</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="6.27">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="3" instanceId="1-0:32.32.0.1" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="3" instanceId="1-0:32.36.0.1" requestType="action">
          <Rules>
            <Rule handler="IntegerHandler">
              <Parameters>
                <FixedParameter name="value">
                  <Value>0</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="7.2">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="68" instanceId="0-0:50.1.0.255" requestType="action">
          <Rules>
            <Rule handler="ArbitratorActionHandler">
              <Parameters>
                <FixedParameter name="requestActor">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="requestAction">
                  <Value>1</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="7.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="1" classId="68" instanceId="0-0:50.1.0.255" requestType="action">
          <Rules>
            <Rule handler="ArbitratorActionHandler">
              <Parameters>
                <FixedParameter name="requestActor">
                  <Value>3</Value>
                </FixedParameter>
                <FixedParameter name="requestAction">
                  <Value>2</Value>
                </FixedParameter>
              </Parameters>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="7.4">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="8.1.1">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
    <ServiceRequest serviceReferenceVariant="11.3">
      <DLMSRequests>
        <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
          <Rules/>
        </DLMSRequest>
        <DLMSRequest attributeMethodId="20" classId="30227" instanceId="0-0:198.4.0.255" requestType="set">
          <Rules>
            <Rule handler="TrilliantPublicKeyHandler">
              <Parameters/>
            </Rule>
          </Rules>
        </DLMSRequest>
      </DLMSRequests>
    </ServiceRequest>
  </ServiceRequests>
  <NoServiceRequest>
    <DLMSRequests>
      <DLMSRequest attributeMethodId="2" classId="8" instanceId="0-0:1.0.1.255" requestType="set">
        <Rules>
          <Rule handler="OctetStringDateTimeSetClockHandler">
            <Parameters>
              <FixedParameter name="tolerance">
                <Value>300</Value>
              </FixedParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>
      <DLMSRequest attributeMethodId="1" classId="15" instanceId="0-0:40.0.1.255" requestType="action">
        <Rules/>
      </DLMSRequest>
      <DLMSRequest attributeMethodId="2" classId="64" instanceId="0-0:43.0.0.255" requestType="action">
        <Rules>
          <Rule handler="KeyTransferHandler">
            <Parameters>
              <FixedParameter name="dlmsClient">
                <Value>0x01</Value>
              </FixedParameter>
              <FixedParameter name="guekKeyType">
                <Value>0</Value>
              </FixedParameter>
              <FixedParameter name="akKeyType">
                <Value>2</Value>
              </FixedParameter>
              <FixedParameter name="numberOfKeysAllowed">
                <Value>2</Value>
              </FixedParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>
      <DLMSRequest attributeMethodId="2" classId="30122" instanceId="0-0:199.122.0.255" requestType="set">
        <Rules/>
      </DLMSRequest>
      <DLMSRequest attributeMethodId="1" classId="30309" instanceId="0-0:199.126.0.255" requestType="action">
        <Rules>
          <Rule handler="KeyTransferHandler">
            <Parameters>
              <FixedParameter name="mkKeyType">
                <Value>0</Value>
              </FixedParameter>
              <FixedParameter name="numberOfKeysAllowed">
                <Value>1</Value>
              </FixedParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>
      <!-- Load Profile 1 (0-0:99.1.0.255) - Attribute 3 (capture_objects) -->
      <DLMSRequest attributeMethodId="3" classId="7" instanceId="0-0:99.1.0.255" requestType="set">
        <Rules>
          <Rule handler="CaptureObjectDefinitionsHandler">
            <Parameters>
              <FixedXMLParameter name="definitions">
                <CaptureObjectDefinitions>
                  <CaptureObjectDefinition>
                    <ClassId>8</ClassId>
                    <LogicalName>0000010000FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>1</ClassId>
                    <LogicalName>0000600A01FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>5</ClassId>
                    <LogicalName>0100010400FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                </CaptureObjectDefinitions>
              </FixedXMLParameter>
            </Parameters>
          </Rule>
          <Rule handler="CaptureObjectDefinitionsHandler">
            <Parameters>
              <FixedXMLParameter name="definitions">
                <CaptureObjectDefinitions>
                  <CaptureObjectDefinition>
                    <ClassId>8</ClassId>
                    <LogicalName>0000010000FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>1</ClassId>
                    <LogicalName>0000600A01FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>3</ClassId>
                    <LogicalName>0100010800FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                </CaptureObjectDefinitions>
              </FixedXMLParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>

      <!-- Load Profile 1 (0-0:99.1.0.255) - Attribute 4 (capture_period) -->
      <DLMSRequest attributeMethodId="4" classId="7" instanceId="0-0:99.1.0.255" requestType="set">
        <Rules>
          <Rule handler="DoubleLongUnsignedHandler">
            <Parameters>
              <FixedParameter name="value">
                <Value>1800</Value>
              </FixedParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>

      <!-- Load Profile 2 (0-0:99.1.1.255) - Attribute 3 (capture_objects) -->
      <DLMSRequest attributeMethodId="3" classId="7" instanceId="0-0:99.1.1.255" requestType="set">
        <Rules>
          <Rule handler="CaptureObjectDefinitionsHandler">
            <Parameters>
              <FixedXMLParameter name="definitions">
                <CaptureObjectDefinitions>
                  <CaptureObjectDefinition>
                    <ClassId>8</ClassId>
                    <LogicalName>0000010000FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>1</ClassId>
                    <LogicalName>0000600A01FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>3</ClassId>
                    <LogicalName>0100020800FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>3</ClassId>
                    <LogicalName>0100030800FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                  <CaptureObjectDefinition>
                    <ClassId>3</ClassId>
                    <LogicalName>0100040800FF</LogicalName>
                    <AttributeIndex>2</AttributeIndex>
                    <DataIndex>0</DataIndex>
                  </CaptureObjectDefinition>
                </CaptureObjectDefinitions>
              </FixedXMLParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>

      <!-- Load Profile 2 (0-0:99.1.1.255) - Attribute 4 (capture_period) -->
      <DLMSRequest attributeMethodId="4" classId="7" instanceId="0-0:99.1.1.255" requestType="set">
        <Rules>
          <Rule handler="DoubleLongUnsignedHandler">
            <Parameters>
              <FixedParameter name="value">
                <Value>1800</Value>
              </FixedParameter>
            </Parameters>
          </Rule>
        </Rules>
      </DLMSRequest>
    </DLMSRequests>
  </NoServiceRequest>
<Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><DigestValue>nfgnDDKiECtXfV2wA7CbbHq0sHrWnrzMOgqhLGks/1M=</DigestValue></Reference></SignedInfo><SignatureValue>IhHzgdAcvZL9maSjUjNdWMDPvWgdUPz18kRHd/jbCrKaZwl1MH3c73BeySchoAg4Sa6VnnTH25WO&#13;
A0u6bkPLqg==</SignatureValue></Signature></DLMSDetectConfiguration>