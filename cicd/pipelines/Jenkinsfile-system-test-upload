#!groovy

// noinspection GroovyUnusedAssignment
@Library(["Pipeline-Global-Library", "jenkins-commons-lib"]) _

def projectName = env.JOB_NAME.split("/")[1]
def credentialsJiraId = "cd-dccdcopt-jira"

currentBuild.result = "SUCCESS"
currentBuild.displayName = projectName + "-${env.BUILD_NUMBER}"

sshCredentialsId = "rsa-dco-pt-x"
env.BRANCH_NAME = ""

try {

    properties([disableConcurrentBuilds(),
                parameters([
                        string(name: 'nodeName', description: 'Jenkins node where the tests can be found'),
                        string(name: 'jWorkspace', description: 'Job Workspace'),
                        string(name: 'dcoHostIp', description: 'DCO Host IP Address'),
                        string(name: 'testExecutionId', description: 'Test Execution ID'),
                        string(name: 'testReportPath', description: 'Test Report Path (ex: clean-up-scheduler, request-manager)')
                ])
    ])

    node(params.nodeName) {

        currentBuild.description = "Jira Test Execution ID: ${params.testExecutionId}\nJenkins node: ${params.nodeName}\nWorkspace: ${params.jWorkspace}\nTest Suites: ${params.testReportPath}"
        hostIp = params.dcoHostIp
        testReportPath = "/home/<USER>/dco/dco-tr/test/system-testing/test-suites/" + params.testReportPath
        repoWorkspace = "/home/<USER>/workspace/DCCDCOPT/" + params.jWorkspace

        echo "Running on Node: ${env.NODE_NAME}"

        ws(repoWorkspace) {
            stages.name("Upload Results to JIRA").timed().timeout(90).retry(5).run {
                timestamps {
                    withCredentials([usernamePassword(credentialsId: credentialsJiraId, passwordVariable: 'password', usernameVariable: 'username')]) {
                        runShellInDcoHost("""cd dco/dco-tr/cicd/cicd-scripts &&
                        ./upload_test_2_xray.sh -a ${username}:${password} -j ${params.testExecutionId} -t ${testReportPath}
                        """)
                    }
                }
            }
        }
    }

} catch (err) {
    echo err.toString()
    currentBuild.result = "FAILURE"
} finally {
    node(params.nodeName) {
        stages.doNotifyElasticSearch("")
    }
}

def runShellInDcoHost(String commands) {
    withCredentials([sshUserPrivateKey(credentialsId: sshCredentialsId, keyFileVariable: 'SSH_KEY')]) {
        sh "ssh -i $SSH_KEY -oStrictHostKeyChecking=no -oIdentitiesOnly=yes dcoadmin@${hostIp} '${commands}'"
    }
}
