#!/bin/bash
dcoDB="-Dthorntail.datasources.data-sources.dco-ds.connection-url=*****************************************\,10.42.2.152:3306/dccdco?useSSL=false&allowPublicKeyRetrieval=true&max_allowed_packet=25M&retriesAllDown=1&connectTimeout=120&socketTimeout=2000 \
-Dquarkus.datasource.jdbc.url=*****************************************\,10.42.2.152:3306/dccdco?useSSL=false&allowPublicKeyRetrieval=true&max_allowed_packet=25M&retriesAllDown=1&connectTimeout=120&socketTimeout=2000"
migrationDB="-Dthorntail.datasources.data-sources.dco-migration-ds.connection-url=*****************************************\,10.42.2.153:3307/dccdcomigration?useSSL=false&allowPublicKeyRetrieval=true \
-Dquarkus.datasource.dco-migration-ds.jdbc.url=*****************************************\,10.42.2.153:3307/dccdcomigration?useSSL=false&allowPublicKeyRetrieval=true"
fullDB="${dcoDB} ${migrationDB}"
secure_crypto_key_with_db="${fullDB} -Dsecure.keystore.key.alias=dco-secure-ecdh"
##This function upgrades the already existing deployment with the connection strings pointing to the correct machines.

source ./setup-drop-config.sh

remote_deploy_upgrade(){
  local -n params=$1
  ${params[@]} \
  --set dco-migration.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-migration.extraEnv[0].value="${fullDB}" \
  --set dco-request-manager.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-request-manager.extraEnv[0].value="${dcoDB}" \
  --set dco-clean-up-scheduler.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-clean-up-scheduler.extraEnv[0].value="${dcoDB}" \
  --set dco-command-manager-ie.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-command-manager-ie.extraEnv[0].value="${fullDB}" \
  --set dco-command-manager-trilliant.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-command-manager-trilliant.extraEnv[0].value="${fullDB}" \
  --set dco-command-manager-secure.extraEnv[0].name=JAVA_EXTRA_OPTIONS --set dco-command-manager-secure.extraEnv[0].value="${secure_crypto_key_with_db}" \
  ${extraOptions[@]}
}
