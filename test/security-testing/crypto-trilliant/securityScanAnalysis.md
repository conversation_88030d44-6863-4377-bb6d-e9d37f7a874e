## Security Scan Analysis

The alerts raised by both instances of the crypto-trilliant component were identical, so their analysis is consolidated into this file.

### ZAP application configurations

Zap version: 2.15.0

Zap add-ons versions:

| Name                                         | Version | Description                                                                                                                                                                           |
|----------------------------------------------|---------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Active scanner rules                         | 69.0.0  | The release status Active Scanner rules                                                                                                                                               |
| Ajax Spider                                  | 23.21.0 | Allows you to spider sites that make heavy use of JavaScript using Crawljax                                                                                                           |
| Alert Filters                                | 22.0.0  | Allows you to automate the changing of alert risk levels.                                                                                                                             |
| Authentication Helper                        | 0.16.0  | Helps identify and set up authentication handling                                                                                                                                     |
| Automation Framework                         | 0.43.0  | Automation Framework.                                                                                                                                                                 |
| Call Home                                    | 0.13.0  | Handles all of the calls to ZAP services.                                                                                                                                             |
| Common Library                               | 1.28.0  | A common library, for use by other add-ons.                                                                                                                                           |
| Database                                     | 0.6.0   | Provides database engines and related infrastructure.                                                                                                                                 |
| Diff                                         | 16.0.0  | Displays a dialog showing the differences between 2 requests or responses. It uses diffutils and diff\_match\_patch                                                                   |
| Directory List v1.0                          | 8.0.0   | List of directory names to be used with Forced Browse or Fuzzer add-on.                                                                                                               |
| DOM XSS Active scanner rule                  | 19.0.0  | DOM XSS Active scanner rule                                                                                                                                                           |
| Encoder                                      | 1.5.0   | Adds encode/decode/hash dialog and support for scripted processors as well                                                                                                            |
| Forced Browse                                | 16.0.0  | Forced browsing of files and directories using code from the OWASP DirBuster tool                                                                                                     |
| Fuzzer                                       | 13.14.0 | Advanced fuzzer for manual testing                                                                                                                                                    |
| Getting Started with ZAP Guide               | 18.0.0  | A short Getting Started with ZAP Guide                                                                                                                                                |
| GraalVM JavaScript                           | 0.8.0   | Provides the GraalVM JavaScript engine for ZAP scripting.                                                                                                                             |
| GraphQL Support                              | 0.25.0  | Inspect and attack GraphQL endpoints.                                                                                                                                                 |
| Help - English                               | 18.0.0  | English version of the ZAP help file.                                                                                                                                                 |
| HUD - Heads Up Display                       | 0.19.0  | Display information from ZAP in browser.                                                                                                                                              |
| Import/Export                                | 0.12.0  | Import and Export functionality                                                                                                                                                       |
| Invoke Applications                          | 15.0.0  | Invoke external applications passing context related information such as URLs and parameters                                                                                          |
| Linux WebDrivers                             | 115.0.0 | Linux WebDrivers for Firefox and Chrome.                                                                                                                                              |
| Network                                      | 0.18.0  | Provides core networking capabilities.                                                                                                                                                |
| OAST Support                                 | 0.20.0  | Allows you to exploit out-of-band vulnerabilities                                                                                                                                     |
| Online menus                                 | 13.0.0  | ZAP Online menu items                                                                                                                                                                 |
| OpenAPI Support                              | 43.0.0  | Imports and spiders OpenAPI definitions.                                                                                                                                              |
| Passive Scanner                              | 0.0.1   | Provides core passive scanning capabilities.                                                                                                                                          |
| Passive scanner rules                        | 61.0.0  | The release status Passive Scanner rules                                                                                                                                              |
| Postman Support                              | 0.4.0   | Imports and spiders Postman collections.                                                                                                                                              |
| Quick Start                                  | 50.0.0  | Provides a tab which allows you to quickly test a target application                                                                                                                  |
| Replacer                                     | 19.0.0  | Easy way to replace strings in requests and responses.                                                                                                                                |
| Report Generation                            | 0.34.0  | Official ZAP Reports.                                                                                                                                                                 |
| Requester                                    | 7.7.0   | Allows to manually edit and send messages.                                                                                                                                            |
| Retest                                       | 0.10.0  | An add-on to retest for presence/absence of previously generated alerts.                                                                                                              |
| Retire.js                                    | 0.42.0  | Use Retire.js to identify vulnerable or out-dated JavaScript packages.                                                                                                                |
| Reveal                                       | 8.0.0   | Show hidden fields and enable disabled fields                                                                                                                                         |
| Script Console                               | 45.7.0  | Supports all JSR 223 scripting languages                                                                                                                                              |
| Selenium                                     | 15.30.0 | WebDriver provider and includes HtmlUnit browser                                                                                                                                      |
| SOAP Support                                 | 23.0.0  | Imports and scans WSDL files containing SOAP endpoints.                                                                                                                               |
| Spider                                       | 0.12.0  | Spider used for automatically finding URIs on a site.                                                                                                                                 |
| Tips and Tricks                              | 13.0.0  | Display ZAP Tips and Tricks                                                                                                                                                           |
| Value Generator                              | 6.6.0   | This Value Generator Add-on allows a user to define field names and values to be used when submitting values to an app. Fields can be added, modified, enabled/disabled, and deleted. |
| WebSockets                                   | 31.0.0  | Allows you to inspect WebSocket communication.                                                                                                                                        |
| Zest - Graphical Security Scripting Language | 47.0.0  | A graphical security scripting language, ZAPs macro language on steroids                                                                                                              |


ZAP active scan input vectors:

- [x] URL Query String & Data Driven Nodes
  - [x] Add URL Query Parameter?
- [x] POST Data
- [x] URL Path
- [x] HTTP Headers
  - [x] All Requests
- [ ] Cookie Data
- [x] Enable Script Input Vectors
- [x] Multipart Form-Data
- [x] XML Tag/Attribute
- [x] JSON
  - [x] Scan Null Values
- [x] Google Web Toolkit
- [x] OData ID/Filter
- [x] Direct Web Remoting

### Alerts Found
For details, check the alerts' report files (either zapAlertsReportQuarkus.md or zapAlertsReportThorntail.md).

| Name                                          | Risk Level    | Number of Instances |
|-----------------------------------------------|---------------|---------------------|
| Application Error Disclosure                  | Medium        | 1                   |
| Information Disclosure - Debug Error Messages | Low           | 1                   |
| X-Content-Type-Options Header Missing         | Low           | 5                   |
| User Agent Fuzzer                             | Informational | 24                  |

#### X-Content-Type-Options Header Missing
Request Headers (no sniffing option is missing)
```
POST http://localhost:8092/dco/crypto/trilliant/authenticate HTTP/1.1
host: localhost:8092
user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:125.0) Gecko/20100101 Firefox/125.0
pragma: no-cache
cache-control: no-cache
accept: application/json
content-type: application/json
content-length: 3717
```

Request Body:
```json
{"encryptionKey":"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjQtMDUtMDdUMTE6NDE6MzkuNDc3KzAxOjAwIiBpZD0iYWZhMjMzODQtN2VkZi00MDM3LTgzMWQtYzYzMzJmOTdlZGEzIiBzY2hlbWFWZXJzaW9uPSIxLjAiPgogICAgPERldmljZUlkcz4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMDAtMDAtMDAtMDE8L0RldmljZUlkPgogICAgPC9EZXZpY2VJZHM+CiAgICA8S2V5VHlwZT5HVUVLPC9LZXlUeXBlPgogICAgPERMTVNDbGllbnQ+MHgwMTwvRExNU0NsaWVudD4KICAgIDxFbmNyeXB0aW9uPgogICAgICAgIDxNZWNoYW5pc20+QUVTPC9NZWNoYW5pc20+CiAgICAgICAgPEVuY3J5cHRpb25LZXlJZD4yPC9FbmNyeXB0aW9uS2V5SWQ+CiAgICAgICAgPEtleT5XRTFJR2lRRmRlV1dWQzNYNVdtSkhRPT08L0tleT4KICAgIDwvRW5jcnlwdGlvbj4KICAgIDxBdXRoZW50aWNhdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkVDQzwvTWVjaGFuaXNtPgogICAgICAgIDxBdXRoZW50aWNhdGlvbktleUlkPjM8L0F1dGhlbnRpY2F0aW9uS2V5SWQ+CiAgICA8L0F1dGhlbnRpY2F0aW9uPgo8U2lnbmF0dXJlIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIj48U2lnbmVkSW5mbz48Q2Fub25pY2FsaXphdGlvbk1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMTAveG1sLWV4Yy1jMTRuIyIvPjxTaWduYXR1cmVNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNlY2RzYS1zaGEyNTYiLz48UmVmZXJlbmNlIFVSST0iIj48VHJhbnNmb3Jtcz48VHJhbnNmb3JtIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnI2VudmVsb3BlZC1zaWduYXR1cmUiLz48L1RyYW5zZm9ybXM+PERpZ2VzdE1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvMDQveG1sZW5jI3NoYTI1NiIvPjxEaWdlc3RWYWx1ZT5SNFdxMDYwbHFJbncrWjdpSjNMTi9FMi8vSUJmRnR0VUdaTVlMNkk1Q1VnPTwvRGlnZXN0VmFsdWU+PC9SZWZlcmVuY2U+PC9TaWduZWRJbmZvPjxTaWduYXR1cmVWYWx1ZT5MdmREa0lxb0FUWnE2YUZOWWZuM3VvbUwvTGVzOXArYUozUjZDZDFhdUtMN2tTWlBuYnBWOFBKbk5PUThjdElUdHY4ekZLSUNneVU3JiMxMzsKZEhqam40dHVzdz09PC9TaWduYXR1cmVWYWx1ZT48L1NpZ25hdHVyZT48L0RMTVNLZXlQYWNrYWdlPg==","authenticationKey":"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjQtMDUtMDdUMTE6NDI6MDQuMDUxKzAxOjAwIiBpZD0iNzY2ZmJlZWQtOTEyOS00OTBjLTk1NDEtNzgyMmQyY2VhMDg3IiBzY2hlbWFWZXJzaW9uPSIxLjAiPgogICAgPERldmljZUlkcz4KICAgICAgICA8RGV2aWNlSWQ+MDAtMDAtMDAtMDAtMDAtMDAtMDAtMDE8L0RldmljZUlkPgogICAgPC9EZXZpY2VJZHM+CiAgICA8S2V5VHlwZT5BSzwvS2V5VHlwZT4KICAgIDxETE1TQ2xpZW50PjB4MDE8L0RMTVNDbGllbnQ+CiAgICA8RW5jcnlwdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkFFUzwvTWVjaGFuaXNtPgogICAgICAgIDxFbmNyeXB0aW9uS2V5SWQ+NjwvRW5jcnlwdGlvbktleUlkPgogICAgICAgIDxLZXk+N1FrNGFqRmt5U3IwdVlZRml0TWZ3dz09PC9LZXk+CiAgICA8L0VuY3J5cHRpb24+CiAgICA8QXV0aGVudGljYXRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5FQ0M8L01lY2hhbmlzbT4KICAgICAgICA8QXV0aGVudGljYXRpb25LZXlJZD4zPC9BdXRoZW50aWNhdGlvbktleUlkPgogICAgPC9BdXRoZW50aWNhdGlvbj4KPFNpZ25hdHVyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0biMiLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8wNC94bWxkc2lnLW1vcmUjZWNkc2Etc2hhMjU2Ii8+PFJlZmVyZW5jZSBVUkk9IiI+PFRyYW5zZm9ybXM+PFRyYW5zZm9ybSBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNlbnZlbG9wZWQtc2lnbmF0dXJlIi8+PC9UcmFuc2Zvcm1zPjxEaWdlc3RNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGVuYyNzaGEyNTYiLz48RGlnZXN0VmFsdWU+alJONVVqRmRlTlJndU0yN2NRdWgyUHZkZFFoRmtCb3lOdjM1ZzRlbFdBdz08L0RpZ2VzdFZhbHVlPjwvUmVmZXJlbmNlPjwvU2lnbmVkSW5mbz48U2lnbmF0dXJlVmFsdWU+UUJ1SllIZHdURWc0UzJkSWdTL2lOdkV1VGhrN2ZGWllwUlFjR1NXSjlCZEhlVEhtOGlGUXZ3VG91bDZUd0FXd3RuQlFzSXNXeU9lNSYjMTM7CnBHV2ZoRzM5cGc9PTwvU2lnbmF0dXJlVmFsdWU+PC9TaWduYXR1cmU+PC9ETE1TS2V5UGFja2FnZT4=","cleartextMessage":"wQFBABQAAA0AAP8GAAYAAAEf","initialisationVector":"TU1NAAC8YU4BI0Vn","keyPackageValidationData":{"dlmsClient":"0x01","deviceId":"00-00-00-00-00-00-00-01"}}
```

Responses:
```json
{"authenticatedMessage":"wQFBABQAAA0AAP8GAAYAAAEfaDRVoa+RcymMKPna"}
```

* This alert refers to mime sniffing performed by Browsers which don't receive this header.
* It does not affect or impact the application.

#### User Agent Fuzzer
Requests sent were empty. Examples of requests/Responses:
```
POST http://localhost:8092/dco/crypto/trilliant/generateKey HTTP/1.1
host: localhost:8092
user-agent: Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1)
pragma: no-cache
cache-control: no-cache
accept: application/json
content-type: application/json
content-length: 1794
```

```
POST http://localhost:8092/dco/crypto/trilliant/generateKey HTTP/1.1
host: localhost:8092
user-agent: Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0)
pragma: no-cache
cache-control: no-cache
accept: application/json
content-type: application/json
content-length: 1794
```

Responses:
```
{"key":"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjQtMTEtMjdUMTE6MDM6MzAuNDg4WiIgaWQ9ImRkM2M4ZDcyLTExNWQtNGFmMC1hZmFiLTgyMDAwYjk4MTBkMSIgc2NoZW1hVmVyc2lvbj0iMS4wIj4KICAgIDxEZXZpY2VJZHM+CiAgICAgICAgPERldmljZUlkPjAwLTAwLTAwLTAwLTAwLTAwLTAwLTAxPC9EZXZpY2VJZD4KICAgIDwvRGV2aWNlSWRzPgogICAgPEtleVR5cGU+R1VFSzwvS2V5VHlwZT4KICAgIDxETE1TQ2xpZW50PjB4MDE8L0RMTVNDbGllbnQ+CiAgICA8RW5jcnlwdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkFFUzwvTWVjaGFuaXNtPgogICAgICAgIDxFbmNyeXB0aW9uS2V5SWQ+MjwvRW5jcnlwdGlvbktleUlkPgogICAgICAgIDxLZXk+TGtvTFZERFU4YXRIODFLK29hUnNuUT09PC9LZXk+CiAgICA8L0VuY3J5cHRpb24+CiAgICA8QXV0aGVudGljYXRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5FQ0M8L01lY2hhbmlzbT4KICAgICAgICA8QXV0aGVudGljYXRpb25LZXlJZD4zPC9BdXRoZW50aWNhdGlvbktleUlkPgogICAgPC9BdXRoZW50aWNhdGlvbj4KPFNpZ25hdHVyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0biMiLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8wNC94bWxkc2lnLW1vcmUjZWNkc2Etc2hhMjU2Ii8+PFJlZmVyZW5jZSBVUkk9IiI+PFRyYW5zZm9ybXM+PFRyYW5zZm9ybSBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNlbnZlbG9wZWQtc2lnbmF0dXJlIi8+PC9UcmFuc2Zvcm1zPjxEaWdlc3RNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGVuYyNzaGEyNTYiLz48RGlnZXN0VmFsdWU+eEw1RExXUlp4SXowOHl0TkpuUE5ucXBDMEE4ekVzWkpNNjV3amZoclJEbz08L0RpZ2VzdFZhbHVlPjwvUmVmZXJlbmNlPjwvU2lnbmVkSW5mbz48U2lnbmF0dXJlVmFsdWU+Vms2UzhVaXdwRCtVQ2pqOTdycEo0aCsyV1Y0UDV1WDNLalJ2NWFrNGJEUEpGanlvVTlSY2kzVW9EZlZwMExPS2dGWHRzY1hlbG9DdCYjMTM7Ci80dy84MGRBcGc9PTwvU2lnbmF0dXJlVmFsdWU+PC9TaWduYXR1cmU+PC9ETE1TS2V5UGFja2FnZT4=","wrappedKey":"VjfUbRc291Fg/F43jkYj6D24yGnW1gMn"}
```

```
{"key":"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48RExNU0tleVBhY2thZ2UgeG1sbnM6bnMyPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIiBjcmVhdGlvbkRhdGU9IjIwMjQtMTEtMjdUMTE6MDM6MjkuODI3WiIgaWQ9IjhhNDQ5ZmVmLWU3MmUtNDdiNy1iZjIwLTViODc5YzA2MzAzMCIgc2NoZW1hVmVyc2lvbj0iMS4wIj4KICAgIDxEZXZpY2VJZHM+CiAgICAgICAgPERldmljZUlkPjAwLTAwLTAwLTAwLTAwLTAwLTAwLTAxPC9EZXZpY2VJZD4KICAgIDwvRGV2aWNlSWRzPgogICAgPEtleVR5cGU+R1VFSzwvS2V5VHlwZT4KICAgIDxETE1TQ2xpZW50PjB4MDE8L0RMTVNDbGllbnQ+CiAgICA8RW5jcnlwdGlvbj4KICAgICAgICA8TWVjaGFuaXNtPkFFUzwvTWVjaGFuaXNtPgogICAgICAgIDxFbmNyeXB0aW9uS2V5SWQ+MjwvRW5jcnlwdGlvbktleUlkPgogICAgICAgIDxLZXk+QkhGYTRiYUdoeFQ4cmEzVWZETGZTUT09PC9LZXk+CiAgICA8L0VuY3J5cHRpb24+CiAgICA8QXV0aGVudGljYXRpb24+CiAgICAgICAgPE1lY2hhbmlzbT5FQ0M8L01lY2hhbmlzbT4KICAgICAgICA8QXV0aGVudGljYXRpb25LZXlJZD4zPC9BdXRoZW50aWNhdGlvbktleUlkPgogICAgPC9BdXRoZW50aWNhdGlvbj4KPFNpZ25hdHVyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzEwL3htbC1leGMtYzE0biMiLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMS8wNC94bWxkc2lnLW1vcmUjZWNkc2Etc2hhMjU2Ii8+PFJlZmVyZW5jZSBVUkk9IiI+PFRyYW5zZm9ybXM+PFRyYW5zZm9ybSBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNlbnZlbG9wZWQtc2lnbmF0dXJlIi8+PC9UcmFuc2Zvcm1zPjxEaWdlc3RNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGVuYyNzaGEyNTYiLz48RGlnZXN0VmFsdWU+Z2FPaS9acDFiVVl2WHdQMkJ6Sm1FZWZweDltc2tmT3ByR2o1R1k2U3l5UT08L0RpZ2VzdFZhbHVlPjwvUmVmZXJlbmNlPjwvU2lnbmVkSW5mbz48U2lnbmF0dXJlVmFsdWU+RlRrMzNvbnY4UXp1VGlWZ0NaTDVJbEdZUHozOXR0VlFPWXFMbTBCakJrL3Z3ZFQ4Ymh5MGFNWnhiaENZd0d2eXM1TmU2TTNEdjhtKyYjMTM7ClkxNEwwdElLVnc9PTwvU2lnbmF0dXJlVmFsdWU+PC9TaWduYXR1cmU+PC9ETE1TS2V5UGFja2FnZT4=","wrappedKey":"M/e1m+x9b0aDUgOtZrwFYrJjMzVnSNSV"}
```

* This alert refers to the application having a different behaviour depending on the User-Agent sent in the request.
* The application does not make use of the User-Agent field, and the difference in the responses originating from requests
  with different user-agents is in the transactionId field, which would change for any request sent anyway.
* This is therefore a false-positive alert.

#### Additional Alerts

* Additional alerts were identified - Application Error Disclosure and Information Disclosure - Debug Error Messages:
These refer to several levels of information disclosure (error/warning messages). These alerts are false positives, since they were triggered by the Open API endpoint.