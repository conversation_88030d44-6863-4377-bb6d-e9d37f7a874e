*** Settings ***
Test Timeout  5 minutes
Documentation  Validate the *Detect* implementation on the DLMS commands that translate the *SRV4.13*
...            for *Itron* *GSME* (*Read Prepayment Configuration*).

Test Tags  S1SP_IE  DCO_DETECT  DCO_ITRON  DCO_DLMS  DCO_GSME  DCO_SRV4.13

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-ie/resource-detect.resource

Suite Setup     Detect Suite Setup
Test Teardown   Detect Test Teardown
Suite Teardown  Detect Suite Teardown

*** Test Cases ***

DETECT-IE-PARTIAL-ITRON-GSME-SRV4.13 - Uncorrelated Request
    [Documentation]  Make requests that do not correlated to this SRV
    ...              - (1) The expected attribute id of 2 is replaced by 1 for the class 8
    ...              - (2) Erroneously use the class id 1 instead of 8
    ...              - (3) Erroneously use the OBIS code 0000218015FF instead of 0000010000FF
    ...              - (4) Use an action request instead of the expected set request
	[Tags]  DCO-2835  DCO_NEGATIVE_TEST  DCO-1482
    [Setup]  Detect Test Setup  uncorrelated/detect-ie-partial-itron-gsme-srv4.13-uncorrelated-duis.xml  ItronGsmeA
    [Template]  Detect Failure
    uncorrelated/detect-ie-partial-itron-gsme-srv4.13-uncorrelated-dlms-01.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-itron-gsme-srv4.13-uncorrelated-dlms-02.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-itron-gsme-srv4.13-uncorrelated-dlms-03.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-itron-gsme-srv4.13-uncorrelated-dlms-04.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
