<?xml version="1.0" encoding="UTF-8"?>
<sr:Request xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:sr="http://www.dccinterface.co.uk/ServiceUserGateway" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0">
    <sr:Header>
        <sr:RequestID>90-B3-D5-1F-30-01-00-00:00-00-00-00-00-01-02-0A:1000</sr:RequestID>
        <sr:CommandVariant>4</sr:CommandVariant>
        <sr:ServiceReference>1.1</sr:ServiceReference>
        <sr:ServiceReferenceVariant>1.1.1</sr:ServiceReferenceVariant>
    </sr:Header>
    <sr:Body>
        <sr:UpdateImportTariffPrimaryElement>
            <sr:GasTariffElements>
                <sr:SwitchingTable>
                    <sr:DayProfiles>
                        <sr:DayProfile>
                            <sr:DayName>1</sr:DayName>
                            <sr:TOUTariffAction>2</sr:TOUTariffAction>
                        </sr:DayProfile>
                        <sr:DayProfile>
                            <sr:DayName>2</sr:DayName>
                            <sr:TOUTariffAction>3</sr:TOUTariffAction>
                        </sr:DayProfile>
                        <sr:DayProfile>
                            <sr:DayName>3</sr:DayName>
                            <sr:TOUTariffAction>1</sr:TOUTariffAction>
                        </sr:DayProfile>
                    </sr:DayProfiles>
                    <sr:WeekProfiles>
                        <sr:WeekProfile>
                            <sr:WeekName>1</sr:WeekName>
                            <sr:ReferencedDayName index="1">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="2">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="3">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="4">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="5">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="6">3</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="7">3</sr:ReferencedDayName>
                        </sr:WeekProfile>
                        <sr:WeekProfile>
                            <sr:WeekName>2</sr:WeekName>
                            <sr:ReferencedDayName index="1">2</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="2">2</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="3">2</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="4">2</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="5">2</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="6">3</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="7">3</sr:ReferencedDayName>
                        </sr:WeekProfile>
                    </sr:WeekProfiles>
                    <sr:Seasons>
                        <sr:Season>
                            <sr:SeasonStartDate>
                                <sr:GasYearWithWildcards>
                                    <sr:SpecifiedYear>2014</sr:SpecifiedYear>
                                </sr:GasYearWithWildcards>
                                <sr:GasMonthWithWildcards>
                                    <sr:SpecifiedMonth>10</sr:SpecifiedMonth>
                                </sr:GasMonthWithWildcards>
                                <sr:GasDayOfMonthWithWildcards>
                                    <sr:SpecifiedDayOfMonth>27</sr:SpecifiedDayOfMonth>
                                </sr:GasDayOfMonthWithWildcards>
                                <sr:GasDayOfWeekWithWildcards>
                                    <sr:NonSpecifiedDayOfWeek/>
                                </sr:GasDayOfWeekWithWildcards>
                            </sr:SeasonStartDate>
                            <sr:ReferencedWeekName>1</sr:ReferencedWeekName>
                        </sr:Season>
                        <sr:Season>
                            <sr:SeasonStartDate>
                                <sr:GasYearWithWildcards>
                                    <sr:SpecifiedYear>2015</sr:SpecifiedYear>
                                </sr:GasYearWithWildcards>
                                <sr:GasMonthWithWildcards>
                                    <sr:SpecifiedMonth>03</sr:SpecifiedMonth>
                                </sr:GasMonthWithWildcards>
                                <sr:GasDayOfMonthWithWildcards>
                                    <sr:SpecifiedDayOfMonth>29</sr:SpecifiedDayOfMonth>
                                </sr:GasDayOfMonthWithWildcards>
                                <sr:GasDayOfWeekWithWildcards>
                                    <sr:NonSpecifiedDayOfWeek/>
                                </sr:GasDayOfWeekWithWildcards>
                            </sr:SeasonStartDate>
                            <sr:ReferencedWeekName>2</sr:ReferencedWeekName>
                        </sr:Season>
                    </sr:Seasons>
                </sr:SwitchingTable>
                <sr:SpecialDays>
                    <sr:SpecialDay>
                        <sr:Date>
                            <sr:GasYearWithWildcards>
                                <sr:SpecifiedYear>2015</sr:SpecifiedYear>
                            </sr:GasYearWithWildcards>
                            <sr:GasMonthWithWildcards>
                                <sr:SpecifiedMonth>05</sr:SpecifiedMonth>
                            </sr:GasMonthWithWildcards>
                            <sr:GasDayOfMonthWithWildcards>
                                <sr:SpecifiedDayOfMonth>01</sr:SpecifiedDayOfMonth>
                            </sr:GasDayOfMonthWithWildcards>
                            <sr:GasDayOfWeekWithWildcards>
                                <sr:SpecifiedDayOfWeek>3</sr:SpecifiedDayOfWeek>
                            </sr:GasDayOfWeekWithWildcards>
                        </sr:Date>
                        <sr:ReferencedDayName>3</sr:ReferencedDayName>
                    </sr:SpecialDay>
                    <sr:SpecialDay>
                        <sr:Date>
                            <sr:GasYearWithWildcards>
                                <sr:NonSpecifiedYear/>
                            </sr:GasYearWithWildcards>
                            <sr:GasMonthWithWildcards>
                                <sr:SpecifiedMonth>12</sr:SpecifiedMonth>
                            </sr:GasMonthWithWildcards>
                            <sr:GasDayOfMonthWithWildcards>
                                <sr:SpecifiedDayOfMonth>25</sr:SpecifiedDayOfMonth>
                            </sr:GasDayOfMonthWithWildcards>
                            <sr:GasDayOfWeekWithWildcards>
                                <sr:SpecifiedDayOfWeek>3</sr:SpecifiedDayOfWeek>
                            </sr:GasDayOfWeekWithWildcards>
                        </sr:Date>
                        <sr:ReferencedDayName>3</sr:ReferencedDayName>
                    </sr:SpecialDay>
                </sr:SpecialDays>
                <sr:ThresholdMatrix>
                    <sr:BlockThreshold index="1">65535</sr:BlockThreshold>
                    <sr:BlockThreshold index="2">65535</sr:BlockThreshold>
                    <sr:BlockThreshold index="3">65535</sr:BlockThreshold>
                </sr:ThresholdMatrix>
            </sr:GasTariffElements>
            <sr:PriceElements>
                <sr:GasPriceElements>
                    <sr:CurrencyUnits>GBP</sr:CurrencyUnits>
                    <sr:StandingCharge>20000</sr:StandingCharge>
                    <sr:TOUTariff>
                        <sr:TOUPrice index="1">2121</sr:TOUPrice>
                        <sr:TOUPrice index="2">3127</sr:TOUPrice>
                        <sr:TOUPrice index="3">4744</sr:TOUPrice>
                        <sr:TOUPrice index="4">1222</sr:TOUPrice>
                    </sr:TOUTariff>
                </sr:GasPriceElements>
            </sr:PriceElements>
        </sr:UpdateImportTariffPrimaryElement>
    </sr:Body>
    <ds:Signature>
        <!-- NOTE: The digest, signature and serial number values below are placeholders and should not be used for actual authentication purposes. -->
        <ds:SignedInfo>
            <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/>
            <ds:Reference URI="">
                <ds:Transforms>
                    <ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                </ds:Transforms>
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>ZGVmYXVsdA==</ds:DigestValue>
            </ds:Reference>
        </ds:SignedInfo>
        <ds:SignatureValue>ZGVmYXVsdA==</ds:SignatureValue>
        <ds:KeyInfo>
            <ds:X509Data>
                <ds:X509IssuerSerial>
                    <ds:X509IssuerName>CN=U1, OU=07</ds:X509IssuerName>
                    <ds:X509SerialNumber>1234567890</ds:X509SerialNumber>
                </ds:X509IssuerSerial>
            </ds:X509Data>
        </ds:KeyInfo>
    </ds:Signature>
</sr:Request>