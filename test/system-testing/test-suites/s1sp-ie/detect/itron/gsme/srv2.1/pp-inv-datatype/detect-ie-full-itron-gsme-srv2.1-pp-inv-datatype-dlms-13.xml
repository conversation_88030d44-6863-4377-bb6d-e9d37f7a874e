<SetRequest>
    <SetRequestNormal>
        <InvokeIdAndPriority Value="41" />
        <AttributeDescriptor>
            <ClassId Value="2328" />
            <InstanceId Value="00003F0103FF" />
            <AttributeId Value="04" />
        </AttributeDescriptor>
        <Value>
            <Structure Qty="09">
                <VisibleString Value="000000000000000000000000000000000000000000000000"/>
                <Enum Value="00"/>
                <Boolean Value="00"/>
                <Boolean Value="01"/>
                <Boolean Value="00"/>
                <BitString Value="000000000"/>
                <Boolean Value="00"/>
                <Boolean Value="00"/>
                <Boolean Value="00"/>
            </Structure>
        </Value>
    </SetRequestNormal>
</SetRequest>