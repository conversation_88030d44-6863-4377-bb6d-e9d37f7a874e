<SetRequest>
  <SetRequestNormal>
    <!--Priority: NORMAL ServiceClass: CONFIRMED invokeID: 1-->
    <InvokeIdAndPriority Value="41" />
    <AttributeDescriptor>
      <!--ACCOUNT-->
      <ClassId Value="006F" />
      <!--********.0.255-->
      <InstanceId Value="0001130000FF" />
      <AttributeId Value="0B" />
    </AttributeDescriptor>
    <Value>
      <Array Qty="0A" >
        <Structure Qty="03" >
          <!--*********.0.255-->
          <OctetString Value="0000130A00FF" />
          <!--********.0.255-->
          <OctetString Value="0000130200FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.0.255-->
          <OctetString Value="0000130A00FF" />
          <!--********.1.255-->
          <OctetString Value="0000130201FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.0.255-->
          <OctetString Value="0000130A00FF" />
          <!--********.2.255-->
          <OctetString Value="0000130202FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.0.255-->
          <OctetString Value="0000130A00FF" />
          <!--********.3.255-->
          <OctetString Value="0000130203FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.0.255-->
          <OctetString Value="0000130A00FF" />
          <!--********.4.255-->
          <OctetString Value="0000130204FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.1.255-->
          <OctetString Value="0000130A01FF" />
          <!--********.0.255-->
          <OctetString Value="0000130200FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.1.255-->
          <OctetString Value="0000130A01FF" />
          <!--********.1.255-->
          <OctetString Value="0000130201FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.1.255-->
          <OctetString Value="0000130A01FF" />
          <!--********.2.255-->
          <OctetString Value="0000130202FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.1.255-->
          <OctetString Value="0000130A01FF" />
          <!--********.3.255-->
          <OctetString Value="0000130203FF" />
          <BitString Value="********" />
        </Structure>
        <Structure Qty="03" >
          <!--*********.1.255-->
          <OctetString Value="0000130A01FF" />
          <!--********.4.255-->
          <OctetString Value="0000130204FF" />
          <BitString Value="********" />
        </Structure>
      </Array>
    </Value>
  </SetRequestNormal>
</SetRequest>