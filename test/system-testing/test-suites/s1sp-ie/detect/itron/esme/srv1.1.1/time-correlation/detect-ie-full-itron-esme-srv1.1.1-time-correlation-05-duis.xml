<?xml version="1.0" encoding="UTF-8"?>
<sr:Request xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:sr="http://www.dccinterface.co.uk/ServiceUserGateway"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0">
    <sr:Header>
        <sr:RequestID>90-B3-D5-1F-30-01-00-00:00-00-00-00-00-00-01-0A:1000</sr:RequestID>
        <sr:CommandVariant>4</sr:CommandVariant>
        <sr:ServiceReference>1.1</sr:ServiceReference>
        <sr:ServiceReferenceVariant>1.1.1</sr:ServiceReferenceVariant>
    </sr:Header>
    <sr:Body>
        <sr:UpdateImportTariffPrimaryElement>
            <sr:ElecTariffElements>
                <sr:CurrencyUnits>GBP</sr:CurrencyUnits>
                <sr:SwitchingTable>
                    <sr:DayProfiles>
                        <sr:DayProfile>
                            <sr:DayName>1</sr:DayName>
                            <sr:ProfileSchedule>
                                <sr:StartTime>00:00:00</sr:StartTime>
                                <sr:TOUTariffAction>3</sr:TOUTariffAction>
                            </sr:ProfileSchedule>
                            <sr:ProfileSchedule>
                                <sr:StartTime>07:00:00</sr:StartTime>
                                <sr:TOUTariffAction>10</sr:TOUTariffAction>
                            </sr:ProfileSchedule>
                        </sr:DayProfile>
                    </sr:DayProfiles>
                    <sr:WeekProfiles>
                        <sr:WeekProfile>
                            <sr:WeekName>1</sr:WeekName>
                            <sr:ReferencedDayName index="1">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="2">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="3">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="4">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="5">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="6">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="7">1</sr:ReferencedDayName>
                        </sr:WeekProfile>
                        <sr:WeekProfile>
                            <sr:WeekName>2</sr:WeekName>
                            <sr:ReferencedDayName index="1">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="2">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="3">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="4">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="5">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="6">1</sr:ReferencedDayName>
                            <sr:ReferencedDayName index="7">1</sr:ReferencedDayName>
                        </sr:WeekProfile>
                    </sr:WeekProfiles>
                    <sr:Seasons>
                        <sr:Season>
                            <sr:SeasonName>winter</sr:SeasonName>
                            <sr:SeasonStartDate>
                                <sr:Year>
                                    <sr:SpecifiedYear>2014</sr:SpecifiedYear>
                                </sr:Year>
                                <sr:Month>
                                    <sr:SpecifiedMonth>10</sr:SpecifiedMonth>
                                </sr:Month>
                                <sr:DayOfMonth>
                                    <sr:SpecifiedDayOfMonth>27</sr:SpecifiedDayOfMonth>
                                </sr:DayOfMonth>
                                <sr:DayOfWeek>
                                    <sr:NonSpecifiedDayOfWeek/>
                                </sr:DayOfWeek>
                            </sr:SeasonStartDate>
                            <sr:ReferencedWeekName>1</sr:ReferencedWeekName>
                        </sr:Season>
                        <sr:Season>
                            <sr:SeasonName>summer</sr:SeasonName>
                            <sr:SeasonStartDate>
                                <sr:Year>
                                    <sr:SpecifiedYear>2015</sr:SpecifiedYear>
                                </sr:Year>
                                <sr:Month>
                                    <sr:SpecifiedMonth>03</sr:SpecifiedMonth>
                                </sr:Month>
                                <sr:DayOfMonth>
                                    <sr:SpecifiedDayOfMonth>29</sr:SpecifiedDayOfMonth>
                                </sr:DayOfMonth>
                                <sr:DayOfWeek>
                                    <sr:NonSpecifiedDayOfWeek/>
                                </sr:DayOfWeek>
                            </sr:SeasonStartDate>
                            <sr:ReferencedWeekName>2</sr:ReferencedWeekName>
                        </sr:Season>
                    </sr:Seasons>
                </sr:SwitchingTable>
                <sr:SpecialDays>
                    <sr:SpecialDay>
                        <sr:Date>
                            <sr:Year>
                                <sr:SpecifiedYear>2015</sr:SpecifiedYear>
                            </sr:Year>
                            <sr:Month>
                                <sr:SpecifiedMonth>05</sr:SpecifiedMonth>
                            </sr:Month>
                            <sr:DayOfMonth>
                                <sr:SpecifiedDayOfMonth>01</sr:SpecifiedDayOfMonth>
                            </sr:DayOfMonth>
                            <sr:DayOfWeek>
                                <sr:NonSpecifiedDayOfWeek/>
                            </sr:DayOfWeek>
                        </sr:Date>
                        <sr:ReferencedDayName>3</sr:ReferencedDayName>
                    </sr:SpecialDay>
                    <sr:SpecialDay>
                        <sr:Date>
                            <sr:Year>
                                <sr:NonSpecifiedYear/>
                            </sr:Year>
                            <sr:Month>
                                <sr:SpecifiedMonth>12</sr:SpecifiedMonth>
                            </sr:Month>
                            <sr:DayOfMonth>
                                <sr:SpecifiedDayOfMonth>25</sr:SpecifiedDayOfMonth>
                            </sr:DayOfMonth>
                            <sr:DayOfWeek>
                                <sr:NonSpecifiedDayOfWeek/>
                            </sr:DayOfWeek>
                        </sr:Date>
                        <sr:ReferencedDayName>3</sr:ReferencedDayName>
                    </sr:SpecialDay>
                </sr:SpecialDays>
                <sr:ThresholdMatrix>
                    <sr:Thresholds index="1">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="2">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="3">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="4">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="5">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="6">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="7">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                    <sr:Thresholds index="8">
                        <sr:BlockThreshold index="1">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="2">4294967295</sr:BlockThreshold>
                        <sr:BlockThreshold index="3">4294967295</sr:BlockThreshold>
                    </sr:Thresholds>
                </sr:ThresholdMatrix>
            </sr:ElecTariffElements>
            <sr:PriceElements>
                <sr:ElectricityPriceElements>
                    <sr:StandingCharge>20000</sr:StandingCharge>
                    <sr:StandingChargeScale>-5</sr:StandingChargeScale>
                    <sr:PriceScale>-5</sr:PriceScale>
                    <sr:TOUTariff>
                        <sr:TOUPrice index="1">50</sr:TOUPrice>
                        <sr:TOUPrice index="3">100</sr:TOUPrice>
                        <sr:TOUPrice index="10">150</sr:TOUPrice>
                        <sr:TOUPrice index="15">200</sr:TOUPrice>
                        <sr:TOUPrice index="17">250</sr:TOUPrice>
                        <sr:TOUPrice index="19">300</sr:TOUPrice>
                        <sr:TOUPrice index="24">350</sr:TOUPrice>
                        <sr:TOUPrice index="28">400</sr:TOUPrice>
                        <sr:TOUPrice index="30">450</sr:TOUPrice>
                        <sr:TOUPrice index="32">500</sr:TOUPrice>
                        <sr:TOUPrice index="37">550</sr:TOUPrice>
                        <sr:TOUPrice index="40">600</sr:TOUPrice>
                        <sr:TOUPrice index="45">700</sr:TOUPrice>
                        <sr:TOUPrice index="48">3000</sr:TOUPrice>
                    </sr:TOUTariff>
                </sr:ElectricityPriceElements>
            </sr:PriceElements>
        </sr:UpdateImportTariffPrimaryElement>
    </sr:Body>
    <ds:Signature>
        <!-- NOTE: The digest, signature and serial number values below are placeholders and should not be used for actual authentication purposes. -->
        <ds:SignedInfo>
            <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/>
            <ds:Reference URI="">
                <ds:Transforms>
                    <ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                </ds:Transforms>
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>ZGVmYXVsdA==</ds:DigestValue>
            </ds:Reference>
        </ds:SignedInfo>
        <ds:SignatureValue>ZGVmYXVsdA==</ds:SignatureValue>
        <ds:KeyInfo>
            <ds:X509Data>
                <ds:X509IssuerSerial>
                    <ds:X509IssuerName>CN=U1, OU=07</ds:X509IssuerName>
                    <ds:X509SerialNumber>1234567890</ds:X509SerialNumber>
                </ds:X509IssuerSerial>
            </ds:X509Data>
        </ds:KeyInfo>
    </ds:Signature>
</sr:Request>