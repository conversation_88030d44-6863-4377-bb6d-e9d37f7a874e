*** Settings ***
Test Timeout  5 minutes
Documentation  Validate the *Detect* implementation on the DLMS commands that translate the *SRV4.4.2*
...            for *Elster* *GSME* (*Retrieve Change Of Mode / Tariff Triggered Billing Data Log*).

Test Tags  S1SP_IE  DCO_DETECT  DCO_ELSTER  DCO_DLMS  DCO_GSME  DCO_SRV4.4.2

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-ie/resource-detect.resource

Suite Setup     Detect Suite Setup
Test Teardown   Detect Test Teardown
Suite Teardown  Detect Suite Teardown

*** Test Cases ***

DETECT-IE-PARTIAL-ELSTER-GSME-SRV4.4.2 - Uncorrelated Request
    [Documentation]  Make requests that do not correlated to this SRV
    ...              - (1) The expected attribute id of 2 is replaced by 1 for the class 8
    ...              - (2) Erroneously use the class id 1 instead of 8
    ...              - (3) Erroneously use the OBIS code 000060030AFF instead of 0000010000FF
    ...              - (4) Use an action request instead of the expected set request
	[Tags]  DCO-3007  DCO_NEGATIVE_TEST  DCO-1508
    [Setup]  Detect Test Setup  uncorrelated/detect-ie-partial-elster-gsme-srv4.4.2-uncorrelated-duis.xml  ElsterGsmeA
    [Template]  Detect Failure
    uncorrelated/detect-ie-partial-elster-gsme-srv4.4.2-uncorrelated-dlms-01.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-elster-gsme-srv4.4.2-uncorrelated-dlms-02.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-elster-gsme-srv4.4.2-uncorrelated-dlms-03.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
    uncorrelated/detect-ie-partial-elster-gsme-srv4.4.2-uncorrelated-dlms-04.hex  ${DETECT_DLMS_REQUEST_CONFIGURATION_NOT_FOUND_ERROR}
