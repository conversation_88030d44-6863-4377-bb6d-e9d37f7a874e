<SetRequest>
  <SetRequestNormal>
    <InvokeIdAndPriority Value="41"/>
    <AttributeDescriptor>
      <ClassId Value="006F"/>
      <InstanceId Value="0001130000FF"/>
      <AttributeId Value="0B"/>
    </AttributeDescriptor>
    <Value>
      <Array Qty="09">
        <Structure Qty="03">
          <OctetString Value="0000130A00FF"/>
          <OctetString Value="0000130200FF"/>
          <BitString Value="111"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A00FF"/>
          <OctetString Value="0000130204FF"/>
          <BitString Value="011"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A00FF"/>
          <OctetString Value="0000130201FF"/>
          <BitString Value="011"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A00FF"/>
          <OctetString Value="0000130202FF"/>
          <BitString Value="011"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A01FF"/>
          <OctetString Value="0000130200FF"/>
          <BitString Value="111"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A02FF"/>
          <OctetString Value="0000130200FF"/>
          <BitString Value="111"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A02FF"/>
          <OctetString Value="0000130204FF"/>
          <BitString Value="011"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A02FF"/>
          <OctetString Value="0000130201FF"/>
          <BitString Value="011"/>
        </Structure>
        <Structure Qty="03">
          <OctetString Value="0000130A02FF"/>
          <OctetString Value="0000130202FF"/>
          <BitString Value="011"/>
        </Structure>
      </Array>
    </Value>
  </SetRequestNormal>
</SetRequest>