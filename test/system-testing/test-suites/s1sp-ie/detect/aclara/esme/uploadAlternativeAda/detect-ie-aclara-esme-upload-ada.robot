*** Settings ***
Test Timeout  5 minutes
Documentation  Validate the *Detect* implementation on the DLMS commands that translate the *SRV1.2.1*
...            for *Aclara* *ESME* (*Update Price (Primary Element)*).

Test Tags  S1SP_IE  DCO_DETECT  DCO_ACLARA  DCO_DLMS  DCO_ESME  DCO_SRV1.2.1

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-ie/resource-detect.resource

Suite Setup     Run Keywords  Detect Suite Setup  AND
                ...  Load Valid ADA File  %{PYTHONPATH}/scenarios/ada-files/ALTERNATIVE_FILE_FOR_TESTING_ADA.csv
Test Teardown   Detect Test Teardown
Suite Teardown  Run Keywords  Detect Suite Teardown  AND
                ...  Load Valid ADA File  %{PYTHONPATH}/scenarios/ada-files/NO_LIMITS_ADA.csv

*** Test Cases ***
DETECT-IE-FULL-ACLARA-ESME-SRV1.2.1 - Success - Price Scale (Equal ADA Limit) - TOU
    [Documentation]  This test will send values for StandingCharge and TOUPriceMatrix equal
    ...    to the most restrictive value defined in ADA file for SRV 1.2.1.
    ...    \\ The DCO Command-Manager is not able to distinguish the service request
    ...       that is being used, only the name of the parameter. If there is more than
    ...       one parameter with the same name, it will be chosen the parameter associated
    ...       with the value that most restrict the StandingCharge or the TouPriceMatrix.
    ...    \\ ADA Alternative values:
    ...      \\ - 1.2.1,StandingChargeElec,Upper,9 |  1.2.1,TariffTOUPriceMatrixElec,Upper,3
    ...    \\ Test Values:
    ...       \\ (dlms-01-01) StandingCharge with value value: 9000 with StandingChargeScale: -3 = 9000*(10^-3) = 9
    ...       \\ (dlms-03-01) TOUPriceMatrix with value: 3000 with PriceScale: -3  = 3000*(10^-3) = 3
    ...    \\ When sending a DUIS for SRV 1.2.1
	[Tags]  DCO-17759  DCO_POSITIVE_TEST  DCO-16947  DCO-7495  DCO-851
    [Setup]  Detect Test Setup  ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-duis-01.xml
    ...      AclaraEsmeA
    [Template]  Detect Success
    ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-dlms-01-01.hex
    ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-dlms-03-01.hex

 #----------------------------------------------------------------------------------------------------

DETECT-IE-FULL-ACLARA-ESME-SRV1.2.1 - Success - Price Scale (Below ADA Limit) - TOU
    [Documentation]  This test will send values for StandingCharge and TOUPriceMatrix
    ...    not higher than the most restrictive value defined in ADA file between SRV 1.2.1.
    ...    \\ The DCO Command-Manager is not able to distinguish the service request
    ...       that is being used, only the name of the parameter. If there is more than
    ...       one parameter with the same name, it will be chosen the parameter associated
    ...       with the value that most restrict the StandingCharge or the TouPriceMatrix.
    ...    \\ ADA Alternative values:
    ...      \\ - 1.2.1,StandingChargeElec,Upper,9 |  1.2.1,TariffTOUPriceMatrixElec,Upper,3
    ...    \\ Test Values:
    ...       \\ (dlms-01-02) StandingCharge with value: 8000 with StandingChargeScale: -3  = 8000*(10^-3) = 8
    ...       \\ (dlms-03-02) TOUPriceMatrix with value: 2000 with PriceScale: -3  = 2000*(10^-3) = 2
    ...    \\ When sending a DUIS for SRV 1.2.1
	[Tags]  DCO-17761  DCO_POSITIVE_TEST  DCO-16947  DCO-7495  DCO-851
    [Setup]  Detect Test Setup  ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-duis-02.xml
    ...      AclaraEsmeA
    [Template]  Detect Success
    ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-dlms-01-02.hex
    ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-tou-dlms-03-02.hex

#----------------------------------------------------------------------------------------------------

DETECT-IE-FULL-ACLARA-ESME-SRV1.2.1 - Failure - Price Scale (Above ADA Limit) - TOU
    [Documentation]  This test will send values for StandingCharge and TOUPriceMatrix higher than
    ...    the most restrictive value defined in ADA file for SRV 1.2.1.
    ...    \\ The DCO Command-Manager is not able to distinguish the service request
    ...       that is being used, only the name of the parameter. If there is more than
    ...       one parameter with the same name, it will be chosen the parameter associated
    ...       with the value that most restrict the StandingCharge or the TouPriceMatrix.
    ...    \\ This test sends a DUIS file in which the values that are being used are equal
    ...       to the values defined in the ADA Limit file. And in which the correlation between
    ...       the DUIS and the DLMS values are according to the expression that is shown in the story DCO-7264.
    ...    \\ ADA Alternative values:
    ...      \\ - 1.2.1,StandingChargeElec,Upper,9 |  1.2.1,TariffTOUPriceMatrixElec,Upper,3
    ...    \\ Test Values:
    ...       \\ (dlms-01-01) StandingCharge = 91 with StandingChargeScale: -1  = 91*(10^-1) = 9.1
    ...       \\ (dlms-03-01) TOUPriceMatrix = 31 with PriceScale: -1  = 31*(10^-1) = 3.1
    ...    \\ When sending a DUIS for SRV 1.2.1
	[Tags]  DCO-17760  DCO_NEGATIVE_TEST  DCO-16947  DCO-7495  DCO-851
    [Setup]  Detect Test Setup  ../srv1.2.1/failure-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-failure-ada-limit-tou-duis-01.xml
    ...   AclaraEsmeA
    [Template]  Detect Failure
    ../srv1.2.1/failure-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-failure-ada-limit-tou-dlms-01-01.hex  ${DETECT_DLMS_CORRELATE_ERROR}
    ../srv1.2.1/failure-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-failure-ada-limit-tou-dlms-03-01.hex  ${DETECT_DLMS_CORRELATE_ERROR}
    

#----------------------------------------------------------------------------------------------------

DETECT-IE-FULL-ACLARA-ESME-SRV1.2.1 - Success - Price Scale (Below and Equal ADA Limit) - BLOCK
    [Documentation]  This test will send a value for BlockPriceMatrix below,equal than
    ...    the most restrictive value defined in ADA file for SRV 1.2.1.
    ...    \\ The DCO Command-Manager is not able to distinguish the service request
    ...       that is being used, only the name of the parameter. If there is more than
    ...       one parameter with the same name, it will be chosen the parameter associated
    ...       with the value that most restrict the BlockPriceMatrix.
    ...    \\ ADA Alternative values:
    ...       \\ - 1.2.1,TariffBlockPriceMatrixElec,Upper,2
    ...    \\ Test Values:
    ...       \\ (dlms-03-01) BlockPriceMatrix = 10000 with PriceScale = -4  = 10000*(10^-4) = 1
    ...       \\ (dlms-03-01) BlockPriceMatrix = 20000 with PriceScale = -4  = 20000*(10^-4) = 2
    ...    \\ When sending a DUIS for SRV 1.2.1
	[Tags]  DCO-17669  DCO_POSITIVE_TEST  DCO-16947  DCO-7495  DCO-851
    [Setup]  Detect Test Setup  ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-block-duis-01.xml
    ...   AclaraEsmeA
    [Template]  Detect Success
    ../srv1.2.1/success-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-success-ada-limit-block-dlms-03-01.hex

#----------------------------------------------------------------------------------------------------

DETECT-IE-FULL-ACLARA-ESME-SRV1.2.1 - Failure - Price Scale (Above ADA Limit) - BLOCK
    [Documentation]  This test will send a value for BlockPriceMatrix higher than
    ...    the most restrictive value defined in ADA file for SRV 1.2.1.
    ...    \\ The DCO Command-Manager is not able to distinguish the service request
    ...       that is being used, only the name of the parameter. If there is more than
    ...       one parameter with the same name, it will be chosen the parameter associated
    ...       with the value that most restrict the BlockPriceMatrix.
    ...    \\ This test sends a DUIS file in which the values that are being used are equal
    ...       to the values defined in the ADA Limit file. And in which the correlation between
    ...       the DUIS and the DLMS values are according to the expression that is shown in the story DCO-7264.
    ...    \\ ADA Alternative values:
    ...       \\ - 1.2.1,TariffBlockPriceMatrixElec,Upper,2
    ...    \\ Test Values:
    ...       \\ (dlms-03-01) BlockPriceMatrix = 21 with PriceScale = -1  = 21*(10^-1) = 2.1
    ...    \\ When sending a DUIS for SRV 1.2.1
	[Tags]  DCO-17670  DCO_NEGATIVE_TEST  DCO-16947  DCO-7495  DCO-851
    [Setup]  Detect Test Setup  ../srv1.2.1/failure-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-failure-ada-limit-block-duis-01.xml
    ...   AclaraEsmeA
    [Template]  Detect Failure
    ../srv1.2.1/failure-ada-limit/detect-ie-full-aclara-esme-srv1.2.1-failure-ada-limit-block-dlms-03-01.hex  ${DETECT_DLMS_CORRELATE_ERROR}