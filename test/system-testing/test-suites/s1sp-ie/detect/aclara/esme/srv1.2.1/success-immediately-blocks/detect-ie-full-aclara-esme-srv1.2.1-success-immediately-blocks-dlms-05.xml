<SetRequest>
    <SetRequestNormal>
        <InvokeIdAndPriority Value="41"/>
        <AttributeDescriptor>
            <ClassId Value="2328"/>
            <InstanceId Value="00003F0101FF"/>
            <AttributeId Value="04"/>
        </AttributeDescriptor>
        <Value>
            <Structure Qty="09">
                <OctetString Value="303030303030303030303030303030303030303030303030"/>
                <Boolean Value="01"/>
                <Boolean Value="01"/>
                <Boolean Value="00"/>
                <Boolean Value="01"/>
                <BitString Value="111111111"/>
                <Boolean Value="01"/>
                <Boolean Value="00"/>
                <Boolean Value="01"/>
            </Structure>
        </Value>
    </SetRequestNormal>
</SetRequest>