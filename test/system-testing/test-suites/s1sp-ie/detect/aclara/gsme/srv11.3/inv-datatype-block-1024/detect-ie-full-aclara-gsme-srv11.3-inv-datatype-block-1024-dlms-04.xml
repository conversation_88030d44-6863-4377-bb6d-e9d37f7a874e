<ActionRequest>
    <ActionRequestNormal>
        <InvokeIdAndPriority Value="41"/>
        <MethodDescriptor>
            <ClassId Value="0012"/>
            <InstanceId Value="00002C0000FF"/>
            <MethodId Value="02"/>
        </MethodDescriptor>
        <MethodInvocationParameters>
            <Structure Qty="02">
                <DoubleLong Value="00000000"/>
                <OctetString Value="257339111ceb8e6c32da6a7c2fe4dc8fd479010c0e9108db8a931595ed01671291d3bea8e9a1e77ce423e031bd4df0a75069560140c5895ef9d75c318c45b39626b97f1c7ab36c5cd56171de1382660a0b8e3f40507836d9701ce538fb2049b140a122af66ec70b6232744271a405961e03970fe9abf9aa35f255130a5c13dbff3c23850ddaaad53f84f6bb4fc8da08ff1b564859c328cfe6da9b94f3f56c7120cc09076090f9bdcb8ab6e765dc793ab42425b207802ec96290e447ff3bf6b2c4f351ee21a8284006efee4b916766f0782e2ba7f1fd9bcc047f1b789cfc05bb656e127d7f7999327d2b05367cc5c53c3e1eb846da5b6207978b924acf6f65e35875b7fb7bbe77f92700da3b80575f98b8011da2de918d6fec122b97c4dadea4030260b3b3abde857439e5a54f759cfedafad7e5cd440182e9e3a52cdec1e33a0423bd322a5c3f487e9c8e8a494127fab7e9d89f11b3fee64b64de4ec6a1a1861372dcca46645ae0b27139cff921a70bf5540be3c2b704c9d5fcf84469a8ac171ce46939586a156794ef9829def2e558062fa6fdf963a1c4f81bd8c43329d831f1c5928eefeb680194c7bee7812db42709447dba5dec3a93d46315982543d3869aa4a70396721579e8263ab05603f91bc105d9bed5a72b55b3ed80da9d940172a8ab3263cfb66c946c944a7d025131dd04604b6b940f8b73f7bd6de39b6425f6c0ed831bd867450d009554bc5b6b97994ad7cea34077fe9f7f6500beb676b28913c68a8f1b24500b0efa4f6e62e2c15d81f24dab7e50c693f59220a7c2955a27a136026504960ab940c0bcf277ed55f441d69d2149bb5db193550d79dec0f60e6c4ee152f1f4b8589c488256e27bacb8d5ad633a2f928586b597783aa09c221e9307dfa68a611cca0d3faf83e6fa472b537d321cc0a5e92bcf1749f1ea33e3ed5a1765441044d3e0cff228503ff570acc416e5629730c3646557e990b8ebd98b5d05ccd7d9b19ee51b30e38226de96d4959da9103d129e19b68e824b2d42920ee45eb5dd7d7f22f80899445e6548d2b2841d986cd3e85976f1238628d4cba6453c526eafdd65a56f2e6790fea33a31565380dee25dd5ca17ee283756f93bc81eba6bcfc57f6414adbcbda581ae9ebe39cc36da940c8c01b86f8ae0c232169b8528d50105c0edc69b4aa049821a14faf9baff981ec0861295d7d66a263c79dc0282d633bc9e60f0e01f672c14a0a86d19e14adf27d832bc2463e35333938f7a1b8e0b9bbd05cbbde396072b927b1428e05175abbbaaef317b7b32fd81c082f22dfa1b0a80c4f6e16e2a347ff7027df8023948965e0146cc51515e84679a8acc4d19404e2281e1eacf104e524f7b5ed884e37f40fae08662b9d604e3a1a5ee7731c1a1f228e991e1c41cd2678a67b9d8dabf9075c95fa1486d9307e1731f217aa4f"/>
            </Structure>
        </MethodInvocationParameters>
    </ActionRequestNormal>
</ActionRequest>