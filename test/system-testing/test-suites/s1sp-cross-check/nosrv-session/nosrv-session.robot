*** Settings ***
Test Timeout  5 minutes
Documentation  Validates that DCO Trilliant services are rejected when getting a context from another S1SP.

Test Tags  S1SP_CROSS_CHECK  DCO-8406

Library  robot-library/dcovv.py

Suite Setup     Trilliant Detect Suite Setup
Suite Teardown  Trilliant Detect Suite Teardown

Resource  robot-resources/s1sp-trilliant/resource-detect.resource
Resource  robot-resources/s1sp-ie/resource-detect.resource

*** Variables ***
${noSrvscenarioDeviceName} =  LgEsmeA
${noSrvscenarioDeviceNameIE} =  AclaraEsmeA
${DEVICE_MODEL} =  E4700000000000000000
${DEVICE_MODELIE} =  118F0520010A00000000
${subsessionIEDeviceID} =  00-00-00-00-00-00-01-0A
${subsessionTriDeviceID} =  00-00-00-00-00-04-01-0A
${clientId} =  0x40
${clientIdTri} =  0x01
*** Test Cases ***
S1SP-CROSS-CHECK-TRILLIANT-NOSRV-SESSION - Failure - Trilliant Session And IE Open Subsession
    [Documentation]    This test verifies the failure to open IE sub session when
    ...             provided an TRILLIANT session ID.
    [Tags]  DCO-12112  DCO_NEGATIVE_TEST  DCO-8423


    ${noSrvsessionId} =  Trilliant Requests To Open NosrvSession  ${noSrvscenarioDeviceName}  ${DEVICE_MODEL}

    ${future}=  Client Sends Async Http Request To  ${scenario}[system-space.Dco.OpenNosrvSubSessionService]
    ...  with method: 	POST
    ...  with header parameters:
    ...     Content-Type: application/json
    ...     sessionId: ${noSrvsessionId}
    ...  with body:
    ...  {
    ...   "meterId": "${subsessionIEDeviceID}",
    ...   "client": "${clientId}"
    ...  }
    Async HTTP Service Replies with Status and Error Codes  ${future}  401  ${COMMAND_MANAGER_DLMS_SESSION_NOT_FOUND_ERROR}

# -------------------------------------------------------------------------------------------------

S1SP-CROSS-CHECK-TRILLIANT-NOSRV-SESSION - Failure - Trilliant Session And IE Close Session
    [Documentation]    This test verifies the failure to close IE session when
    ...             provided an TRILLIANT session ID.
    [Tags]  DCO-12113  DCO_NEGATIVE_TEST  DCO-8423


    ${noSrvsessionId} =  Trilliant Requests To Open NosrvSession  ${noSrvscenarioDeviceName}  ${DEVICE_MODEL}


    ${future}=  Client Sends Async Http Request To  ${scenario}[system-space.Dco.CloseNosrvSessionService]
    ...  with method:  PUT
    ...  with header parameters:
    ...     sessionId: ${noSrvsessionId}
    Async HTTP Service Replies with Status and Error Codes  ${future}  401   ${COMMAND_MANAGER_DLMS_SESSION_NOT_FOUND_ERROR}

# -------------------------------------------------------------------------------------------------

S1SP-CROSS-CHECK-IE-NOSRV-SESSION - Failure - IE Session And Trilliant Close Session
    [Documentation]    This test verifies the failure to close Trilliant session when
    ...             provided an IE session ID.
    [Tags]  DCO-12780  DCO_NEGATIVE_TEST


    ${noSrvsessionId} =  S1SP Requests To Open NosrvSession  ${noSrvscenarioDeviceNameIE}  ${DEVICE_MODELIE}


    ${future}=  Client Sends Async Http Request To  ${scenario}[system-space.Dco.TrilliantCloseNosrvSessionService]
    ...  with method:  PUT
    ...  with header parameters:
    ...     sessionId: ${noSrvsessionId}
    Async HTTP Service Replies with Status and Error Codes  ${future}  401   ${COMMAND_MANAGER_DLMS_SESSION_NOT_FOUND_ERROR}