*** Settings ***
Test Timeout  5 minutes
Documentation  Validate the *Anomaly Detection Attribute* thresholds on the *SRV6.6*

Test Tags  DCO-2978  DCO_ANOMALY_DETECTION  DCO_SRV6.6  DCO-11389

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-common/resource-anomaly-detection.resource

Suite Setup     ADA Suite Setup Scenario
Suite Teardown  ADA Suite Teardown

*** Test Cases ***
ADA-SRV6.6-CalorificValue - Upper
    [Documentation]  This test case verifies that DCO accepts S1SPRequests for SRV6.6 when the 
    ...              value of the DUIS attribute 
    ...              UpdateDeviceConfigurationGasConversion/CalorificValue is equal to
    ...              the upper limit defined in the ADA file.
    ...              \\ A positive value is expected.

    [Tags]  DCO-6134  DCO_POSITIVE_TEST
    [Template]  DUIS with valid ADA value
    CalorificValue/ada-srv6.6-CalorificValue-upper-duis.xml
    ...  GSME

#----------------------------------------------------------------------------------------------------

ADA-SRV6.6-CalorificValue - Normal
    [Documentation]  This test case verifies that DCO accepts S1SPRequests for SRV6.6 when the
    ...              value of the DUIS attribute
    ...              UpdateDeviceConfigurationGasConversion/CalorificValue is below to
    ...              the upper limit defined in the ADA file.
    ...              \\ A positive value is expected.

    [Tags]  DCO-7139  DCO_POSITIVE_TEST
    [Template]  DUIS with valid ADA value
    CalorificValue/ada-srv6.6-CalorificValue-normal-duis.xml
    ...  GSME

 #----------------------------------------------------------------------------------------------------
ADA-SRV6.6-CalorificValue - Overflow
    [Documentation]  This test case verifies that DCO rejects S1SPRequests for SRV6.6 when the
    ...              value of the DUIS attribute 
    ...              UpdateDeviceConfigurationGasConversion/CalorificValue is higher than 
    ...              the upper limit defined in the ADA file.

    [Tags]  DCO-6133  DCO_NEGATIVE_TEST
    [Template]  Send DUIS With Invalid ADA Value To DCO
    CalorificValue/ada-srv6.6-CalorificValue-overflow-duis.xml
    ...  ${REQUEST_MANAGER_ATTRIBUTE_ANOMALY_ERROR}

#----------------------------------------------------------------------------------------------------
ADA-SRV6.6-ConversionFactor - Upper
    [Documentation]  This test case verifies that DCO accepts S1SPRequests for SRV6.6 when the 
    ...              value of the DUIS attribute 
    ...              UpdateDeviceConfigurationGasConversion/ConversionFactor is equal to
    ...              the upper limit defined in the ADA file.
    ...              \\ A positive value is expected.

    [Tags]  DCO-6131  DCO_POSITIVE_TEST
    [Template]  DUIS with valid ADA value
    ConversionFactor/ada-srv6.6-ConversionFactor-upper-duis.xml
    ...  GSME

#----------------------------------------------------------------------------------------------------
ADA-SRV6.6-ConversionFactor - Normal
    [Documentation]  This test case verifies that DCO accepts S1SPRequests for SRV6.6 when the
    ...              value of the DUIS attribute
    ...              UpdateDeviceConfigurationGasConversion/ConversionFactor is below to
    ...              the upper limit defined in the ADA file.
    ...              \\ A positive value is expected.

    [Tags]  DCO-7138  DCO_POSITIVE_TEST
    [Template]  DUIS with valid ADA value
    ConversionFactor/ada-srv6.6-ConversionFactor-normal-duis.xml
    ...  GSME

#----------------------------------------------------------------------------------------------------
ADA-SRV6.6-ConversionFactor - Overflow
    [Documentation]  This test case verifies that DCO rejects S1SPRequests for SRV6.6 when the
    ...              value of the DUIS attribute 
    ...              UpdateDeviceConfigurationGasConversion/ConversionFactor is higher than
    ...              the upper limit defined in the ADA file.
    
    [Tags]  DCO-6132  DCO_NEGATIVE_TEST
    [Template]  Send DUIS With Invalid ADA Value To DCO
    ConversionFactor/ada-srv6.6-ConversionFactor-overflow-duis.xml
    ...  ${REQUEST_MANAGER_ATTRIBUTE_ANOMALY_ERROR}