*** Settings ***
Test Timeout  5 minutes
Documentation  SMKI Importer Test Cases

Test Tags  DCO-7742  DCO-13841  DCO_SMKI_MANAGER

Library  robot-library/dcovv.py
Library  Collections
Library  OperatingSystem
Resource  robot-resources/s1sp-common/resource-common.resource
Resource  robot-resources/s1sp-common/resource-smki-importer.resource

Suite Setup     SMKI Importer Suite Setup
Suite Teardown  SMKI Importer Suite Teardown

Test Setup      SMKI Importer Test Setup
Test Teardown   SMKI Importer Test Teardown


*** Variables ***
${certificates_folder}   %{PYTHONPATH}/scenarios/smki/certificates
${CRL_FOLDER}=           %{PYTHONPATH}/scenarios/smki/crls

*** Test Cases ***

SMKI Importer - Success - Log File Created At Startup
    [Documentation]  Verify that the log file is created at component startup
    [Tags]  DCO-20993  DCO_POSITIVE_TEST  DCO-15689

    Scale Up SMKI Importer And Return Log File Content

    #If the file exists, remove it
    Exec Command  dco-smki-importer  rm logs/smkiimporter.log

    #Restart pod to create a new log file
    SMKI Importer Test Teardown
    ${logFile}=  Scale Up SMKI Importer And Return Log File Content
    Should Not Be Empty  ${logFile}  The container logs have not been created or have no content

    #Retrieve container stdout/terminal logs
    ${logs}=  Wait Until Keyword Succeeds  300 seconds  5 seconds  Get Container Logs  dco-smki-importer
    Should Not Be Empty  ${logs}  The container terminal logs could not be extracted

    #Trim logs to avoid extra prints (Quarkus boot up, JProfiler, etc)
    ${logTerminalTrimmed}=  Trim Log String To First Date Found  ${logs}
    ${logFileTrimmed}=  Trim Log String To First Date Found  ${logFile}

    #Compare log file content with stdout/terminal logs with a limit of 1000 characters
    Should Be Equal As Strings  ${logTerminalTrimmed}[:1000]  ${logFileTrimmed}[:1000]  The log file content does not match the container terminal l>

# -------------------------------------------------------------------------------------------------
SMKI Importer - Success FULL
    [Documentation]  Tests that if no FULL file has been processed, a FULL file is chosen
    ...              and the DELTA files are not processed.
    ...              \\ The table smki_file_history is empty. So the FULL file is mandatory.
    [Tags]  DCO-8549  DCO_POSITIVE_TEST  DCO-14052  DCO-15873
    # DCCDCOPT-91 AC2

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  FULL  ${date}
    Assert Successful Execution In Log  FULL

# -------------------------------------------------------------------------------------------------

SMKI Importer - Last DELTA Processed Eight Days Ago
    [Documentation]  Tests that if the last processed DELTA is older than the dates range defined
    ...              in the properties, a FULL file is chosen and the DELTA files are not processed.
    ...              \\ The date of the last exectution is out of the defined range.So the FULL file is mandatory.
    ...              \\ Defined range ::: smkir.execution.expectedAmountOfDeltaFiles = 6
    [Tags]  DCO-8548  DCO_POSITIVE_TEST  DCO-14052
    # DCCDCOPT-91 AC3

    ${date_before_eight_days}=  Increment Date  ${date}  -8 days

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_eight_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Mandatory Files - FULL
    [Documentation]  Tests that the SMKI Importer throws an exception if the FULL
    ...              file is missing in the SFTP server and the SMKI Importer has never been executed.
    ...              \\ The table smki_file_history is empty. So the FULL file is mandatory.
    [Tags]  DCO-8564  DCO_NEGATIVE_TEST  DCO-14052
    # DCCDCOPT-91 AC1

    ${files}=  Create List
#    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files  ${files}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - First DELTA
    [Documentation]  Tests that the SMKI Importer throws an exception if the DELTA
    ...              file from six days ago is missing in the SFTP server.
    ...              \\ The DELTA File from six days it's missing, for that reason it's expected
    ...              that none of the FULL or DELTA files will be processed.
    [Tags]  DCO-8554  DCO_NEGATIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files
    ...  ${files}
    ...  database_history_is_empty=${False}
    ...  expected_crls=${1}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_DELTA_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - Last DELTA
    [Documentation]  Tests that the SMKI Importer throws an exception if the DELTA
    ...              file for the current date is missing in the SFTP server.
    ...              \\ Also validates that the DELTA files from the previous dates (6,5,4,3,2,1 days ago) are processed.
    [Tags]  DCO-8550  DCO_NEGATIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}
    Run SMKI Importer

#--------------

     ${existing_certificates_list}=  Create List
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}

#--------------

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

#--------------

    Test Expected Certificates Files
    ...  create_files_and_run_smki=${False}
    ...  database_history_is_empty=${False}
    ...  expected_certificates=${certificates_in_files}
    ...  expected_crls=${1}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_DELTA_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - Middle DELTA
    [Documentation]  Tests that the SMKI Importer throws an exception if the DELTA
    ...              file from three days ago is missing in the SFTP server.
    ...              \\ It's expectecd that only the DELTA files from 6,5 and 4 days ago will be processed.
    [Tags]  DCO-8563  DCO_NEGATIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}
    Run SMKI Importer

#--------------

    ${existing_certificates_list}=  Create List
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}

#--------------

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

#--------------

    Test Expected Certificates Files
    ...  create_files_and_run_smki=${False}
    ...  database_history_is_empty=${False}
    ...  expected_certificates=${certificates_in_files}
    ...  expected_crls=${1}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_DELTA_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - Several DELTAs
    [Documentation]  Tests that the SMKI Importer throws an exception if some DELTA
    ...              files are missing in the SFTP server.
    ...              \\ It's expected that only the DELTA file from six days ago will be processed.
    [Tags]  DCO-8559  DCO_NEGATIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}
    Run SMKI Importer

#--------------

    ${existing_certificates_list}=  Create List
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}

#--------------

     ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

#--------------

    Test Expected Certificates Files
    ...  create_files_and_run_smki=${False}
    ...  database_history_is_empty=${False}
    ...  expected_certificates=${certificates_in_files}
    ...  expected_crls=${1}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_DELTA_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - ARL
    [Documentation]  Tests that the SMKI Importer throws an exception if there is no
    ...              ARL file in the SFTP server.
    ...              \\ It's expected that none of the files will be processed.
    [Tags]  DCO-8541  DCO_NEGATIVE_TEST
    # DCCDCOPT-91 AC1

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
#    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files  ${files}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - CRL
    [Documentation]  Tests that the SMKI Importer throws an exception if there is no
    ...              CRL file in the SFTP server.
    ...              \\ It's expected that none of the files will be processed.
    [Tags]  DCO-8560  DCO_NEGATIVE_TEST
    # DCCDCOPT-91 AC1

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files  ${files}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - DELTA With Seven Days
    [Documentation]  Tests that the SMKI Importer verifies the expected files by
    ...              filename and not by the number of files.
    ...              \\ Seven DELTA files are available in the SFTP, but one of the files has more than seven days,
    ...              and one of the expected files is missing.
    ...              \\ It's expected that only the DELTA files from 6,5,4,3,2,1 days ago will be processed.
    ...              \\ In this test the SMKI Importer should throw an exception.
    [Tags]  DCO-8553  DCO_NEGATIVE_TEST
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${certificates}=  Create List   ${certificates_folder}/root/ca-revoked/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root/ca1/ca2/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/reporting-party/cert.der

    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_DELT_${date_before_seven_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
#    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_seven_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}
    Run SMKI Importer

#--------------

    ${existing_certificates_list}=  Create List
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}

#--------------

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_delta_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

#--------------

    Test Expected Certificates Files
    ...  create_files_and_run_smki=${False}
    ...  database_history_is_empty=${False}
    ...  expected_certificates=${certificates_in_files}
    ...  expected_crls=${1}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_DELTA_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Missing Expected Files - FULL From Previous Day
    [Documentation]  Tests that the SMKI Importer verifies the expected files by
    ...              filename and not by the number of files.
    ...              \\ The FULL file available in the SFTP server is from the previous day,
    ...              and the FULL file from the current day is missing. So the FULL file is mandatory.
    ...              \\ In this test the SMKI Importer should throw an exception.
    [Tags]  DCO-8547  DCO_NEGATIVE_TEST  DCO-14052
    # DCCDCOPT-91 AC1

    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  2
    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${date_before_seven_days}    Convert Date    ${date_before_seven_days}    result_format=%-m/%-d/%y    date_format=%Y-%m-%d

    ${certificates}=  Create List   ${certificates_folder}/root/ca-revoked/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root/ca1/ca2/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/reporting-party/cert.der

    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date_before_three_days}

    ${files}=  Create List
    #...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_FULL_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    # The table smki_file_history only has the dummy record inserted in beginning of the test execution
    # that's why the value of 'database_history_is_empty' is set to ${False}
    Test Expected Certificates Files
    ...  ${files}
    ...  database_history_is_empty=${False}
    ...  error_code_message=${SMKIE101_ERROR_EXTRA_FULL_FILE_SINCE} ${date_before_seven_days}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Extra FULL File
    [Documentation]  Tests that the SMKI Importer verifies the expected files by
    ...              filename and not by the number of files.
    ...              \\ All the expected files are created and available in the SFTP server.
    ...              \\ An extra FULL file with a date within the expected files dates range is available in the SFTP server.
    ...              \\ So the FULL file is mandatory.
    ...              \\ As there is an extra file, the SMKI Importer should throw an exception.
    [Tags]  DCO-8562  DCO_NEGATIVE_TEST  DCO-14052
    # DCCDCOPT-91 AC1

    ${certificates}=  Create List   ${certificates_folder}/root/ca-revoked/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root/ca1/ca2/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/reporting-party/cert.der

    ${date_after_one_day}=  Increment Date  ${date}  1 day
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date_after_one_day}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_FULL_${date_after_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files
    ...  ${files}
    ...  error_code_message=${SMKIE101_ERROR_EXTRA_FULL_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Extra FULL File (Date Outside Dates Range)
    [Documentation]  Tests that the SMKI Importer verifies the expected files by
    ...              filename and not by the number of files.
    ...              \\ All the expected files are created and available in the SFTP server.
    ...              \\ An extra FULL file with a date outside the expected files dates range is available in the SFTP server.
    ...              \\ As there is an extra file, the SMKI Importer should throw an exception.
    [Tags]  DCO-17791  DCO_NEGATIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC1

    ${certificates}=  Create List   ${certificates_folder}/root/ca-revoked/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root/ca1/ca2/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/reporting-party/cert.der

    ${date_after_one_day}=  Increment Date  ${date}  1 day
    ${date_before_thirteen_days}=  Increment Date  ${date}  -13 day
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date_after_one_day}
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date_before_thirteen_days}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date_before_thirteen_days}.xml.gz
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files
    ...  ${files}
    ...  error_code_message=${SMKIE101_ERROR_EXTRA_FULL_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Extra DELTA File
    [Documentation]  Tests that the SMKI Importer verifies the expected files by
    ...              filename and not by the number of files.
    ...              \\ All the expected files are created and available in the SFTP server.
    ...              \\ An extra DELTA file with a date outside the expected files range is available in the SFTP server.
    ...              \\ As there is an extra file, the SMKI Importer should execute without any error,
    ...                 and the extra DELTA file should not be processed.
    [Tags]  DCO-8552  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC1

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    ${certificates}=  Create List   ${certificates_folder}/root/ca-revoked/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root/ca1/ca2/ee/reporting-party/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/reporting-party/cert.der

    ${date_after_one_day}=  Increment Date  ${date}  1 day
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_DELT_${date_after_one_day}
    ${date_after_delta_after_one_day_certificates}  Set Variable  ${certificates}

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_after_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl    # CN=DCO_ROOT,OU=00
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl      # CN=DCO_CA,OU=07

    Create Mandatory Certificate Files

    Upload Files To Server  ${files}
    Run SMKI Importer
#---------
    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}
#---------
    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_after_delta_after_one_day_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
#---------
    ${expected_CRLs_dic_arl}  Create Dictionary  crl_entry=CN=DCO_ROOT,OU=00  file_type=ARL  file_name=ARL_DCO_ROOT.arl
    ${expected_CRLs_dic_crl}  Create Dictionary  crl_entry=DCO_CA,OU=07  file_type=CRL  file_name=CRL_DCO_CA.crl
    ${expected_crls_entries_list}  Create List
    ...  ${expected_CRLs_dic_arl}
    ...  ${expected_CRLs_dic_crl}

    Assert That Database Contains CRLs Entries  ${expected_crls_entries_list}
    Assert That Database History Contains CRLs Entries  ${expected_crls_entries_list}
#---------
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA
    Assert Log Output Execution  The SFTP server contains 1 FULL file(s), 8 DELTA file(s), 2 Revocation List File(s).
    Assert Successful Execution In Log  DELTA file(s) for the last 7 day(s) need to be executed

# -------------------------------------------------------------------------------------------------

SMKI Importer - No Valid ARL
    [Documentation]  Tests that the SMKI Importer verifies the validity of the ARL files before
    ...              add it to the database.
    ...              \\ The ARL file available in the SFTP server is invalid.
    ...              \\ In this test the SMKI Importer should throw an exception.
    [Tags]  DCO-8555  DCO_NEGATIVE_TEST
    # DCCDCOPT-91 AC1

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT_UNTRUSTED.arl  #Invalid ARL
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

    Number Of CRLs In Database Is  1
    Assert That Database History Contains Entry  FULL  ${date}
    Assert That Execution Log Contains Error Code  Error SMKIE105
    Assert That The FULL File Execution Was Successful
    Assert That Revocation List Import Execution Was Successful

# -------------------------------------------------------------------------------------------------

SMKI Importer - No Valid CRL
    [Documentation]  Tests that the SMKI Importer verifies the validity of the CRL files before
    ...              add them to the database.
    ...              \\ None of the  CRL files available in the SFTP server is invalid.
    ...              \\ In this test the SMKI Importer should throw an exception.
    [Tags]  DCO-8557  DCO_NEGATIVE_TEST
    # DCCDCOPT-91 AC1

    ${files}=  Create List
    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA_EXPIRED.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA_INVALID_SIGNATURE.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA_MISSING.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA_REVOKED.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA1.crl
#    ...                     ${CRL_FOLDER}/CRL_DCO_CA2.crl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA_SS.crl
    ...                     ${CRL_FOLDER}/CRL_DCO_ROOT_UNTRUSTED_CA.crl

    Create Mandatory Certificate Files
    Upload Files To Server  ${files}

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}

    Number Of CRLs In Database Is  1  #The ARL file content should be in the database
    Assert That Database History Contains Entry  FULL  ${date}
    Assert That Execution Log Contains Error Code  Error SMKIE107
    Assert That The FULL File Execution Was Successful
    Assert That Revocation List Import Execution Was Successful

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last FULL Seven Days Ago
    [Documentation]  Tests that if the last processed FULL file was at seven days ago, the DELTA files are chosen
    ...              to be processed sequentialy since the next day from history date.
    [Tags]  DCO-8556  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC4

    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  FULL  ${date_before_seven_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${date_full_certificates}
    Assert That Database Does Not Contains Certificates  ${date_full_certificates}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last DELTA Seven Days Ago
    [Documentation]  Tests that if the last processed DELTA file was at seven days ago, the DELTA files are chosen
    ...              to be processed sequentialy since the next day from history date.
    [Tags]  DCO-8543  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC4

    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${date_full_certificates}
    Assert That Database Does Not Contains Certificates  ${date_full_certificates}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last FULL Four Days Ago
    [Documentation]  Tests that if the last processed FULL file was at four days ago, the DELTA files are chosen
    ...              to be processed sequentialy since the next day from history date.
    [Tags]  DCO-8561  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC4

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  FULL  ${date_before_four_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last DELTA Four Days Ago
    [Documentation]  Tests that if the last processed DELTA file was at four days ago, the DELTA files are chosen
    ...              to be processed sequentialy since the next day from history date.
    [Tags]  DCO-8544  DCO_POSITIVE_TEST  DCO-16960
    # DCCDCOPT-91 AC4

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_four_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last FULL Yesterday
    [Documentation]  Tests that if the last processed FULL file was yesterday, only the DELTA file for today
    ...              is chosed to be processed.
    [Tags]  DCO-8558  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC4

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  FULL  ${date_before_one_day}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_file}=  Get Length  ${date_delta_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_delta_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Success - Last DELTA Yesterday
    [Documentation]  Tests that if the last processed DELTA file was yesterday, only the DELTA file for today
    ...              is chosed to be processed.
    [Tags]  DCO-8545  DCO_POSITIVE_TEST  DCO-15873  DCO-16960
    # DCCDCOPT-91 AC4

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_file}=  Get Length  ${date_delta_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_delta_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Repeat Successful Execution
    [Documentation]  Tests that if the processing has been conducted this day with success, then the processing
    ...              cannot be repeated this day.
    [Tags]  DCO-8566  DCO_POSITIVE_TEST
    # DCCDCOPT-91 AC8

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_file}=  Get Length  ${date_delta_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_delta_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

    #----------------END OF FIRST RUN----------------

    ${initial_certificates}=  Query  SELECT * FROM smki_certificates ORDER BY serial_number
    ${initial_revocation_lists}=  Query  SELECT issuer, authority_key_identifier, last_change_reason, crl FROM smki_revocation_lists ORDER BY issuer
    ${initial_file_history}=  Query  SELECT file_type, file_name FROM smki_file_history ORDER BY file_name

    Run SMKI Importer

    Should Contain  ${execution_log}  DELTA file(s) for the last 0 day(s) need to be executed
    Should Contain  ${execution_log}  DELTA file(s)${SPACE*2}are going to be executed

    ${final_certificates}=  Query  SELECT * FROM smki_certificates ORDER BY serial_number
    ${final_revocation_lists}=  Query  SELECT issuer, authority_key_identifier, last_change_reason, crl FROM smki_revocation_lists ORDER BY issuer
    ${final_file_history}=  Query  SELECT file_type, file_name FROM smki_file_history ORDER BY file_name

    Should Be Equal  ${initial_certificates}  ${final_certificates}
    Should Be Equal  ${initial_revocation_lists}  ${final_revocation_lists}
    Should Be Equal  ${initial_file_history}  ${final_file_history}

# -------------------------------------------------------------------------------------------------

SMKI Importer - CertificateBody With White Spaces
    [Documentation]  Tests that SMKI Importer will not fail if there are white spaces in the CertificateBody
    ...  XML element of the imported files.
    [Tags]  DCO-15108  DCO_POSITIVE_TEST

    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_seven_days}

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    Remove File  test-files/SMKIKR_DELT_${date_before_one_day}.xml

    # The content of this file matches the certificates for @{date_before_one_day_certificates}
    Copy File  support-files/certificate-with-spaces.gz  test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz

    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${date_full_certificates}
    Assert That Database Does Not Contains Certificates  ${date_full_certificates}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA
    Assert That All Certificates Have The Expected Values   ${existing_certificates_list}

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL With Pending Status
    [Documentation]  Tests that SMKI Importer expects only "In Use" certificates in the FULL file.
    ...              \\ The FULL file will contain certificates with Pending status,
    ...              and the SMKI Importer should throw an exception
    [Tags]  DCO-8565  DCO_NEGATIVE_TEST
    # DCCDCOPT-96 AC1.1

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml

    # Create a FULL file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/supplier/cert.der                    status=I
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der               status=I
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der     status=I
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der               status=I
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der               status=P
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der                    status=P

    ${certificates}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date}

    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE103
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL With Not In Use Status
    [Documentation]  Tests that SMKI Importer expects only "In Use" certificates in the FULL file.
    ...              \\ The FULL file will contain certificates with "Not In Use" status,
    ...              and the SMKI Importer should throw an exception
    [Tags]  DCO-8539  DCO_NEGATIVE_TEST
    # DCCDCOPT-96 AC1.1

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml

    # Create a FULL file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/supplier/cert.der                    status=I
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der               status=I
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der     status=I
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der               status=I
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der               status=N
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der                    status=N

    ${certificates}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date}

    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE103
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL With Expired Status
    [Documentation]  Tests that SMKI Importer expects only "In Use" certificates in the FULL file.
    ...              \\ The FULL file will contain certificates with "Expired" status,
    ...              and the SMKI Importer should throw an exception
    [Tags]  DCO-8551  DCO_NEGATIVE_TEST
    # DCCDCOPT-96 AC1.1

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml

    # Create a FULL file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/supplier/cert.der                    status=I
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der               status=I
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der     status=I
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der               status=I
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der               status=E
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der                    status=E

    ${certificates}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date}

    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE103
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL With Revoked Status
    [Documentation]  Tests that SMKI Importer expects only "In Use" certificates in the FULL file.
    ...              \\ The FULL file will contain certificates with "Revoked" status,
    ...              and the SMKI Importer should throw an exception
    [Tags]  DCO-8568  DCO_NEGATIVE_TEST
    # DCCDCOPT-96 AC1.1

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml

    # Create a FULL file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/supplier/cert.der                    status=I
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der               status=I
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der     status=I
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der               status=I
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der               status=R
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der                    status=R

    ${certificates}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}
    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_FULL_${date}

    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE103
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - DELTA With Other Status
    [Documentation]  Tests that SMKI Importer do not add to database certificates with status
    ...              Pending, Not in use, Revoked and Expired from a DELTA file.
    [Tags]  DCO-8570  DCO_POSITIVE_TEST
    # DCCDCOPT-96 AC1.1

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml
    Remove File  test-files/SMKIKR_DELT_${date}.xml.gz
    Remove File  test-files/SMKIKR_DELT_${date}.xml

        # Create a DELTA file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/supplier/cert.der                    status=I
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der               status=I
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der     status=I
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der               status=I
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der               status=P
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der                    status=P
    ${cert7}=  Create Certificate Response From File  ${certificates_folder}/root/ca-expired/ee/supplier/cert.der               status=N
    ${cert8}=  Create Certificate Response From File  ${certificates_folder}/root/ca-invalid-signature/ee/supplier/cert.der     status=N
    ${cert9}=  Create Certificate Response From File  ${certificates_folder}/root/ca-missing/ee/supplier/cert.der               status=E
    ${cert10}=  Create Certificate Response From File  ${certificates_folder}/root/ca-revoked/ee/supplier/cert.der              status=E
    ${cert11}=  Create Certificate Response From File  ${certificates_folder}/root/ca1/ca2/ee/supplier/cert.der                 status=R
    ${cert12}=  Create Certificate Response From File  ${certificates_folder}/root-untrusted/ca/ee/supplier/cert.der            status=R
    ${cert13}=  Create Certificate Response From File  ${certificates_folder}/root/ca/cert.der
    ${cert14}=  Create Certificate Response From File  ${certificates_folder}/root/ca1/cert.der
    ${cert15}=  Create Certificate Response From File  ${certificates_folder}/root/ca-expired/cert.der
    ${cert16}=  Create Certificate Response From File  ${certificates_folder}/root/ca-missing/cert.der
    ${cert17}=  Create Certificate Response From File  ${certificates_folder}/root/ca1/ca2/cert.der
    ${cert18}=  Create Certificate Response From File  ${certificates_folder}/root/ca-invalid-signature/cert.der
    ${cert19}=  Create Certificate Response From File  ${certificates_folder}/root/ca-revoked/cert.der
    ${cert20}=  Create Certificate Response From File  ${certificates_folder}/root-b/ca-b/cert.der

    ${certificates_delta}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}  ${cert7}  ${cert8}
    ...    ${cert9}   ${cert10}   ${cert11}  ${cert12}  ${cert13}  ${cert14}  ${cert15}  ${cert16}  ${cert17}
    ...    ${cert18}   ${cert19}   ${cert20}

    Create Certificate Data Response File  ${certificates_delta}  test-files/SMKIKR_DELT_${date}

    ${certificates_full}=  Create List   ${certificates_folder}/root/ca/ee-db/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/ee-expired/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/ee-invalid-signature/acb-xml-signing/cert.der

    Create Certificate Data Response File  ${certificates_full}  test-files/SMKIKR_FULL_${date}

    Upload Mandatory Files To Server

    Insert History Record  DELTA  ${date_before_one_day}

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  ${certificates_folder}/root/ca/ee-db/supplier/cert.der
    ...  ${certificates_folder}/root/ca/ee-expired/supplier/cert.der
    ...  ${certificates_folder}/root/ca/ee-invalid-signature/supplier/cert.der
    ...  ${certificates_folder}/root/ca/ee-missing/supplier/cert.der
    ...  ${certificates_folder}/root/ca/cert.der
    ...  ${certificates_folder}/root/ca1/cert.der
    ...  ${certificates_folder}/root/ca-expired/cert.der
    ...  ${certificates_folder}/root/ca-missing/cert.der
    ...  ${certificates_folder}/root/ca1/ca2/cert.der
    ...  ${certificates_folder}/root/ca-invalid-signature/cert.der
    ...  ${certificates_folder}/root/ca-revoked/cert.der
    ...  ${certificates_folder}/root-b/ca-b/cert.der

    ${certificates_in_file}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    # Append certificates from DELTA file with other status
    Append To List  ${non_existing_certificates_list}
    ...  ${certificates_folder}/root/ca/ee-revoked/supplier/cert.der
    ...  ${certificates_folder}/root/ca/ee-ws/supplier/cert.der
    ...  ${certificates_folder}/root/ca-expired/ee/supplier/cert.der
    ...  ${certificates_folder}/root/ca-invalid-signature/ee/supplier/cert.der
    ...  ${certificates_folder}/root/ca-missing/ee/supplier/cert.der
    ...  ${certificates_folder}/root/ca-revoked/ee/supplier/cert.der
    ...  ${certificates_folder}/root/ca1/ca2/ee/supplier/cert.der
    ...  ${certificates_folder}/root-untrusted/ca/ee/supplier/cert.der

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Certificates With New Status
    [Documentation]  Tests that the SMKI Importer removes certificates from the database,
    ...              that have other status than "In Use" in a DELTA file.
    [Tags]  DCO-8540  DCO_POSITIVE_TEST

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    # This first run will add "In Use" certificates to the database
    Run SMKI Importer

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_file}=  Get Length  ${date_delta_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_delta_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

    #----------------END OF FIRST RUN----------------

    # Remove history from prevous run
    Delete All Rows From Table  smki_file_history

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    # Create a DELTA file with certificates status different than In Use
    ${cert1}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/acb-xml-signing/cert.der                 status=P
    ${cert2}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-expired/acb-xml-signing/cert.der            status=N
    ${cert3}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-db/xml-signing/cert.der                     status=N
    ${cert4}=  Create Certificate Response From File  ${certificates_folder}/root/ca/ee-invalid-signature/acb-xml-signing/cert.der  status=E
    ${cert5}=  Create Certificate Response From File  ${certificates_folder}/root-untrusted/ca/ee/supplier/cert.der                 status=R
    ${cert6}=  Create Certificate Response From File  ${certificates_folder}/root/ca/cert.der                                       status=I
    ${cert7}=  Create Certificate Response From File  ${certificates_folder}/root/ca1/cert.der                                      status=I
    ${cert8}=  Create Certificate Response From File  ${certificates_folder}/root/ca1/ca2/cert.der
    ${cert9}=  Create Certificate Response From File  ${certificates_folder}/root/ca-expired/cert.der
    ${cert10}=  Create Certificate Response From File  ${certificates_folder}/root/ca-invalid-signature/cert.der
    ${cert11}=  Create Certificate Response From File  ${certificates_folder}/root/ca-missing/cert.der
    ${cert12}=  Create Certificate Response From File  ${certificates_folder}/root/ca-revoked/cert.der
    ${cert13}=  Create Certificate Response From File  ${certificates_folder}/root-b/ca-b/cert.der

    ${certificates_delta}=  Create List  ${cert1}  ${cert2}  ${cert3}  ${cert4}  ${cert5}  ${cert6}
    ...  ${cert7}  ${cert8}  ${cert9}  ${cert10}  ${cert11}  ${cert12}  ${cert13}

    Create Certificate Data Response File  ${certificates_delta}  test-files/SMKIKR_DELT_${date}

    # Replace the file that conatins the same certificates but with other status
    Upload File To Server  test-files/SMKIKR_DELT_${date}.xml.gz

    Run SMKI Importer

     ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    # Append certificates from DELTA file with other status
    Append To List  ${non_existing_certificates_list}
    ...  ${certificates_folder}/root/ca/ee-db/acb-xml-signing/cert.der
    ...  ${certificates_folder}/root/ca/ee-expired/acb-xml-signing/cert.der
    ...  ${certificates_folder}/root/ca/ee-invalid-signature/acb-xml-signing/cert.der
    ...  ${certificates_folder}/root-untrusted/ca/ee/supplier/cert.der

    ${existing_certificate_list}=  Create List
    ...  ${certificates_folder}/root/ca/cert.der
    ...  ${certificates_folder}/root/ca1/cert.der
    ...  ${certificates_folder}/root/ca1/ca2/cert.der
    ...  ${certificates_folder}/root/ca-expired/cert.der
    ...  ${certificates_folder}/root/ca-invalid-signature/cert.der
    ...  ${certificates_folder}/root/ca-missing/cert.der
    ...  ${certificates_folder}/root/ca-revoked/cert.der
    ...  ${certificates_folder}/root-b/ca-b/cert.der

    ${certificates_in_file}=  Get Length  ${existing_certificate_list}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${existing_certificate_list}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Validate Certificate Elements In Database
    [Documentation]  Tests that the Certificate elements persisted in Data Base by the SMKI Importer
    ...              are as expected.
    ...              \\ The tests expects only expects that the DELTA files will be processed ignoring the FULL file.
    ...              \\ This period does not include the current day.
    ...                 \\  smkir.execution.expectedAmountOfDeltaFiles = 7
    [Tags]  DCO-8567  DCO_POSITIVE_TEST

    # The SMKI Importer will only process a maximum of 7 DELTA files
    Update SMKI Importer Config  SMKIR_EXECUTION_EXPECTEDAMOUNTOFDELTAFILES  7

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  FULL  ${date_before_seven_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    #...  @{date_full_certificates}
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${date_full_certificates}
    Assert That Database Does Not Contains Certificates  ${date_full_certificates}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA file(s) for the last 0 day(s) need to be executed
    Assert Successful Execution In Log  DELTA
    Assert That All Certificates Have The Expected Values   ${existing_certificates_list}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Empty GZ File
    [Documentation]  Tests that if for some reason the GZ file transfered from the SFTP Server is empty,
    ...              the SMKI Importer throws an exception.
    [Tags]  DCO-8546  DCO_NEGATIVE_TEST

    Create Mandatory Certificate Files
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Create File  test-files/SMKIKR_FULL_${date}.xml.gz  ${EMPTY}
    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE005
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - Empty XML File
    [Documentation]  Tests that if for some reason the XML file inside the GZ file transfered from the SFTP Server
    ...              is empty, the SMKI Importer throws an exception.
    [Tags]  DCO-8569  DCO_NEGATIVE_TEST

    Create Mandatory Certificate Files
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Create File  test-files/SMKIKR_FULL_${date}.xml  ${EMPTY}
    Run  gzip -k -f test-files/SMKIKR_FULL_${date}.xml
    Upload Mandatory Files To Server

    Run SMKI Importer

    Assert That Execution Log Contains Error Code  Error SMKIE000 - Internal Error: (ParseError at|Unexpected EOF in prolog\\n at) \\[row,col.*\\]:.*\\[1,(0|1)\\]
    Number Of Certificates In Database Is  0
    Number Of CRLs In Database Is  0
    Assert That Database History Is Empty

# -------------------------------------------------------------------------------------------------

SMKI Importer - Invalid XML File
    [Documentation]  Tests that if the XML file inside the GZ file transfered from the SFTP Server
    ...              has invalid XML structure, the SMKI Importer throws an exception and doesn't change the database
    ...              content.
    ...              \\ The SMKI Importer runs with files containing no errors.
    ...              \\ In the second run an invalid XML file will be used, and no changes are expected in the database.
    [Tags]  DCO-13907  DCO_NEGATIVE_TEST

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  FULL  ${date}
    Assert Successful Execution In Log  FULL

    # End of the initial successfull execution

    Delete All Rows From Table  smki_file_history
    Delete File On Server  SMKIKR_FULL_${date}.xml.gz

    Remove File  test-files/SMKIKR_FULL_${date}.xml
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz

    Copy File  support-files/invalid-xml.xml.gz  test-files/SMKIKR_FULL_${date}.xml.gz

    Upload File To Server  test-files/SMKIKR_FULL_${date}.xml.gz

    Run SMKI Importer

    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Is Empty
    Assert That Execution Log Contains Error Code  Error SMKIE000 - Internal Error: (ParseError at|Unexpected close tag <\\/CertificateResponse>; expected <\\/DummyTag>.\\n at) \\[row,col.*\\]:.*\\[13,(5|23)\\]

# -------------------------------------------------------------------------------------------------

SMKI Importer - Self Signed Certificates
    [Documentation]  Tests that if the file being processed contains a self signed certificate,
    ...              then the SMKI Importer ignores such certificate and continue with the normalexecution.
    [Tags]  DCO-8542  DCO_NEGATIVE_TEST

    Create Mandatory Certificate Files

    Remove File  test-files/SMKIKR_DELT_${date}.xml
    Remove File  test-files/SMKIKR_DELT_${date}.xml.gz

    # Create a DELTA file with self signed certificates
    ${certificates}=  Create List   ${certificates_folder}/root/ca/ee-db/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/ee-expired/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/ee-invalid-signature/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/cert.der
    ...                             ${certificates_folder}/ca-ss/cert.der
    ...                             ${certificates_folder}/ee-self-signed/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/ee-self-signed/supplier/cert.der

    Create Certificate Data Response File  ${certificates}  test-files/SMKIKR_DELT_${date}

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Upload Mandatory Files To Server

    Run SMKI Importer

    ${valid_certificates_list}=  Get Slice From List  ${certificates}  0  4
    ${self_signed_certificates}=  Get Slice From List  ${certificates}  4  7

    ${certificates_in_list}=  Get Length  ${valid_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_list}
    Assert That Database Contains Certificates  ${valid_certificates_list}
    Assert That Database Does Not Contains Certificates  ${self_signed_certificates}

    Number Of CRLs In Database Is  3
    ${issuers}=  Read Issuers Of Revocation Lists From Database

    List Should Contain Value  ${issuers}  CN=DCO_ROOT,OU=00                #ARL_DCO_ROOT.arl
    List Should Contain Value  ${issuers}  CN=DCO_ROOT_B,OU=00                #ARL_DCO_ROOT_B.arl
    List Should Contain Value  ${issuers}  CN=DCO_CA,OU=07                  #CRL_DCO_CA.crl

    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Wrong Configs
    [Documentation]  Tests wrong configuration parameters in the application.properties file
    ...              and expects an error message to be logged.
    [Tags]  DCO-8749  DCO_NEGATIVE_TEST
    [Timeout]   20 min
    [Setup]  SMKI Importer Test Setup

    [Template]  Test Wrong Configuration

    SFTP_CLIENT_USERNAME                dummy                   Error SMKIE003 - Failed SFTP authentication
    SFTP_CLIENT_PASSWORD                dummy                   Error SMKIE003 - Failed SFTP authentication
    SFTP_CLIENT_PUBLICKEY               /keys/test-ssh.pub      Error SMKIE002 - Failed SFTP connection
    SFTP_CLIENT_HOST                    *************           Error SMKIE002 - Failed SFTP connection
    SFTP_CLIENT_PORT                    12345                   Error SMKIE002 - Failed SFTP connection
    SFTP_CLIENT_SFTPFOLDER              /dummy-folder           Error SMKIE000
    SMKIR_EXECUTION_IGNORE_DEVICE_CERT  false                   Error SMKIE501 - Reading device certificates is not yet implemented  upload_files=True
    EXECUTION_REVOCATION_LIST_KEYSTORE_KEYSTORE_PATH  /keys/keystore-example-test.p12    Error SMKIE007 - Failed to load keystore  skip_exit_code=True  second_prop=EXECUTION_REVOCATION_LIST_KEYSTORE_KEYSTOREPATH
    EXECUTION_REVOCATION_LIST_KEYSTORE_ALIAS         key-alias                              Error SMKIE007 - Certificate for alias \\[key-alias\\] does not exist in keystore  skip_exit_code=True
    EXECUTION_REVOCATION_LIST_KEYSTORE_PASSWORD      99999      Error SMKIE007 - Failed to load keystore  skip_exit_code=True
    SPRING_DATASOURCE_USERNAME          zzzz                    Access denied for user  skip_exit_code=True  second_prop=QUARKUS_DATASOURCE_USERNAME
    SPRING_DATASOURCE_PASSWORD          aaaa                    Access denied for user  skip_exit_code=True  second_prop=QUARKUS_DATASOURCE_PASSWORD

# -------------------------------------------------------------------------------------------------

SMKI Importer - Test Sample Supplied File as DELTA
    [Documentation]  Tests the execution of a sample file from the real SMKI SFTP server as a DELTA file.
    [Tags]  DCO-8973  DCO_POSITIVE_TEST

    Import Library  robot-library/process-xml-certs-file.py

    Create Mandatory Certificate Files

    Remove File  test-files/SMKIKR_DELT_${date}.xml
    Remove File  test-files/SMKIKR_DELT_${date}.xml.gz

    Copy File  support-files/file-sample.xml.gz  test-files/SMKIKR_DELT_${date}.xml.gz

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Upload Mandatory Files To Server

    # Upload File To Server  support-files/CRL_S4.crl.gz
    # Disabling the crl processing because we don't have the root certificate for the CA CRL

    Update SMKI Importer Config  SMKIR_EXECUTION_READ_REVOCATION_LISTS  false

    Run SMKI Importer

    ${certs_to_use}  ${certs_to_ignore}=  Process XML File  test-files/SMKIKR_DELT_${date}.xml.gz

    ${num_valid_certs}=  Get Length  ${certs_to_use}
    Number Of Certificates In Database Is  ${num_valid_certs}
    Assert That Database Contains Certificates  ${certs_to_use}  has_filepaths=False
    Assert That Database Does Not Contains Certificates  ${certs_to_ignore}  has_filepaths=False

    Assert That Database History Contains Entry  DELTA  ${date}
    Assert That Execution Log Does Not Contains Errors
    Assert That The DELTA File Execution Was Successful

# -------------------------------------------------------------------------------------------------

SMKI Importer - Test Sample Supplied File as FULL
    [Documentation]  Tests the execution of a sample file from the real SMKI SFTP server as a FULL file.
    [Tags]  DCO-9106  DCO_POSITIVE_TEST

    Import Library  robot-library/process-xml-certs-file.py

    Create Mandatory Certificate Files

    Remove File  test-files/SMKIKR_FULL_${date}.xml
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz

    Copy File  support-files/file-sample.xml.gz  test-files/SMKIKR_FULL_${date}.xml.gz

    Upload Mandatory Files To Server

    # Upload File To Server  support-files/CRL_S4.crl.gz
    # Disabling the crl processing because we don't have the root certificate for the CA CR
    Update SMKI Importer Config  SMKIR_EXECUTION_READ_REVOCATION_LISTS  false

    Run SMKI Importer

    ${certs_to_use}  ${certs_to_ignore}=  Process XML File  test-files/SMKIKR_FULL_${date}.xml.gz

    ${num_valid_certs}=  Get Length  ${certs_to_use}
    Number Of Certificates In Database Is  ${num_valid_certs}
    Assert That Database Contains Certificates  ${certs_to_use}  has_filepaths=False
    Assert That Database Does Not Contains Certificates  ${certs_to_ignore}  has_filepaths=False

    Assert That Database History Contains Entry  FULL  ${date}
    Assert That Execution Log Does Not Contains Errors
    Assert That The FULL File Execution Was Successful

# -------------------------------------------------------------------------------------------------

SMKI Importer - Root Certificates
    [Documentation]  Tests that if the file being processed contains root certificates
    ...              the SMKI Importer ignores those certificates, and the execution runs normaly.
    [Tags]  DCO-9354  DCO-8864  DCO_POSITIVE_TEST

    Create Mandatory Certificate Files

    Remove File  test-files/SMKIKR_DELT_${date}.xml
    Remove File  test-files/SMKIKR_DELT_${date}.xml.gz

    ${valid_certificates}=  Create List   ${certificates_folder}/root/ca/cert.der
    ...                             ${certificates_folder}/root/ca/ee-db/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root/ca/ee-db/supplier/cert.der
    ...                             ${certificates_folder}/root-b/ca-b/cert.der
    ...                             ${certificates_folder}/root-b/ca-b/ee-db/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root-b/ca-b/ee-db/supplier/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/acb-xml-signing/cert.der
    ...                             ${certificates_folder}/root-untrusted/ca/ee/supplier/cert.der

    ${root_certificates}=  Create List   ${certificates_folder}/root/cert.der
    ...                                  ${certificates_folder}/root-b/cert.der
    ...                                  ${certificates_folder}/root-untrusted/cert.der

    ${certificates_list}=  Combine Lists  ${valid_certificates}  ${root_certificates}

    Create Certificate Data Response File  ${certificates_list}  test-files/SMKIKR_DELT_${date}

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_one_day}

    Upload Mandatory Files To Server

    Run SMKI Importer

    ${certificates_in_list}=  Get Length  ${valid_certificates}
    Number Of Certificates In Database Is  ${certificates_in_list}
    Assert That Database Contains Certificates  ${valid_certificates}
    Assert That Database Does Not Contains Certificates  ${root_certificates}

    Number Of CRLs In Database Is  5
    ${issuers}=  Read Issuers Of Revocation Lists From Database

    List Should Contain Value  ${issuers}  CN=DCO_ROOT,OU=00                #ARL_DCO_ROOT.arl
    List Should Contain Value  ${issuers}  CN=DCO_ROOT_B,OU=00              #ARL_DCO_ROOT_B.arl
    List Should Contain Value  ${issuers}  CN=DCO_CA,OU=07                  #CRL_DCO_CA.crl
    List Should Contain Value  ${issuers}  CN=DCO_CA_B,OU=07                #CRL_DCO_CA_B.crl
    List Should Contain Value  ${issuers}  CN=DCO_ROOT_UNTRUSTED_CA,OU=07   #CRL_DCO_ROOT_UNTRUSTED_CA.crl

    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL File Available
    [Documentation]  This test checks that when SMKI Importer hasn't run for more than seven days
    ...              and there is a FULL file availale in the SFTP server, the FULL file is still
    ...              processed as it is the only possibility to cover all the existing certificates.
    ...               \\ The DELTA files are not processed
    [Tags]  DCO-14116  DCO_POSITIVE_TEST  DCO-14052
    [Setup]  SMKI Importer Test Setup

    ${date_before_eight_days}=  Increment Date  ${date}  -8 days

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_eight_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${certificates_in_file}=  Get Length  ${date_full_certificates}
    Number Of Certificates In Database Is  ${certificates_in_file}
    Assert That Database Contains Certificates  ${date_full_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  FULL  ${date}
    Assert Successful Execution In Log  FULL

# -------------------------------------------------------------------------------------------------

SMKI Importer - FULL File Not Available
    [Documentation]  This test checks that if there's no FULL file available in the SFTP server and
    ...              the SMKI importer has not run from more than seven days, the application throws an exception.
    [Tags]  DCO-14117  DCO_NEGATIVE_TEST  DCO-14052
    [Setup]  SMKI Importer Test Setup

    ${date_before_eight_days}=  Increment Date  ${date}  -8 days

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_eight_days}

    ${files}=  Create List
#    ...                     test-files/SMKIKR_FULL_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...                     test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz
    ...                     ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...                     ${CRL_FOLDER}/CRL_DCO_CA.crl

    Test Expected Certificates Files
    ...  ${files}
    ...  database_history_is_empty=${False}
    ...  error_code_message=${SMKIE101_ERROR_MISSING_FULL_FILE}

# -------------------------------------------------------------------------------------------------

SMKI Importer - Last DELTA Two Days Ago And FULL File Available
    [Documentation]  This test checks that if the FULL file is available in the SFTP server and SMKI Importer has run
    ...              two days ago, the application shall processes the necessary DELTA files until the current date.
    [Tags]  DCO-14119  DCO_POSITIVE_TEST  DCO-14052  DCO-16960
    [Setup]  SMKI Importer Test Setup

    ${date_before_two_days}=  Increment Date  ${date}  -2 days

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_two_days}

    Create Mandatory Certificate Files
    Upload Mandatory Files To Server

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------

SMKI Importer - Last DELTA Two Days Ago And FULL File Not Available
    [Documentation]  Tests that if the FULL file is not available in the SFTP server and SMKI Importer has run
    ...              two days ago, the application processes the necessary DELTA files until the current date
    ...              ignoring that the FULL file is missing in the SFTP.
    [Tags]  DCO-14118  DCO_POSITIVE_TEST  DCO-14052  DCO-16960
    [Setup]  SMKI Importer Test Setup

    ${date_before_two_days}=  Increment Date  ${date}  -2 days

    # Add a dummy entry to the history table for the SMKI Importer to find what was the last processed file
    Insert History Record  DELTA  ${date_before_two_days}

    Create Mandatory Certificate Files

    # Remove the default FULL file
    Remove File  test-files/SMKIKR_FULL_${date}.xml.gz
    Remove File  test-files/SMKIKR_FULL_${date}.xml

    ${files}=  Create List
#    ...  test-files/SMKIKR_FULL_${date}.xml.gz
    ...  test-files/SMKIKR_DELT_${date}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_one_day}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_two_days}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_three_days}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_four_days}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_five_days}.xml.gz
    ...  test-files/SMKIKR_DELT_${date_before_six_days}.xml.gz

    # Uploading all CRLs and ARLs to tests if invalid ones are ignored
    ...  ${CRL_FOLDER}/CRL_DCO_CA_EXPIRED.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA_INVALID_SIGNATURE.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA_MISSING.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA_REVOKED.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA_B.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA1.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA2.crl
    ...  ${CRL_FOLDER}/CRL_DCO_CA_SS.crl
    ...  ${CRL_FOLDER}/CRL_DCO_ROOT_UNTRUSTED_CA.crl
    ...  ${CRL_FOLDER}/ARL_DCO_ROOT_UNTRUSTED.arl
    ...  ${CRL_FOLDER}/ARL_DCO_ROOT.arl
    ...  ${CRL_FOLDER}/ARL_DCO_ROOT_B.arl

    Upload Files To Server  ${files}

    Run SMKI Importer

    ${existing_certificates_list}=  Create List
    ...  @{date_delta_certificates}
    ...  @{date_before_one_day_certificates}

    ${non_existing_certificates_list}=  Create List
    ...  @{date_full_certificates}
    ...  @{date_before_two_days_certificates}
    ...  @{date_before_three_days_certificates}
    ...  @{date_before_four_days_certificates}
    ...  @{date_before_five_days_certificates}
    ...  @{date_before_six_days_certificates}

    ${certificates_in_files}=  Get Length  ${existing_certificates_list}
    Number Of Certificates In Database Is  ${certificates_in_files}
    Assert That Database Contains Certificates  ${existing_certificates_list}

    Remove CA Certificates From List  ${non_existing_certificates_list}
    Assert That Database Does Not Contains Certificates  ${non_existing_certificates_list}
    Assert That Database Contains Mandatory CRLs
    Assert That Database History Contains Mandatory CRLs Entries
    Assert That Database History Contains Entry  DELTA  ${date}
    Assert Successful Execution In Log  DELTA

# -------------------------------------------------------------------------------------------------
