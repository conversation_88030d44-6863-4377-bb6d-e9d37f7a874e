*** Settings ***
Test Timeout  5 minutes
Documentation  This Test suite contains test cases related directly with the Trilliant Crypto module

Test Tags  DCO_HSM  S1SP_TRILLIANT

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-common/resource-common.resource
Resource  robot-resources/s1sp-trilliant/resource-detect.resource
Resource  robot-resources/s1sp-trilliant/resource-common.resource
Resource  robot-resources/s1sp-common/resource-health-checks.resource

Suite Setup     Generic Suite Setup
Suite Teardown  Generic Suite Teardown

*** Test Cases ***
HSM-TRILLIANT-CRYPTO - Success - Log File Created At Startup
    [Documentation]  Verify that the log file is created at component startup
    [Tags]  DCO-20995  DCO_POSITIVE_TEST  DCO-15689

    Check Log File Created At Startup  dco-crypto-trilliant  logs/dco-crypto-trilliant.log
    ...    ${scenario}[system-space.Dco.CryptoTrilliantHealth]  operation=checkSigningHealth, message='Checking signing health', healthy=true

# -------------------------------------------------------------------------------------------------
HSM-TRILLIANT-CRYPTO - Health Check - All UP
    [Documentation]  This test validates that the crypto health check endpoint returns the correct status if
    ...              all the Crypto checks are UP.
    [Tags]  DCO-14299  DCO_POSITIVE_TEST  DCO_HEALTH_CHECK  DCO-14139
    [Timeout]  5 minutes

    Wait For Crypto Dlms Health Check To Be UP In dco-crypto-trilliant

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CryptoTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  200
    ${responseBody}=  Get Async HTTP Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${responseBody}  status
    Should Be Equal As Strings  ${healthStatus}  UP

# -------------------------------------------------------------------------------------------------
HSM-TRILLIANT-CRYPTO - Codesafe Destroy Call Is Logged
    [Documentation]  This test verifies the Destroy Codesafe API is being called and that it is being logged.
    [Tags]  DCO-15240  DCO_POSITIVE_TEST  DCO-15184
    [Setup]  Trilliant Detect Test Setup  %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/authenticate/generic/success/authenticate-success-duis.xml  HSMLgEsmeA
    
    ${encryptionKey64} =  Get File  %{PYTHONPATH}/scenarios/${scenario}[system-space.HSMLgEsmeA.DlmsClients.Management.ValidKey.Guek.Package]
    ${authenticationKey64} =  Get File  %{PYTHONPATH}/scenarios/${scenario}[system-space.HSMLgEsmeA.DlmsClients.Management.ValidKey.Ak.Package]
    ${initialisationVector64} =  Set Variable  ${scenario}[system-space.Dco.InitialisationVector.Base64]
    ${message} =  Get File  %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/authenticate/generic/success/authenticate-success-dlms-1.hex
    ${message64} =  Get Base64 From Hex  ${message}

    # This step is needed to ensure that the crypto Trilliant has the expected HSM 'destroy' message
    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.TrilliantAuthenticateService]
    ...  with method:  POST
    ...  with header parameters:
    ...     accept: application/json
    ...     Content-Type: application/json
    ...     sessionId: ${sessionId}
    ...     SubsessionId: ${SubsessionId}
    ...  with body:
    ...  {
    ...    "encryptionKey": "${encryptionKey64}",
    ...    "authenticationKey": "${authenticationKey64}",
    ...    "cleartextMessage": "${message64}",
    ...    "initialisationVector": "${initialisationVector64}"
    ...  }

    Validate That Service Replies Async With Status  ${future}  200
    Trilliant S1SP Closes Session And Subsession  ${sessionId}  ${SubsessionId}

    Set Test Start Date Time Variable

    # This keyword does scale down the artifact.
    Check If Last Logs Have Message Waiting Until 300 seconds  dco-crypto-trilliant  Destroying HSM Unattended Interface

    ${currentDate} =  Get Current Date  result_format=%Y-%m-%dT%H:%M:%S.%fZ
    [Teardown]  Run Keywords  Scale Up Artifact  dco-crypto-trilliant  AND
    ...  Wait For Crypto Dlms Health Check To Be UP In dco-crypto-trilliant  timeout=300 seconds  AND
    ...  Wait For Command Manager Crypto Health Check To Be UP In dco-command-manager-trilliant  timeout=300 seconds

#----------------------------------------------------------------------------------------------------
