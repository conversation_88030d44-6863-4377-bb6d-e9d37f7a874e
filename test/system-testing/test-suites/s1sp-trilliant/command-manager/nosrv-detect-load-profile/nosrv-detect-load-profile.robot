*** Settings ***
Test Timeout  5 minutes
Documentation  Validate the *NoSRV Detect* functionality for Load Profile commands.
...            This test suite covers the validation of Load Profile commands in NoSRV context,
...            including both valid and invalid scenarios as specified in the acceptance criteria.

Test Tags  DCO_DLMS  S1SP_TRILLIANT  DCO_LG  NO_SRV  DCO_DETECT  LOAD_PROFILE

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-trilliant/resource-nosrv-detect-load-profile.resource
Resource  robot-resources/s1sp-trilliant/resource-nosrv-detect-load-profile-payloads.resource

Suite Setup     NoSRV Detect Load Profile Suite Setup
Test Teardown   NoSRV Detect Load Profile Test Teardown
Suite Teardown  NoSRV Detect Load Profile Suite Teardown

*** Variables ***
${defaultreqid} =  00-00-00-00-00-04-01-0A
${defaultdevicemodel} =  E4700000000000000000

*** Test Cases ***

# =============================================================================
# SUCCESS CASES - Load Profile 1 (0.0.99.1.0.255)
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE-1-V1 - Success - Both Attributes
    [Documentation]  Verify successful NoSRV detect for Load Profile 1 with V1 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V1: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {5,1-0:1.4.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_1  V1_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${XML_LP1_V1}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-1-V2 - Success - Both Attributes
    [Documentation]  Verify successful NoSRV detect for Load Profile 1 with V2 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V2: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {3,1.0.1.8.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_1  V2_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP1_V2_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-1-V1 - Success - Different Data Index Values
    [Documentation]  Verify successful NoSRV detect for Load Profile 1 with V1 configuration
    ...              using different values for the data index in {1,0-0:96.10.1.255,2,?}.
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_1  V1_CONFIG  DATA_INDEX_VARIATION
    [Setup]  NoSRV Detect Load Profile Test Setup

    # Using direct payloads instead of problematic hex files
    ${future1}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_0_CLEAN}
    Validate That Service Replies Async With Status  ${future1}  200

    ${future2}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_1_CLEAN}
    Validate That Service Replies Async With Status  ${future2}  200

    ${future3}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_255_CLEAN}
    Validate That Service Replies Async With Status  ${future3}  200

# =============================================================================
# SUCCESS CASES - Load Profile 2 (0.0.99.1.1.255)
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE-2-V1 - Success - Both Attributes
    [Documentation]  Verify successful NoSRV detect for Load Profile 2 with V1 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V1: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {5,1-0:2.8.0.255,2,0}, {5,1-0:3.8.0.255,2,0}, {5,1-0:4.8.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_2  V1_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${XML_LP2_V1}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-2-V2 - Success - Both Attributes
    [Documentation]  Verify successful NoSRV detect for Load Profile 2 with V2 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V2: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {3,1-0:2.8.0.255,2,0}, {3,1-0:3.8.0.255,2,0}, {3,1-0:4.8.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_2  V2_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_V2_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

# =============================================================================
# FAILURE CASES - Context and Request Type Validation
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - SRV Context
    [Documentation]  Verify that Load Profile commands are rejected when sent in SRV context
    ...              instead of NoSRV context.
    [Tags]  DCO_NEGATIVE_TEST  CONTEXT_VALIDATION
    [Teardown]  # No teardown needed as no session is opened

    ${sessionId}  ${subsessionId} =  Trilliant Send Request And Open Session And Subsession
    ...  ../nosrv-generate-gmac/success/generate-gmac-success-duis.xml  LgEsmeA

    ${future}=  NoSRV Detect Load Profile Request With Session  
    ...  failure/load-profile-1-v1-both-attrs-dlms.hex  ${sessionId}  ${subsessionId}
    Async HTTP Service Replies with Status and Error Codes  ${future}  400  ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

    Trilliant S1SP Closes Session And Subsession  ${sessionId}  ${subsessionId}

NOSRV-DETECT-LOAD-PROFILE - Failure - Action Request Type
    [Documentation]  Verify that Load Profile commands are rejected when using "action"
    ...              request type instead of "set".
    [Tags]  DCO_NEGATIVE_TEST  REQUEST_TYPE_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Request  failure/load-profile-1-action-type-dlms.hex
    Async HTTP Service Replies with Status and Error Codes  ${future}  400  ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# FAILURE CASES - Class ID and Attribute ID Validation
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - Wrong Class ID
    [Documentation]  Verify that Load Profile commands are rejected when using unexpected
    ...              class ID (not 7 - Profile Generic).
    [Tags]  DCO_NEGATIVE_TEST  CLASS_ID_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Request  failure/load-profile-wrong-class-id-dlms.hex
    Async HTTP Service Replies with Status and Error Codes  ${future}  400  ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

NOSRV-DETECT-LOAD-PROFILE - Failure - Wrong Attribute ID
    [Documentation]  Verify that Load Profile commands are rejected when using unexpected
    ...              attribute ID (not 3 or 4).
    [Tags]  DCO_NEGATIVE_TEST  ATTRIBUTE_ID_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-1-attr-id-2-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-1-attr-id-5-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# FAILURE CASES - OBIS Code Validation
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - Wrong OBIS Code
    [Documentation]  Verify that Load Profile commands are rejected when using OBIS codes
    ...              that don't match the expected Load Profile instances.
    [Tags]  DCO_NEGATIVE_TEST  OBIS_CODE_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-wrong-obis-1-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-wrong-obis-2-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# FAILURE CASES - Attribute Type Validation
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - Wrong Attribute Type
    [Documentation]  Verify that Load Profile commands are rejected when using unexpected
    ...              attribute types (e.g., sending array/structure in place of constant value).
    [Tags]  DCO_NEGATIVE_TEST  ATTRIBUTE_TYPE_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-1-attr4-wrong-type-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-2-attr4-array-type-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# FAILURE CASES - Attribute Value Validation
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - Wrong Capture Period
    [Documentation]  Verify that Load Profile commands are rejected when using wrong
    ...              capture period value (not 1800 seconds).
    [Tags]  DCO_NEGATIVE_TEST  ATTRIBUTE_VALUE_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-1-wrong-period-1000-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-1-wrong-period-3600-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# FAILURE CASES - Array Validation for Attribute 3
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Failure - Invalid Array Elements
    [Documentation]  Verify that Load Profile commands are rejected when using invalid
    ...              array elements in attribute 3 (capture_objects).
    [Tags]  DCO_NEGATIVE_TEST  ARRAY_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-1-extra-array-element-dlms.hex     ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-1-missing-array-element-dlms.hex   ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-1-wrong-array-element-dlms.hex     ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-2-invalid-class-id-dlms.hex        ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-2-invalid-obis-dlms.hex            ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

NOSRV-DETECT-LOAD-PROFILE - Failure - Mixed V1 V2 Elements
    [Documentation]  Verify that Load Profile commands are rejected when mixing V1 and V2
    ...              array elements (not matching either V1 or V2 exactly).
    [Tags]  DCO_NEGATIVE_TEST  ARRAY_VALIDATION  MIXED_VERSION
    [Setup]  NoSRV Detect Load Profile Test Setup
    [Template]  NoSRV Detect Load Profile Failure Template

    failure/load-profile-1-mixed-v1-v2-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}
    failure/load-profile-2-mixed-v1-v2-dlms.hex    ${COMMAND_MANAGER_DLMS_SERVICE_REQUEST_APDU_ERROR}

# =============================================================================
# INTEGRATION TEST CASES - Individual Attribute Testing
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Success - Individual Attribute 3 Only V1
    [Documentation]  Verify successful NoSRV detect when setting only attribute 3 (capture_objects)
    ...              for Load Profile 1 V1 configuration. This tests individual attribute setting capability.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_1  V1_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP1_ATTR3_V1_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE - Success - Individual Attribute 3 Only V2
    [Documentation]  Verify successful NoSRV detect when setting only attribute 3 (capture_objects)
    ...              for Load Profile 1 V2 configuration. This tests individual attribute setting capability.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_1  V2_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP1_ATTR3_V2_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE - Success - Individual Attribute 4 Only
    [Documentation]  Verify successful NoSRV detect when setting only attribute 4 (capture_period)
    ...              for Load Profile 1. This tests individual attribute setting capability.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_1
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP1_ATTR4_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

# =============================================================================
# LOAD PROFILE 2 TEST CASES
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE-2 - Success - Both Attributes V1
    [Documentation]  Verify successful NoSRV detect for Load Profile 2 with V1 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V1: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {3,1-0:2.8.0.255,2,0}, {3,1-0:3.8.0.255,2,0}, {3,1-0:4.8.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_2  V1_CONFIG  WITH_LIST
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_V1_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-2 - Success - Both Attributes V2
    [Documentation]  Verify successful NoSRV detect for Load Profile 2 with V2 configuration
    ...              setting both attributes 3 (capture_objects) and 4 (capture_period).
    ...              V2: {8,0-0:1.0.0.255,2,0}, {1,0-0:96.10.1.255,2,?}, {3,1-0:2.8.0.255,2,0}, {3,1-0:3.8.0.255,2,0}, {3,1-0:4.8.0.255,2,0}
    [Tags]  DCO_POSITIVE_TEST  LOAD_PROFILE_2  V2_CONFIG  WITH_LIST
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_V2_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-2 - Success - Individual Attribute 3 Only V1
    [Documentation]  Verify successful NoSRV detect when setting only attribute 3 (capture_objects)
    ...              for Load Profile 2 V1 configuration.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_2  V1_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_ATTR3_V1_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-2 - Success - Individual Attribute 3 Only V2
    [Documentation]  Verify successful NoSRV detect when setting only attribute 3 (capture_objects)
    ...              for Load Profile 2 V2 configuration.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_2  V2_CONFIG
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_ATTR3_V2_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

NOSRV-DETECT-LOAD-PROFILE-2 - Success - Individual Attribute 4 Only
    [Documentation]  Verify successful NoSRV detect when setting only attribute 4 (capture_period)
    ...              for Load Profile 2.
    [Tags]  DCO_POSITIVE_TEST  INDIVIDUAL_ATTRIBUTE  LOAD_PROFILE_2
    [Setup]  NoSRV Detect Load Profile Test Setup

    ${future}=  NoSRV Detect Load Profile Raw Request  ${LP2_ATTR4_CLEAN}
    Validate That Service Replies Async With Status  ${future}  200

# =============================================================================
# EDGE CASE TESTING
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Success - Data Index Edge Cases
    [Documentation]  Verify NoSRV detect with edge case data index values
    ...              for the {1,0-0:96.10.1.255,2,?} object.
    [Tags]  DCO_POSITIVE_TEST  EDGE_CASES  DATA_INDEX_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup

    # Valid Load Profile 1 command with different data indices
    # Data index 0
    ${future1}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_0_CLEAN}
    Validate That Service Replies Async With Status  ${future1}  200

    # Data index 1
    ${future2}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_1_CLEAN}
    Validate That Service Replies Async With Status  ${future2}  200

    # Data index 255 (max value)
    ${future3}=  NoSRV Detect Load Profile Raw Request  ${LP1_DATA_INDEX_255_CLEAN}
    Validate That Service Replies Async With Status  ${future3}  200

# =============================================================================
# HELPER TEST FOR DEBUGGING
# =============================================================================

NOSRV-DETECT-LOAD-PROFILE - Helper - Use Direct Hex
    [Documentation]  Helper test that allows running tests with direct hex data
    ...              to bypass hex file parsing issues
    [Tags]  DCO_POSITIVE_TEST  HELPER
    [Setup]  NoSRV Detect Load Profile Test Setup

    Log To Console  Testing original LP1 V1 from XML
    ${future1}=  NoSRV Detect Load Profile Raw Request  ${XML_LP1_V1}
    Validate That Service Replies Async With Status  ${future1}  200

    Log To Console  Testing original LP2 V1 from XML
    ${future2}=  NoSRV Detect Load Profile Raw Request  ${XML_LP2_V1}
    Validate That Service Replies Async With Status  ${future2}  200

    # Extra simple test with exact hex from examples
    Log To Console  Testing helper LP1 V1
    ${future3}=  NoSRV Detect Load Profile Raw Request  C10441020007000063010000FF03000007000063010000FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800
    Validate That Service Replies Async With Status  ${future3}  200

    # Test with your exact Base64 payload to bypass hex conversion
    Log To Console  Testing with exact Base64 payload
    ${future4}=  NoSRV Detect Load Profile Base64 Request  ${WORKING_BASE64}
    Validate That Service Replies Async With Status  ${future4}  200

    [Teardown]  NoSRV Detect Load Profile Test Teardown

NOSRV-DETECT-LOAD-PROFILE - Failure - Session Context Mismatch
    [Documentation]  Verify that NoSRV detect commands fail when attempted with
    ...              invalid session/subsession combinations.
    [Tags]  DCO_NEGATIVE_TEST  SESSION_CONTEXT_VALIDATION
    [Setup]  NoSRV Detect Load Profile Test Setup

    # Test with invalid session ID
    ${future}=  NoSRV Detect Load Profile Request With Session
    ...  failure/load-profile-1-v1-both-attrs-dlms.hex  invalid_session  ${noSRVsubsessionId}
    Async HTTP Service Replies with Status and Error Codes  ${future}  401  ${COMMAND_MANAGER_DLMS_SESSION_NOT_FOUND_ERROR}
