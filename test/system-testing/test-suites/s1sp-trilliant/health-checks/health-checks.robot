*** Settings ***
Test Timeout  5 minutes
Documentation  This test suite tests the output returned by the health check endpoint in the Command Manager and Crypto components.

Test Tags  DCO_HEALTH_CHECK  DCO-14109

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-common/resource-common.resource
Resource  robot-resources/s1sp-common/resource-health-checks.resource

Suite Setup     Generic Suite Setup
Suite Teardown  Generic Suite Teardown

*** Test Cases ***

TRILLIANT - Health Check - All UP
    [Documentation]  The normal status of the Trilliant Command Manager dependencies is to be available,
    ...              therefore the health check should return the status as UP.
    [Tags]  DCO-14208  DCO_HEALTH_CHECK  DCO_POSITIVE_TEST

    Wait For Main Database Health Check To Be UP In dco-command-manager-trilliant
    Wait For Migration Database Health Check To Be UP In dco-command-manager-trilliant
    Wait For Command Manager Crypto Health Check To Be UP In dco-command-manager-trilliant

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CommandManagerTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  200
    ${json}=  Get Async Http Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${json}  status
    Should Be Equal As Strings  ${healthStatus}  UP

# -------------------------------------------------------------------------------------------------
TRILLIANT - Health Check - Main Database DOWN
    [Documentation]  This test validates that the health check endpoint returns the correct status if
    ...              the main database check fails.
    [Tags]  DCO-14207  DCO_HEALTH_CHECK  DCO_POSITIVE_TEST
    [Setup]  Scale Down Artifact  dco-database-server  StatefulSet

    Wait Until Keyword Succeeds  300 seconds  5 seconds
    ...    Run Keyword And Expect Error  *  Get Pod Names  dco-database-server

    Wait For Main Database Health Check To Be DOWN In dco-command-manager-trilliant
    
    Run Keyword And Expect Error  *'dco-database'*  Dco Database Is Available

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CommandManagerTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  503
    ${json}=  Get Async Http Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${json}  status
    Should Be Equal As Strings  ${healthStatus}  DOWN
    ${databaseHealthStatus} =  Get Values From JSON  ${json}  checks[?(@.name=='dco-database')].status
    Should Be Equal As Strings  ${databaseHealthStatus[0]}  DOWN

    [Teardown]  Run Keywords  Scale Up Artifact  dco-database-server  StatefulSet  AND
    ...  Wait Until Dco Database Is Available

# -------------------------------------------------------------------------------------------------
TRILLIANT - Health Check - Migration Database DOWN
    [Documentation]  This test validates that the health check endpoint returns the correct status if
    ...              the migration database check fails.
    [Tags]  DCO-14206  DCO_HEALTH_CHECK  DCO_POSITIVE_TEST
    [Setup]  Scale Down Artifact  dco-migration-database-router  StatefulSet

    Wait Until Keyword Succeeds  300 seconds  5 seconds
    ...    Run Keyword And Expect Error  *  Get Pod Names  dco-migration-database-router

    Wait For Migration Database Health Check To Be DOWN In dco-command-manager-trilliant

    Run Keyword And Expect Error  *'dco-migration-database'*  Dco Migration Database Is Available

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CommandManagerTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  503
    ${json}=  Get Async Http Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${json}  status
    Should Be Equal As Strings  ${healthStatus}  DOWN
    ${databaseHealthStatus} =  Get Values From JSON  ${json}  checks[?(@.name=='migration-database')].status
    Should Be Equal As Strings  ${databaseHealthStatus[0]}  DOWN

    [Teardown]  Run Keywords  Scale Up Artifact  dco-migration-database-router  StatefulSet  AND
    ...  Wait Until Dco Migration Database Is Available

# -------------------------------------------------------------------------------------------------
TRILLIANT - Health Check - Crypto DOWN
    [Documentation]  This test validates that the health check endpoint returns the correct status if
    ...              the Crypto check fails.
    [Tags]  DCO-14209  DCO_POSITIVE_TEST  DCO_HEALTH_CHECK
    [Setup]  Scale Down Artifact  dco-crypto-trilliant

    Wait Until Keyword Succeeds  300 seconds  5 seconds
    ...    Run Keyword And Expect Error  *  Get Pod Names  dco-crypto-trilliant

    Wait For Command Manager Crypto Health Check To Be DOWN In dco-command-manager-trilliant

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CommandManagerTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  503
    ${json}=  Get Async Http Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${json}  status
    Should Be Equal As Strings  ${healthStatus}  DOWN
    ${cryptoHealthStatus} =  Get Values From JSON   ${json}  checks[?(@.name=='crypto')].status
    Should Be Equal As Strings  ${cryptoHealthStatus[0]}  DOWN

    ${currentDate} =  Get Current Date  result_format=%Y-%m-%dT%H:%M:%S.%fZ
    [Teardown]  Run Keywords  Scale Up Artifact  dco-crypto-trilliant  AND
    ...  Wait For Crypto Dlms Health Check To Be UP In dco-crypto-trilliant  timeout=300 seconds  AND
    ...  Wait Until Keyword Succeeds  300 seconds  5 seconds  Check Component Health  ${scenario}[system-space.Dco.CommandManagerTrilliantHealth]

# -------------------------------------------------------------------------------------------------
TRILLIANT-CRYPTO - Health Check - All UP
    [Documentation]  The normal status of the Trilliant Crypto dependencies is to be available,
    ...              therefore the health check should return the status as UP.
    [Tags]  DCO-14305  DCO_HEALTH_CHECK  DCO_POSITIVE_TEST

    Wait For Crypto Dlms Health Check To Be UP In dco-crypto-trilliant

    ${future}=  Client Sends Async HTTP Request To  ${scenario}[system-space.Dco.CryptoTrilliantHealth]
    ...  with method:  GET
    ...  with header parameters:
    ...     accept: application/json

    Async HTTP Service Replies with Status  ${future}  200
    ${json}=  Get Async Http Text Response  ${future}
    ${healthStatus} =  Get Value From JSON  ${json}  status
    Should Be Equal As Strings  ${healthStatus}  UP

# -------------------------------------------------------------------------------------------------