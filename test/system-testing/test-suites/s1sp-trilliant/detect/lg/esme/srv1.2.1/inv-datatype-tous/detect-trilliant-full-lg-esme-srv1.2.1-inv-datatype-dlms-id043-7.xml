<SetRequest>
    <SetRequestNormal>
        <InvokeIdAndPriority Value="41"/>
        <AttributeDescriptor>
            <ClassId Value="2328"/>
            <InstanceId Value="00003F0101FF"/>
            <AttributeId Value="04"/>
        </AttributeDescriptor>
        <Value>
          <Structure Qty="0009">
            <OctetString value="FFFFFFFFFFFFFFFFFFFFFFFF"/>
            <Boolean Value="01"/>
            <Boolean Value="01"/>
            <Boolean Value="00"/>
            <Boolean Value="00"/>
            <BitString Value="000000000"/>
            <Integer Value="00"/>
            <Boolean Value="00"/>
            <Boolean Value="01"/>
          </Structure>
        </Value>
    </SetRequestNormal>
</SetRequest>