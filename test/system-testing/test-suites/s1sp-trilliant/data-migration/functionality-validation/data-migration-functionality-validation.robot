*** Settings ***
Test Timeout  10 minutes
Documentation  Test the *Data Migration* functionality validation implementation.
...  Validate database tables behaviour and status updating as the files are processed
...  Also, covers several aspects of installation generation and overwriting

Test Tags  DCO_DATA_MIGRATION  DCO_DLMS  S1SP_TRILLIANT

Library  robot-library/dcovv.py
Resource  robot-resources/s1sp-common/resource-data-migration.resource
Resource  robot-resources/s1sp-trilliant/resource-common.resource

Suite Setup     Migration Suite Setup
Test Setup      Migration Test Setup
Test Teardown   Migration Test Teardown
Suite Teardown  Migration Suite Teardown

*** Variables ***
${chf_column}                   chf_id
${chf_column_order_asc}         order by chf_id ASC
${device_column}                device_id
${migration_duplicated_device}  migration_duplicated_device
${viable_installation}          migration_viable_installation

&{dic_duplicated_device_1}      req_party_id=00-00-00-00-00-00-00-93  mcf_counter=4  group_id=EA  mef_counter=4  chf_id=10-00-00-00-00-00-21-00  viable_chf_id=10-00-00-00-00-00-00-20  device_id=10-00-00-00-00-00-00-21
&{dic_duplicated_device_2}      req_party_id=00-00-00-00-00-00-00-93  mcf_counter=4  group_id=EA  mef_counter=4  chf_id=20-00-00-00-00-00-21-00  viable_chf_id=20-00-00-00-00-00-00-20  device_id=20-00-00-00-00-00-00-21
&{dic_duplicated_device_3}      req_party_id=00-00-00-00-00-00-00-93  mcf_counter=4  group_id=EA  mef_counter=4  chf_id=30-00-00-00-00-00-21-00  viable_chf_id=30-00-00-00-00-00-00-20  device_id=30-00-00-00-00-00-00-21
@{duplicated_devices_list}      &{dic_duplicated_device_1}   &{dic_duplicated_device_2}   &{dic_duplicated_device_3}


*** Test Cases ***

#----------------------------------------------------------------------------------------------------

MIGRATION-TRILLIANT-FUNCTIONALITY - Duplicated Device For All Types and All GroupIDs (Input EA)
    [Documentation]  This test consists of validating the behavior of the DCO when exists installations with multiple
    ...  duplicated devices. With regard to all types of devices (CHF, ESME, GPF, GSME, PPMID), as well as all existent
    ...  groups (EA,EB,EC) for Trilliant cohort.
    ...  \\ The DCO will check if there are duplicated devices, through the analysis of the following tags/fields
    ...  “<ESMEDetail>” (which is the type of device that have an encrypted key associated).
    ...  If there is any device id of the type (ESME), already being used in a previous installation in one of
    ...  these tags/fields then the latest installation will be considered duplicated.
    ...  \\ Expected Viable Installations
    ...    \\  1) 10-00-00-00-00-00-00-00        2) 10-00-00-00-00-00-00-01	     3) 10-00-00-00-00-00-00-02		 4) 10-00-00-00-00-00-00-03     5) 10-00-00-00-00-00-00-04	     6) 10-00-00-00-00-00-00-10      7) 10-00-00-00-00-00-00-20	     8) 10-00-00-00-00-00-00-30
    ...    \\  9) 10-00-00-00-00-00-00-40       10) 10-00-00-00-00-00-00-50	    11) 10-00-00-00-00-00-25-00     12) 10-00-00-00-00-00-29-00    13) 10-00-00-00-00-00-33-00      14) 10-00-00-00-00-00-37-00	    15) 10-00-00-00-00-01-00-00     16) 10-00-00-00-00-05-00-00
    ...    \\ 17) 10-00-00-00-00-09-00-00       18) 10-00-00-00-00-13-00-00	    19) 10-00-00-00-00-17-00-00	    20) 10-00-00-00-00-21-00-00    21) 10-00-00-00-00-25-00-00      22) 10-00-00-00-00-29-00-00	    23) 10-00-00-00-00-33-00-00     24) 10-00-00-00-00-37-00-00
    ...    \\ 25) 10-00-00-00-00-41-00-00       26) 10-00-00-00-00-45-00-00	    27) 10-00-00-00-00-49-00-00		28) 10-00-00-00-00-53-00-00    29) 10-00-00-00-00-57-00-00      30) 20-00-00-00-00-00-00-00	    31) 20-00-00-00-00-00-00-01     32) 20-00-00-00-00-00-00-02
    ...    \\ 33) 20-00-00-00-00-00-00-03       34) 20-00-00-00-00-00-00-04	    35) 20-00-00-00-00-00-00-10	    36) 20-00-00-00-00-00-00-20    37) 20-00-00-00-00-00-00-30      38) 20-00-00-00-00-00-00-40	    39) 20-00-00-00-00-00-00-50     40) 20-00-00-00-00-00-25-00
    ...    \\ 41) 20-00-00-00-00-00-29-00       42) 20-00-00-00-00-00-33-00	    43) 20-00-00-00-00-00-37-00	    44) 20-00-00-00-00-01-00-00    45) 20-00-00-00-00-05-00-00      46) 20-00-00-00-00-09-00-00	    47) 20-00-00-00-00-13-00-00     48) 20-00-00-00-00-17-00-00
    ...    \\ 49) 20-00-00-00-00-21-00-00       50) 20-00-00-00-00-25-00-00	    51) 20-00-00-00-00-29-00-00	    52) 20-00-00-00-00-33-00-00    53) 20-00-00-00-00-37-00-00      54) 20-00-00-00-00-41-00-00     55) 20-00-00-00-00-45-00-00     56) 20-00-00-00-00-49-00-00
    ...    \\ 57) 20-00-00-00-00-53-00-00       58) 20-00-00-00-00-57-00-00	    59) 30-00-00-00-00-00-00-00	    60) 30-00-00-00-00-00-00-01    61) 30-00-00-00-00-00-00-02      62) 30-00-00-00-00-00-00-03	    63) 30-00-00-00-00-00-00-04     64) 30-00-00-00-00-00-00-10
    ...    \\ 65) 30-00-00-00-00-00-00-20       66) 30-00-00-00-00-00-00-30	    67) 30-00-00-00-00-00-00-40	    68) 30-00-00-00-00-00-00-50    69) 30-00-00-00-00-00-25-00      70) 30-00-00-00-00-00-29-00	    71) 30-00-00-00-00-00-33-00     72) 30-00-00-00-00-00-37-00
    ...    \\ 73) 30-00-00-00-00-01-00-00       74) 30-00-00-00-00-05-00-00	    75) 30-00-00-00-00-09-00-00	    76) 30-00-00-00-00-13-00-00    77) 30-00-00-00-00-17-00-00      78) 30-00-00-00-00-21-00-00	    79) 30-00-00-00-00-25-00-00     80) 30-00-00-00-00-29-00-00
    ...    \\ 81) 30-00-00-00-00-33-00-00       82) 30-00-00-00-00-37-00-00	    83) 30-00-00-00-00-41-00-00	    84) 30-00-00-00-00-45-00-00    85) 30-00-00-00-00-49-00-00      86) 30-00-00-00-00-53-00-00	    87) 30-00-00-00-00-57-00-00
    ...  \\
    ...  \\
    ...  \\ Expected Duplicated Installations
    ...     \\  88) 10-00-00-00-00-00-21-00 	89) 20-00-00-00-00-00-21-00     90) 30-00-00-00-00-00-21-00
    [Tags]  DCO-16725  DCO_POSITIVE_TEST  DCO-13801
    # Upload of 3 migration file sets (GroupID:EA,EB,EC) - 18x installations
    #  Viable installations:
    #  6x >>> EA-  1),  6),  7),  8),  9), 10)
    #  6x >>> EB- 30), 35), 36), 37), 38), 39)
    #  6x >>> EC- 59), 64), 65), 66), 67), 68)
    Upload Migration File  valid-groupid-EA/MCF_00-00-00-00-00-00-00-93_1_0.xml
    Upload Migration File  valid-groupid-EA/MEF_00-00-00-00-00-00-00-93_1_1.xml
    Upload Migration File  valid-groupid-EA/MVF_00-00-00-00-00-00-00-90_1_1.xml
    Upload Migration File  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_2_0.xml
    Upload Migration File  valid-groupid-EB/MEF_00-00-00-00-00-00-00-93_2_2.xml
    Upload Migration File  valid-groupid-EB/MVF_00-00-00-00-00-00-00-90_2_2.xml
    Upload Migration File  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Upload Migration File  valid-groupid-EC/MEF_00-00-00-00-00-00-00-93_3_3.xml
    Upload Migration File  valid-groupid-EC/MVF_00-00-00-00-00-00-00-90_3_3.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  18  0

    # Upload 1 migration file set (GroupID:EA)- 75x installations
    # Viable installations: 2),  3),  4),  5), 11), 12), 13), 14), 15), 16), 17), 18), 19), 20)
    #   21), 22), 23), 24), 25), 26), 27), 28), 29), 31), 32), 33), 34), 40), 41), 42), 43), 44)
    #   45), 46), 47), 48), 49), 50), 51), 52), 53), 54), 55), 56), 57), 58), 60), 61), 62), 63)
    #   69), 70), 71), 72), 73), 74), 75), 76), 77), 78), 79), 80), 81), 82), 83), 84), 85), 86), 87)
    # Duplicated installations: 88), 89, 90)
    # Replaced installations: 6), 35), 64)
    Upload Migration File  valid-groupid-EA/MCF_00-00-00-00-00-00-00-93_4_0.xml
    Upload Migration File  valid-groupid-EA/MEF_00-00-00-00-00-00-00-93_4_4.xml
    Upload Migration File  valid-groupid-EA/MVF_00-00-00-00-00-00-00-90_4_4.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EA/MCF_00-00-00-00-00-00-00-93_4_0.xml  installation_index=74
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait 95 Seconds Until Table ${migration_duplicated_device} Has 3 Records In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  87  0

    # Ensure that the list "${duplicated_devices_list}" which contains the information of each expected
    # duplicated device, match the information stored on DB
    ${index}  Set Variable  0
   FOR    ${device}    IN    @{duplicated_devices_list}
        # The new Trilliant installation ${device.chf_id} contains a Device ${device.device_id} that
        # is already being used in the existing viable Trilliant installation ${device.viable_chf_id}
        Validate Value Table  ${device.chf_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${chf_column}  ${index}  0
        Validate Value Table  ${device.device_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${device_column}  ${index}  0
        # Validate on DB that the table "migration_duplicated_device", contains all the expected information
        Validate Values From Migration Duplicated Devices  ${device.chf_id}  EA  ${device.req_party_id}  ${device.mcf_counter}  ${device.mef_counter}
        # Check if the viable Trilliant installation found in the table "migration_duplicated_device" was indeed already considered
        # viable and can be found in the table "migration_viable_installation"
        ${viable_chf_id}  Get Viable CHF Id From Migration Duplicated Devices  ${device.chf_id}
        Validate Installation  ${viable_installation}  ${viable_chf_id}
        ${index}  Evaluate  ${index} + 1
   END

#----------------------------------------------------------------------------------------------------

MIGRATION-TRILLIANT-FUNCTIONALITY - Duplicated Device For All Types and All GroupIDs (Input EB)
    [Documentation]  This test consists of validating the behavior of the DCO when exists installations with multiple
    ...  duplicated devices. With regard to all types of devices (CHF, ESME, GPF, GSME, PPMID), as well as all existent
    ...  groups (EA,EB,EC) for Trilliant cohort.
    ...  \\ The DCO will check if there are duplicated devices, through the analysis of the following tags/fields
    ...  “<ESMEDetail>” (which is the type of device that have an encrypted key associated).
    ...  If there is any device id of the type (ESME), already being used in a previous installation in one of
    ...  these tags/fields then the latest installation will be considered duplicated.
    ...  \\ Expected Viable Installations
    ...    \\  1) 10-00-00-00-00-00-00-00        2) 10-00-00-00-00-00-00-01	     3) 10-00-00-00-00-00-00-02		 4) 10-00-00-00-00-00-00-03     5) 10-00-00-00-00-00-00-04	     6) 10-00-00-00-00-00-00-10      7) 10-00-00-00-00-00-00-20	     8) 10-00-00-00-00-00-00-30
    ...    \\  9) 10-00-00-00-00-00-00-40       10) 10-00-00-00-00-00-00-50	    11) 10-00-00-00-00-00-25-00     12) 10-00-00-00-00-00-29-00    13) 10-00-00-00-00-00-33-00      14) 10-00-00-00-00-00-37-00	    15) 10-00-00-00-00-01-00-00     16) 10-00-00-00-00-05-00-00
    ...    \\ 17) 10-00-00-00-00-09-00-00       18) 10-00-00-00-00-13-00-00	    19) 10-00-00-00-00-17-00-00	    20) 10-00-00-00-00-21-00-00    21) 10-00-00-00-00-25-00-00      22) 10-00-00-00-00-29-00-00	    23) 10-00-00-00-00-33-00-00     24) 10-00-00-00-00-37-00-00
    ...    \\ 25) 10-00-00-00-00-41-00-00       26) 10-00-00-00-00-45-00-00	    27) 10-00-00-00-00-49-00-00		28) 10-00-00-00-00-53-00-00    29) 10-00-00-00-00-57-00-00      30) 20-00-00-00-00-00-00-00	    31) 20-00-00-00-00-00-00-01     32) 20-00-00-00-00-00-00-02
    ...    \\ 33) 20-00-00-00-00-00-00-03       34) 20-00-00-00-00-00-00-04	    35) 20-00-00-00-00-00-00-10	    36) 20-00-00-00-00-00-00-20    37) 20-00-00-00-00-00-00-30      38) 20-00-00-00-00-00-00-40	    39) 20-00-00-00-00-00-00-50     40) 20-00-00-00-00-00-25-00
    ...    \\ 41) 20-00-00-00-00-00-29-00       42) 20-00-00-00-00-00-33-00	    43) 20-00-00-00-00-00-37-00	    44) 20-00-00-00-00-01-00-00    45) 20-00-00-00-00-05-00-00      46) 20-00-00-00-00-09-00-00	    47) 20-00-00-00-00-13-00-00     48) 20-00-00-00-00-17-00-00
    ...    \\ 49) 20-00-00-00-00-21-00-00       50) 20-00-00-00-00-25-00-00	    51) 20-00-00-00-00-29-00-00	    52) 20-00-00-00-00-33-00-00    53) 20-00-00-00-00-37-00-00      54) 20-00-00-00-00-41-00-00     55) 20-00-00-00-00-45-00-00     56) 20-00-00-00-00-49-00-00
    ...    \\ 57) 20-00-00-00-00-53-00-00       58) 20-00-00-00-00-57-00-00	    59) 30-00-00-00-00-00-00-00	    60) 30-00-00-00-00-00-00-01    61) 30-00-00-00-00-00-00-02      62) 30-00-00-00-00-00-00-03	    63) 30-00-00-00-00-00-00-04     64) 30-00-00-00-00-00-00-10
    ...    \\ 65) 30-00-00-00-00-00-00-20       66) 30-00-00-00-00-00-00-30	    67) 30-00-00-00-00-00-00-40	    68) 30-00-00-00-00-00-00-50    69) 30-00-00-00-00-00-25-00      70) 30-00-00-00-00-00-29-00	    71) 30-00-00-00-00-00-33-00     72) 30-00-00-00-00-00-37-00
    ...    \\ 73) 30-00-00-00-00-01-00-00       74) 30-00-00-00-00-05-00-00	    75) 30-00-00-00-00-09-00-00	    76) 30-00-00-00-00-13-00-00    77) 30-00-00-00-00-17-00-00      78) 30-00-00-00-00-21-00-00	    79) 30-00-00-00-00-25-00-00     80) 30-00-00-00-00-29-00-00
    ...    \\ 81) 30-00-00-00-00-33-00-00       82) 30-00-00-00-00-37-00-00	    83) 30-00-00-00-00-41-00-00	    84) 30-00-00-00-00-45-00-00    85) 30-00-00-00-00-49-00-00      86) 30-00-00-00-00-53-00-00	    87) 30-00-00-00-00-57-00-00
    ...  \\
    ...  \\
    ...  \\ Expected Duplicated Installations
    ...     \\  88) 10-00-00-00-00-00-21-00 	89) 20-00-00-00-00-00-21-00     90) 30-00-00-00-00-00-21-00
    [Tags]  DCO-16723  DCO_POSITIVE_TEST  DCO-13801
    # Upload of 3 migration file sets (GroupID:EA,EB,EC) - 18x installations
    #  Viable installations:
    #  6x >>> EA- 1), 6), 7), 8), 9), 10)
    #  6x >>> EB- 30), 35), 36), 37), 38), 39)
    #  6x >>> EC- 59), 64), 65), 66), 67), 68)
    Upload Migration File  valid-groupid-EA/MCF_00-00-00-00-00-00-00-93_1_0.xml
    Upload Migration File  valid-groupid-EA/MEF_00-00-00-00-00-00-00-93_1_1.xml
    Upload Migration File  valid-groupid-EA/MVF_00-00-00-00-00-00-00-90_1_1.xml
    Upload Migration File  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_2_0.xml
    Upload Migration File  valid-groupid-EB/MEF_00-00-00-00-00-00-00-93_2_2.xml
    Upload Migration File  valid-groupid-EB/MVF_00-00-00-00-00-00-00-90_2_2.xml
    Upload Migration File  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Upload Migration File  valid-groupid-EC/MEF_00-00-00-00-00-00-00-93_3_3.xml
    Upload Migration File  valid-groupid-EC/MVF_00-00-00-00-00-00-00-90_3_3.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  18  0

    # Upload 1 migration file set (GroupID:EA)- 75x installations
    # Viable installations: 2),  3),  4),  5), 11), 12), 13), 14), 15), 16), 17), 18), 19), 20)
    #   21), 22), 23), 24), 25), 26), 27), 28), 29), 31), 32), 33), 34), 40), 41), 42), 43), 44)
    #   45), 46), 47), 48), 49), 50), 51), 52), 53), 54), 55), 56), 57), 58), 60), 61), 62), 63)
    #   69), 70), 71), 72), 73), 74), 75), 76), 77), 78), 79), 80), 81), 82), 83), 84), 85), 86), 87)
    # Duplicated installations: 88), 89, 90)
    # Replaced installations: 6), 35), 64)
    Upload Migration File  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_4_0.xml
    Upload Migration File  valid-groupid-EB/MEF_00-00-00-00-00-00-00-93_4_4.xml
    Upload Migration File  valid-groupid-EB/MVF_00-00-00-00-00-00-00-90_4_4.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_4_0.xml  installation_index=74
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait 95 Seconds Until Table ${migration_duplicated_device} Has 3 Records In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  87  0

    # Ensure that the list "${duplicated_devices_list}" which contains the information of each expected
    # duplicated device, match the information stored on DB
    ${index}  Set Variable  0
   FOR    ${device}    IN    @{duplicated_devices_list}
        # The new Trilliant installation ${device.chf_id} contains a Device ${device.device_id} that
        # is already being used in the existing viable Trilliant installation ${device.viable_chf_id}
        Validate Value Table  ${device.chf_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${chf_column}  ${index}  0
        Validate Value Table  ${device.device_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${device_column}  ${index}  0
        # Validate on DB that the table "migration_duplicated_device", contains all the expected information
        Validate Values From Migration Duplicated Devices  ${device.chf_id}  EB  ${device.req_party_id}  ${device.mcf_counter}  ${device.mef_counter}
        # Check if the viable Trilliant installation found in the table "migration_duplicated_device" was indeed already considered
        # viable and can be found in the table "migration_viable_installation"
        ${viable_chf_id}  Get Viable CHF Id From Migration Duplicated Devices  ${device.chf_id}
        Validate Installation  ${viable_installation}  ${viable_chf_id}
        ${index}  Evaluate  ${index} + 1
   END

#----------------------------------------------------------------------------------------------------

MIGRATION-TRILLIANT-FUNCTIONALITY - Duplicated Device For All Types and All GroupIDs (Input EC)
    [Documentation]  This test consists of validating the behavior of the DCO when exists installations with multiple
    ...  duplicated devices. With regard to all types of devices (CHF, ESME, GPF, GSME, PPMID), as well as all existent
    ...  groups (EA,EB,EC) for Trilliant cohort.
    ...  \\ The DCO will check if there are duplicated devices, through the analysis of the following tags/fields
    ...  “<ESMEDetail>” (which is the type of device that have an encrypted key associated).
    ...  If there is any device id of the type (ESME), already being used in a previous installation in one of
    ...  these tags/fields then the latest installation will be considered duplicated.
    ...  \\ Expected Viable Installations
    ...    \\  1) 10-00-00-00-00-00-00-00        2) 10-00-00-00-00-00-00-01	     3) 10-00-00-00-00-00-00-02		 4) 10-00-00-00-00-00-00-03     5) 10-00-00-00-00-00-00-04	     6) 10-00-00-00-00-00-00-10      7) 10-00-00-00-00-00-00-20	     8) 10-00-00-00-00-00-00-30
    ...    \\  9) 10-00-00-00-00-00-00-40       10) 10-00-00-00-00-00-00-50	    11) 10-00-00-00-00-00-25-00     12) 10-00-00-00-00-00-29-00    13) 10-00-00-00-00-00-33-00      14) 10-00-00-00-00-00-37-00	    15) 10-00-00-00-00-01-00-00     16) 10-00-00-00-00-05-00-00
    ...    \\ 17) 10-00-00-00-00-09-00-00       18) 10-00-00-00-00-13-00-00	    19) 10-00-00-00-00-17-00-00	    20) 10-00-00-00-00-21-00-00    21) 10-00-00-00-00-25-00-00      22) 10-00-00-00-00-29-00-00	    23) 10-00-00-00-00-33-00-00     24) 10-00-00-00-00-37-00-00
    ...    \\ 25) 10-00-00-00-00-41-00-00       26) 10-00-00-00-00-45-00-00	    27) 10-00-00-00-00-49-00-00		28) 10-00-00-00-00-53-00-00    29) 10-00-00-00-00-57-00-00      30) 20-00-00-00-00-00-00-00	    31) 20-00-00-00-00-00-00-01     32) 20-00-00-00-00-00-00-02
    ...    \\ 33) 20-00-00-00-00-00-00-03       34) 20-00-00-00-00-00-00-04	    35) 20-00-00-00-00-00-00-10	    36) 20-00-00-00-00-00-00-20    37) 20-00-00-00-00-00-00-30      38) 20-00-00-00-00-00-00-40	    39) 20-00-00-00-00-00-00-50     40) 20-00-00-00-00-00-25-00
    ...    \\ 41) 20-00-00-00-00-00-29-00       42) 20-00-00-00-00-00-33-00	    43) 20-00-00-00-00-00-37-00	    44) 20-00-00-00-00-01-00-00    45) 20-00-00-00-00-05-00-00      46) 20-00-00-00-00-09-00-00	    47) 20-00-00-00-00-13-00-00     48) 20-00-00-00-00-17-00-00
    ...    \\ 49) 20-00-00-00-00-21-00-00       50) 20-00-00-00-00-25-00-00	    51) 20-00-00-00-00-29-00-00	    52) 20-00-00-00-00-33-00-00    53) 20-00-00-00-00-37-00-00      54) 20-00-00-00-00-41-00-00     55) 20-00-00-00-00-45-00-00     56) 20-00-00-00-00-49-00-00
    ...    \\ 57) 20-00-00-00-00-53-00-00       58) 20-00-00-00-00-57-00-00	    59) 30-00-00-00-00-00-00-00	    60) 30-00-00-00-00-00-00-01    61) 30-00-00-00-00-00-00-02      62) 30-00-00-00-00-00-00-03	    63) 30-00-00-00-00-00-00-04     64) 30-00-00-00-00-00-00-10
    ...    \\ 65) 30-00-00-00-00-00-00-20       66) 30-00-00-00-00-00-00-30	    67) 30-00-00-00-00-00-00-40	    68) 30-00-00-00-00-00-00-50    69) 30-00-00-00-00-00-25-00      70) 30-00-00-00-00-00-29-00	    71) 30-00-00-00-00-00-33-00     72) 30-00-00-00-00-00-37-00
    ...    \\ 73) 30-00-00-00-00-01-00-00       74) 30-00-00-00-00-05-00-00	    75) 30-00-00-00-00-09-00-00	    76) 30-00-00-00-00-13-00-00    77) 30-00-00-00-00-17-00-00      78) 30-00-00-00-00-21-00-00	    79) 30-00-00-00-00-25-00-00     80) 30-00-00-00-00-29-00-00
    ...    \\ 81) 30-00-00-00-00-33-00-00       82) 30-00-00-00-00-37-00-00	    83) 30-00-00-00-00-41-00-00	    84) 30-00-00-00-00-45-00-00    85) 30-00-00-00-00-49-00-00      86) 30-00-00-00-00-53-00-00	    87) 30-00-00-00-00-57-00-00
    ...  \\
    ...  \\
    ...  \\ Expected Duplicated Installations
    ...     \\  88) 10-00-00-00-00-00-21-00 	89) 20-00-00-00-00-00-21-00     90) 30-00-00-00-00-00-21-00
    [Tags]  DCO-16724  DCO_POSITIVE_TEST  DCO-13801
    # Upload of 3 migration file sets (GroupID:EA,EB,EC) - 18x installations
    #  Viable installations:
    #  6x >>> EA- 1), 6), 7), 8), 9), 10)
    #  6x >>> EB- 30), 35), 36), 37), 38), 39)
    #  6x >>> EC- 59), 64), 65), 66), 67), 68)
    Upload Migration File  valid-groupid-EA/MCF_00-00-00-00-00-00-00-93_1_0.xml
    Upload Migration File  valid-groupid-EA/MEF_00-00-00-00-00-00-00-93_1_1.xml
    Upload Migration File  valid-groupid-EA/MVF_00-00-00-00-00-00-00-90_1_1.xml
    Upload Migration File  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_2_0.xml
    Upload Migration File  valid-groupid-EB/MEF_00-00-00-00-00-00-00-93_2_2.xml
    Upload Migration File  valid-groupid-EB/MVF_00-00-00-00-00-00-00-90_2_2.xml
    Upload Migration File  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Upload Migration File  valid-groupid-EC/MEF_00-00-00-00-00-00-00-93_3_3.xml
    Upload Migration File  valid-groupid-EC/MVF_00-00-00-00-00-00-00-90_3_3.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_3_0.xml
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  18  0

    # Upload 1 migration file set (GroupID:EA)- 75x installations
    # Viable installations: 2),  3),  4),  5), 11), 12), 13), 14), 15), 16), 17), 18), 19), 20)
    #   21), 22), 23), 24), 25), 26), 27), 28), 29), 31), 32), 33), 34), 40), 41), 42), 43), 44)
    #   45), 46), 47), 48), 49), 50), 51), 52), 53), 54), 55), 56), 57), 58), 60), 61), 62), 63)
    #   69), 70), 71), 72), 73), 74), 75), 76), 77), 78), 79), 80), 81), 82), 83), 84), 85), 86), 87)
    # Duplicated installations: 88), 89, 90)
    # Replaced installations: 6), 35), 64)
    Upload Migration File  valid-groupid-EC/MCF_00-00-00-00-00-00-00-93_4_0.xml
    Upload Migration File  valid-groupid-EC/MEF_00-00-00-00-00-00-00-93_4_4.xml
    Upload Migration File  valid-groupid-EC/MVF_00-00-00-00-00-00-00-90_4_4.xml

    ${chf_id}=  Get CHF DeviceID  valid-groupid-EB/MCF_00-00-00-00-00-00-00-93_4_0.xml  installation_index=74
    Wait 95 Seconds Until Column ${chf_column} In Table ${viable_installation} Has Value ${chf_id} In Migration Database
    Wait 95 Seconds Until Table ${migration_duplicated_device} Has 3 Records In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  87  0

    # Ensure that the list "${duplicated_devices_list}" which contains the information of each expected
    # duplicated device, match the information stored on DB
    ${index}  Set Variable  0
   FOR    ${device}    IN    @{duplicated_devices_list}
        # The new Trilliant installation ${device.chf_id} contains a Device ${device.device_id} that
        # is already being used in the existing viable Trilliant installation ${device.viable_chf_id}
        Validate Value Table  ${device.chf_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${chf_column}  ${index}  0
        Validate Value Table  ${device.device_id}  ${migration_duplicated_device}${SPACE}${chf_column_order_asc}  ${device_column}  ${index}  0
        # Validate on DB that the table "migration_duplicated_device", contains all the expected information
        Validate Values From Migration Duplicated Devices  ${device.chf_id}  EC  ${device.req_party_id}  ${device.mcf_counter}  ${device.mef_counter}
        # Check if the viable Trilliant installation found in the table "migration_duplicated_device" was indeed already considered
        # viable and can be found in the table "migration_viable_installation"
        ${viable_chf_id}  Get Viable CHF Id From Migration Duplicated Devices  ${device.chf_id}
        Validate Installation  ${viable_installation}  ${viable_chf_id}
        ${index}  Evaluate  ${index} + 1
   END

#----------------------------------------------------------------------------------------------------

MIGRATION-TRILLIANT-FUNCTIONALITY - Replacement Installation With Duplicated Device ID
    [Documentation]  This test consists on uploading two viable sets with valid installations,
    ...  both for Trilliant. In which the latest installation will replace the previous one,
    ...  that has the same Device Id.
    ...  \\ Information concerning the upload the migration file sets:
    ...  \\ TRILLIANT (MEF_11_11) 2x SMETS1Installation
    ...     \\ CHF_1 - 00-00-00-00-00-03-00-00
    ...         \\ GPF_1  - 00-00-00-00-00-00-00-08
    ...         \\ ESME_1 - 00-00-00-00-00-00-00-09
    ...     \\ CHF_2 - 80-CE-46-D9-41-BD-B5-D9
    ...         \\ GPF_2  - 44-01-39-7D-C0-8E-A5-D9
    ...         \\ ESME_2 - 08-78-1A-74-35-C8-55-D9
    ...  \\ TRILLIANT (MEF_15_15) 1x SMETS1Installation
    ...     \\ CHF_1 - 00-00-00-00-00-03-00-00
    ...         \\ GPF_1  - 00-00-00-00-00-00-00-23
    ...         \\ ESME_1 - 08-78-1A-74-35-C8-55-D9
    ...  \\ The information regarding the discarded installation should be added to the table
    ...  "migration_duplicated_device" contained in the database.
    ...  \\ Viable installations expected:
    ...    \\ 1) 44-01-39-7D-C0-8E-A5-D9 - TRILLIANT (MEF_11_11)
    ...  \\ Discarded installations:
    ...    \\ 1) 00-00-00-00-00-03-00-00 - TRILLIANT (MEF_15_15)
    [Tags]  DCO-16726  DCO_POSITIVE_TEST  DCO-13801
    Upload Migration File  duplicated-deviceids-chfids-tri/MCF_00-00-00-00-00-00-00-93_11_0.xml
    Upload Migration File  duplicated-deviceids-chfids-tri/MEF_00-00-00-00-00-00-00-93_11_11.xml
    Upload Migration File  duplicated-deviceids-chfids-tri/MVF_00-00-00-00-00-00-00-90_11_11.xml

    Wait 95 Seconds Until Table ${viable_installation} Has 2 Records In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  2  0

    Upload Migration File  duplicated-deviceids-chfids-tri/MCF_00-00-00-00-00-00-00-93_15_0.xml
    Upload Migration File  duplicated-deviceids-chfids-tri/MEF_00-00-00-00-00-00-00-93_15_15.xml
    Upload Migration File  duplicated-deviceids-chfids-tri/MVF_00-00-00-00-00-00-00-90_15_15.xml

    Wait 95 Seconds Until Table ${viable_installation} Has 1 Records In Migration Database
    Wait 95 Seconds Until Table ${migration_duplicated_device} Has 1 Records In Migration Database
    Wait Until Keyword Succeeds    35 seconds    5 seconds  Validate Installation Numbers  1  0

    #The replacement of the Trilliant installation 00-00-00-00-00-03-00-00 contains a Device 08-78-1A-74-35-C8-55-D9 that
    #was already in use in the Trilliant installation 44-01-39-7D-C0-8E-A5-D9
    Validate Value Table  00-00-00-00-00-03-00-00  ${migration_duplicated_device}  ${chf_column}  0  0
    Validate Value Table  08-78-1A-74-35-C8-55-D9  ${migration_duplicated_device}  ${device_column}  0  0

    ${viable_chf_id_1}  Get Viable CHF Id From Migration Duplicated Devices  00-00-00-00-00-03-00-00
    Validate Value Table  ${viable_chf_id_1}  ${viable_installation}  ${chf_column}  0  0
    Validate Values From Migration Duplicated Devices  00-00-00-00-00-03-00-00  EB  00-00-00-00-00-00-00-93  15  15

#----------------------------------------------------------------------------------------------------