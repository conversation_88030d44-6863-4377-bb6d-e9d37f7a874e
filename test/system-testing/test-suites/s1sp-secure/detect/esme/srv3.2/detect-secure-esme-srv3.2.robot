*** Settings ***
Test Timeout  5 minutes
Documentation   This test suite validates the DCO's *Full Detect* implementation when a S1SP requests the DCO
...             to generate a SUA for a Secure *ESME* device for *SRV 3.2* and full detect configuration is uploaded.

Test Tags  S1SP_SECURE  DCO_SECURE_SUA  DCO_DETECT  DCO_ESME  DCO_SRV3.2

Library  robot-library/dcovv.py

Resource  robot-resources/s1sp-secure/resource-detect.resource

Suite Setup     Secure Suite Setup
Suite Teardown  Secure Suite Teardown

*** Test Cases ***
DETECT-SECURE-FULL-ESME-SRV3.2 - Success
    [Documentation]  This test validates the DCO response when receiving a valid S1SP request and
    ...              a SUA generation request for such S1SP request.
    ...              The DCO's response is validated to guarantee that the requested ActionId is applicable
    ...              for such SRV, and also that all the constraints applicable for such ActionId have
    ...              the expected values.
	[Tags]  DCO-10944  DCO_POSITIVE_TEST  DCO-9594  DCO-10310  DCO-10803
    [Setup]  Secure Detect Test Setup  success/detect-secure-full-esme-srv3.2-success-duis.xml  SecureEsmeA
    [Template]  Secure Detect Success
    24  success/detect-secure-full-esme-srv3.2-success-action-24.json


DETECT-SECURE-PARTIAL-ESME-SRV3.2 - Invalid Action
    [Documentation]  This test validates that DCO returns an error in response to a SUA generation request
    ...              containing an invalid ActionId for that SRV.
    ...              The error code returned in DCO's response is validated.
	[Tags]  DCO-10994  DCO_NEGATIVE_TEST  DCO-10310  DCO-10803  DCO-9594
    [Setup]  Secure Detect Test Setup  invalid-action/detect-secure-partial-esme-srv3.2-invalid-action-duis.xml  SecureEsmeA

    Secure Detect Invalid Actions  24

#-------------------------------------------------------------------------------
