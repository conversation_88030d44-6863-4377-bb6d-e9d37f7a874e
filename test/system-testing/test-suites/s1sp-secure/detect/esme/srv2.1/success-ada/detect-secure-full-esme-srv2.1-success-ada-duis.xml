<?xml version="1.0" encoding="UTF-8"?>
<sr:Request xmlns:ds="http://www.w3.org/2000/09/xmldsig#"
            xmlns:sr="http://www.dccinterface.co.uk/ServiceUserGateway"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" schemaVersion="1.0">
    <sr:Header>
        <sr:RequestID>90-B3-D5-1F-30-01-00-00:00-DB-12-34-56-78-90-A0:1004</sr:RequestID>
        <sr:CommandVariant>4</sr:CommandVariant>
        <sr:ServiceReference>2.1</sr:ServiceReference>
        <sr:ServiceReferenceVariant>2.1</sr:ServiceReferenceVariant>
    </sr:Header>
  <sr:Body>
    <sr:UpdatePrepayConfiguration>
      <sr:UpdatePrepayConfigElectricity>
        <sr:DebtRecoveryRateCap>4321</sr:DebtRecoveryRateCap>
        <sr:EmergencyCreditLimit>5000</sr:EmergencyCreditLimit>
        <sr:EmergencyCreditThreshold>2000</sr:EmergencyCreditThreshold>
        <sr:LowCreditThreshold>2000</sr:LowCreditThreshold>
        <sr:ElectricityNonDisablementCalendar>
          <sr:ElectricitySpecialDays>
            <sr:SpecialDay index="1">
              <sr:Date>
                <sr:Year>
                  <sr:NonSpecifiedYear/>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>01</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>01</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
            <sr:SpecialDay index="2">
              <sr:Date>
                <sr:Year>
                  <sr:NonSpecifiedYear/>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>12</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>25</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
            <sr:SpecialDay index="3">
              <sr:Date>
                <sr:Year>
                  <sr:NonSpecifiedYear/>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>12</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>26</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
            <sr:SpecialDay index="4">
              <sr:Date>
                <sr:Year>
                  <sr:SpecifiedYear>2019</sr:SpecifiedYear>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>03</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>25</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
            <sr:SpecialDay index="5">
              <sr:Date>
                <sr:Year>
                  <sr:SpecifiedYear>2019</sr:SpecifiedYear>
                </sr:Year>
                <sr:Month>
                  <sr:SpecifiedMonth>03</sr:SpecifiedMonth>
                </sr:Month>
                <sr:DayOfMonth>
                  <sr:SpecifiedDayOfMonth>28</sr:SpecifiedDayOfMonth>
                </sr:DayOfMonth>
                <sr:DayOfWeek>
                  <sr:NonSpecifiedDayOfWeek/>
                </sr:DayOfWeek>
              </sr:Date>
            </sr:SpecialDay>
          </sr:ElectricitySpecialDays>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>1</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>2</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>3</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>4</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>5</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
            </sr:SpecialDaysApplicability>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Saturday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Sunday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>00:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-27Z</sr:StartDate>
              <sr:EndDate>2019-03-28Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Friday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>00:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-27Z</sr:StartDate>
              <sr:EndDate>2019-03-28Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>STOP</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Friday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>08:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-27Z</sr:StartDate>
              <sr:EndDate>2019-03-28Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Friday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>18:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2018-10-27Z</sr:StartDate>
              <sr:EndDate>2019-03-28Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>1</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>2</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>3</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>4</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
              <sr:SpecialDayApplicability>
                <sr:SpecialDayID>5</sr:SpecialDayID>
              </sr:SpecialDayApplicability>
            </sr:SpecialDaysApplicability>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Saturday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Sunday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>00:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2019-03-29Z</sr:StartDate>
              <sr:EndDate>2020-10-25Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>START</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Friday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>00:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2019-03-29Z</sr:StartDate>
              <sr:EndDate>2019-10-25Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
          <sr:ElectricityNonDisablementSchedule>
            <sr:NonDisablementScript>STOP</sr:NonDisablementScript>
            <sr:SpecialDaysApplicability/>
            <sr:DaysOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Monday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Tuesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Wednesday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Thursday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
              <sr:DayOfWeekApplicability>
                <sr:DayOfWeekID>Friday</sr:DayOfWeekID>
              </sr:DayOfWeekApplicability>
            </sr:DaysOfWeekApplicability>
            <sr:ScheduleDatesAndTime>
              <sr:SwitchTime>07:00:00.00Z</sr:SwitchTime>
              <sr:StartDate>2019-03-29Z</sr:StartDate>
              <sr:EndDate>2019-10-25Z</sr:EndDate>
            </sr:ScheduleDatesAndTime>
          </sr:ElectricityNonDisablementSchedule>
        </sr:ElectricityNonDisablementCalendar>
        <sr:MaxMeterBalance>560000</sr:MaxMeterBalance>
        <sr:MaxCreditThreshold>9000</sr:MaxCreditThreshold>
      </sr:UpdatePrepayConfigElectricity>
    </sr:UpdatePrepayConfiguration>
  </sr:Body>
    <ds:Signature>
        <!-- NOTE: The digest, signature and serial number values below are placeholders and should not be used for actual authentication purposes. -->
        <ds:SignedInfo>
            <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
            <ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/>
            <ds:Reference URI="">
                <ds:Transforms>
                    <ds:Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/>
                </ds:Transforms>
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>ZGVmYXVsdA==</ds:DigestValue>
            </ds:Reference>
        </ds:SignedInfo>
        <ds:SignatureValue>ZGVmYXVsdA==</ds:SignatureValue>
        <ds:KeyInfo>
            <ds:X509Data>
                <ds:X509IssuerSerial>
                    <ds:X509IssuerName>CN=U1, OU=07</ds:X509IssuerName>
                    <ds:X509SerialNumber>1234567890</ds:X509SerialNumber>
                </ds:X509IssuerSerial>
            </ds:X509Data>
        </ds:KeyInfo>
    </ds:Signature>
</sr:Request>
