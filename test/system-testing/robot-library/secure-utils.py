import os
import json
import pyasn1_1
import base64
import datetime
import time_utils
from robot.api import logger
from robot.libraries.XML import XML


def create_constraints_object_from_file(file_path):
    if not os.path.isfile(file_path):
        raise ValueError('File "{}" not found'.format(file_path))
    with open(file_path, 'r') as f:
        constraints = json.load(f)

    constraints_list = []
    for constraint in constraints:
        constraint_obj = pyasn1_1.create_constraint(constraint['constraintId'], constraint['critical'], constraint['constraintValue'])
        constraints_list.append(constraint_obj)
    return pyasn1_1.create_constraints_object(constraints_list)


def convert_base64_to_hex(s):
    result = base64.b64decode(s).hex()
    return result

def convert_hex_to_base64(value):
    result = base64.b64encode(value.decode('hex'))
    # result = value.decode('hex').encode('base64')
    return result


def get_billing_instant(duis_file, periodicity):
    """
    Calculates the billing instant that DCO should return based in the DUIS file and periodicity
    :param duis_file: path to the DUIS file to read the values.
    :param periodicity: periodicity that should be used in the calculation.
    :return: the number of seconds in hex format since 01/01/2000 00:00:00 to the billing instant calculated
    """

    current_datetime = datetime.datetime.utcnow()
    # current_datetime = datetime.datetime(2019, 2, 2, 14, 00, 00)

    target_datetime = None
    xml = XML().parse_xml(duis_file, strip_namespaces=True)

    billing_time_txt = XML().get_element_text(xml, xpath='.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/BillingTime')
    logger.info('\nRequest BillingTime: {}'.format(billing_time_txt), also_console=True)

    # add digits to convert 00Z into the fractional format .000000Z
    billing_time = billing_time_txt.rstrip('Z')+'0000Z'
    request_datetime = datetime.datetime.strptime(billing_time, "%H:%M:%S.%fZ")

    if periodicity.lower() == 'daily':
        logger.info('Periodicity: Daily', also_console=True)
        target_datetime = _calculate_time(current_datetime, request_datetime)
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time

    elif periodicity.lower() == 'monthly':
        monthly_day = int(XML().get_element_text(xml,
                                             '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Monthly'))
        logger.info('Periodicity: Monthly | Day of month: {}'.format(monthly_day), also_console=True)

        target_datetime = _monthly_billing(current_datetime, monthly_day, request_datetime)
        assert target_datetime.day == monthly_day
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time

    elif periodicity.lower() == 'weekly':
        weekly_week_day = int(XML().get_element_text(xml,
                                                 '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Weekly'))
        logger.info('Periodicity: Weekly | Day of week: {}'.format(weekly_week_day), also_console=True)
        logger.info('Current day of week: {}'.format(current_datetime.isoweekday()), also_console=True)
        # DUIS week days (positive integer between 1 and 7) 1-Monday 7-Sunday

        target_datetime = _weekly_billing(current_datetime, weekly_week_day, request_datetime)
        assert target_datetime.isoweekday() == weekly_week_day
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time

    elif periodicity.lower() == 'quarterly':
        quarterly_day_of_month = int(XML().get_element_text(xml,
                                                            '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Quarterly/DayOfMonth'))
        quarterly_start_month = int(XML().get_element_text(xml,
                                                           '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Quarterly/BillingPeriodStartMonth'))
        logger.info(
            'Periodicity: Quarterly | DayOfMonth: {} | BillingPeriodStartMonth: {}'.format(quarterly_day_of_month,
                                                                                           quarterly_start_month),
            also_console=True)

        target_datetime = _quarterly_billing(current_datetime, quarterly_start_month, quarterly_day_of_month,
                                          request_datetime)

        assert target_datetime.day == quarterly_day_of_month
        # assert target_datetime.month == quarterly_start_month
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time

    elif periodicity.lower() == 'sixmonthly':
        sixmonthly_day_of_month = int(XML().get_element_text(xml,
                                                         '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/SixMonthly/DayOfMonth'))
        sixmonthly_start_month = int(XML().get_element_text(xml,
                                                        '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/SixMonthly/BillingPeriodStartMonth'))
        logger.info(
            'Periodicity: SixMonthly | DayOfMonth: {} | BillingPeriodStartMonth: {}'.format(sixmonthly_day_of_month,
                                                                                            sixmonthly_start_month),
            also_console=True)
    
        target_datetime = _sixmonthly_billing(current_datetime, sixmonthly_start_month, sixmonthly_day_of_month,
                                          request_datetime)

        assert target_datetime.day == sixmonthly_day_of_month
        # assert target_datetime.month == sixmonthly_start_month
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time
    
    elif periodicity.lower() == 'yearly':
        yearly_day_of_month = int(XML().get_element_text(xml,
                                                     '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Yearly/DayOfMonth'))
        yearly_start_month = int(XML().get_element_text(xml,
                                                    '.Body/UpdateDeviceConfigurationBillingCalendar/ElectricityBillingCalendar/Yearly/BillingPeriodStartMonth'))
        logger.info(
            'Periodicity: Yearly | DayOfMonth: {} | BillingPeriodStartMonth: {}'.format(yearly_day_of_month,
                                                                                        yearly_start_month),
            also_console=True)
    
        target_datetime = _yearly_billing(current_datetime, yearly_start_month, yearly_day_of_month, request_datetime)

        assert target_datetime.day == yearly_day_of_month
        assert target_datetime.month == yearly_start_month
        assert target_datetime.time().strftime('%H:%M:%S.%fZ') == billing_time

    else:
        raise ValueError('Unable to calculate a target datetime. Invalid periodicity: {}'.format(periodicity))

    logger.info('Current datetime: {} | Target datetime: {}'.format(current_datetime.isoformat(),
                                                                    target_datetime.isoformat()), also_console=True)
    target_string = target_datetime.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
    hex_target_string = time_utils.get_seconds_from_date(target_string).replace('0x', '')
    return hex_target_string


def _quarterly_billing(current_datetime, start_month, day_of_month, request_datetime):
    billing_months = _get_billing_months(start_month, 4)
    return _get_last_billing_datetime(billing_months, current_datetime, day_of_month, request_datetime)


def _sixmonthly_billing(current_datetime, start_month, day_of_month, request_datetime):
    billing_months = _get_billing_months(start_month, 2)
    return _get_last_billing_datetime(billing_months, current_datetime, day_of_month, request_datetime)


def _yearly_billing(current_datetime, start_month, day_of_month, request_datetime):
    billing_months = [start_month]
    return _get_last_billing_datetime(billing_months, current_datetime, day_of_month, request_datetime)


def _weekly_billing(current_datetime, weekly_week_day, request_datetime):
    # If the week day didn't occurred yet this week, or today is the same week day but the request time is
    # in the future, go to that week day in the last week
    if current_datetime.isoweekday() < weekly_week_day \
            or (current_datetime.isoweekday() == weekly_week_day and current_datetime.time() <= request_datetime.time()):
        new_date = current_datetime - datetime.timedelta(days=7-(weekly_week_day - current_datetime.isoweekday()))
        target_datetime = datetime.datetime.combine(new_date.date(), request_datetime.time())

    else:
        # The week day already occurred this week. move back to that week day in the current week
        new_date = current_datetime - datetime.timedelta(days=current_datetime.isoweekday() - weekly_week_day)
        target_datetime = datetime.datetime.combine(new_date.date(), request_datetime.time())
    return target_datetime


def _monthly_billing(current_datetime, monthly_day, request_datetime):
    # If the day didn't occurred yet this month, or today is the request day but the request time is in the future,
    # go to that day in the month week
    if current_datetime.day < monthly_day or \
            (current_datetime.day == monthly_day and current_datetime.time() <= request_datetime.time()):
        new_date = _add_months(current_datetime, -1).replace(day=monthly_day)
        target_datetime = datetime.datetime.combine(new_date.date(), request_datetime.time())

    else:
        # The day already occurred this month. move back to that day in the current month
        target_datetime = datetime.datetime.combine(current_datetime.date().replace(day=monthly_day),
                                                    request_datetime.time())
    return target_datetime


def _add_months(current_datetime, months_to_add):
    new_date = datetime.date(current_datetime.year + ((current_datetime.month - 1 + months_to_add) // 12),
                             ((current_datetime.month - 1 + months_to_add) % 12 + 1), current_datetime.day)
    new_date_time = datetime.datetime.combine(new_date, current_datetime.time())
    return new_date_time


def _calculate_time(current_datetime, request_datetime):
    if current_datetime.time() <= request_datetime.time():
        # move to the previous day
        new_date = current_datetime - datetime.timedelta(days=1)
        target_datetime = datetime.datetime.combine(new_date.date(), request_datetime.time())
    else:
        target_datetime = datetime.datetime.combine(current_datetime.date(), request_datetime.time())
    return target_datetime


def _get_last_billing_datetime(billing_months, current_datetime, day_of_month, request_datetime):
    if current_datetime.month not in billing_months or \
            (current_datetime.month in billing_months and current_datetime.day < day_of_month) or \
            (current_datetime.month in billing_months and current_datetime.day == day_of_month and current_datetime.time() < request_datetime.time()):

        new_date = current_datetime.replace(day=day_of_month)
        new_date = _add_months(new_date, -1)
        while new_date.month not in billing_months:
            new_date = _add_months(new_date, -1)

        target_datetime = datetime.datetime.combine(new_date.date(), request_datetime.time())
    else:
        new_date = datetime.date(year=current_datetime.year, month=current_datetime.month, day=day_of_month)
        target_datetime = datetime.datetime.combine(new_date, request_datetime.time())
    return target_datetime


def _get_billing_months(start_month, num_months):
    increment = 12 / num_months
    months = []
    month = start_month
    for m in range(0, num_months):
        month = month + increment
        if month % 12 != 0:
            month = month % 12
        months.append(month)
    return months
