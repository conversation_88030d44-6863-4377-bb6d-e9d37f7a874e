*** Settings ***
Resource  robot-resources/s1sp-trilliant/resource-common.resource
Resource  robot-resources/s1sp-common/resource-errors.resource

*** Keywords ***

NoSRV Detect Load Profile Suite Setup
    [Documentation]  Initialize the test suite for NoSRV Detect Load Profile tests.

    Trilliant Suite Setup
    Populate Trilliant Devices

NoSRV Detect Load Profile Suite Teardown
    [Documentation]  Clean up after the test suite.

    Trilliant Suite Teardown

NoSRV Detect Load Profile Test Setup
    [Documentation]  Set up NoSRV session and subsession for Load Profile detect tests.

    ${noSRVsessionId}  ${noSRVsubsessionId} =  Trilliant Open NosrvSession And NosrvSubsession  LgEsmeA

    Set Test Variable  ${noSRVsessionId}
    Set Test Variable  ${noSRVsubsessionId}

NoSRV Detect Load Profile Test Teardown
    [Documentation]  Clean up NoSRV session and subsession after each test.

    Run Keyword If Test Failed  Log  Test failed, cleaning up sessions
    Run Keyword If  '${noSRVsessionId}' != '${EMPTY}' and '${noSRVsubsessionId}' != '${EMPTY}'
    ...  Trilliant S1SP Closes NosrvSession And NosrvSubsession  ${noSRVsessionId}  ${noSRVsubsessionId}

NoSRV Detect Load Profile Request
    [Arguments]  ${hexFile}
    ...          ${scenarioDeviceName}=LgEsmeA
    ...          ${encryptionKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Guek.Package]
    ...          ${authenticationKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Ak.Package]
    ...          ${initialisationVector64}=${scenario}[system-space.Dco.InitialisationVector.Base64]
    [Documentation]  Send a NoSRV detect request for Load Profile command validation.
    ...              This keyword sends a DLMS command through the NoSRV authenticate service
    ...              which triggers the detect validation.

    ${encryptionKey64} =  Get File  ${encryptionKeyFile}
    ${authenticationKey64} =  Get File  ${authenticationKeyFile}

    ${responseRaw} =  Get File  %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/nosrv-detect-load-profile/${hexFile}
    # Enhanced cleaning of hex data - remove all non-hex characters
    ${responseRawStripped} =  Strip String  ${responseRaw}
    ${responseRawStripped} =  Replace String Using Regexp  ${responseRawStripped}  [^0-9A-Fa-f]  ${EMPTY}
    ${payload} =  Get Base64 From Hex  ${responseRawStripped}

    ${future}=  Client Sends Async Http request to  ${scenario}[system-space.Dco.TrilliantNosrvAuthenticateService]
    ...  with method:  POST
    ...  with header parameters:
    ...     accept: application/json
    ...     Content-Type: application/json
    ...     sessionId: ${noSRVsessionId}
    ...     subsessionId: ${noSRVsubsessionId}
    ...  with body:
    ...  {
    ...    "encryptionKey": "${encryptionKey64}",
    ...    "authenticationKey": "${authenticationKey64}",
    ...    "cleartextMessage": "${payload}",
    ...    "initialisationVector": "${initialisationVector64}"
    ...  }

    [Return]  ${future}

NoSRV Detect Load Profile Request With Session
    [Arguments]  ${hexFile}
    ...          ${sessionId}
    ...          ${subsessionId}
    ...          ${scenarioDeviceName}=LgEsmeA
    ...          ${encryptionKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Guek.Package]
    ...          ${authenticationKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Ak.Package]
    ...          ${initialisationVector64}=${scenario}[system-space.Dco.InitialisationVector.Base64]
    [Documentation]  Send a detect request using regular SRV session/subsession (for negative testing).
    ...              This is used to test that Load Profile commands are rejected in SRV context.

    ${encryptionKey64} =  Get File  ${encryptionKeyFile}
    ${authenticationKey64} =  Get File  ${authenticationKeyFile}

    ${responseRaw} =  Get File  %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/nosrv-detect-load-profile/${hexFile}
    # Enhanced cleaning of hex data - remove all non-hex characters
    ${responseRawStripped} =  Strip String  ${responseRaw}
    ${responseRawStripped} =  Replace String Using Regexp  ${responseRawStripped}  [^0-9A-Fa-f]  ${EMPTY}
    ${payload} =  Get Base64 From Hex  ${responseRawStripped}

    ${future}=  Client Sends Async Http request to  ${scenario}[system-space.Dco.TrilliantAuthenticateService]
    ...  with method:  POST
    ...  with header parameters:
    ...     accept: application/json
    ...     Content-Type: application/json
    ...     sessionId: ${sessionId}
    ...     subsessionId: ${subsessionId}
    ...  with body:
    ...  {
    ...    "encryptionKey": "${encryptionKey64}",
    ...    "authenticationKey": "${authenticationKey64}",
    ...    "cleartextMessage": "${payload}",
    ...    "initialisationVector": "${initialisationVector64}"
    ...  }

    [Return]  ${future}

NoSRV Detect Load Profile Success Template
    [Arguments]  ${hexFile}
    [Documentation]  Template keyword for testing successful Load Profile detect scenarios.

    ${future}=  NoSRV Detect Load Profile Request  ${hexFile}
    Validate That Service Replies Async With Status  ${future}  200

NoSRV Detect Load Profile Failure Template
    [Arguments]  ${hexFile}  ${expectedErrorCode}
    [Documentation]  Template keyword for testing failed Load Profile detect scenarios.

    ${future}=  NoSRV Detect Load Profile Request  ${hexFile}
    Async HTTP Service Replies with Status and Error Codes  ${future}  400  ${expectedErrorCode}

Create Load Profile DLMS Command
    [Arguments]  ${classId}=7
    ...          ${instanceId}=0.0.99.1.0.255
    ...          ${attributeId}=3
    ...          ${requestType}=set
    ...          ${captureObjects}=@{EMPTY}
    ...          ${capturePeriod}=${EMPTY}
    [Documentation]  Helper keyword to create DLMS commands for Load Profile testing.
    ...              This can be used to generate test data programmatically.

    # This is a helper keyword that could be expanded to generate DLMS hex data
    # For now, we'll rely on pre-created hex files
    Log  Creating DLMS command with classId=${classId}, instanceId=${instanceId}, attributeId=${attributeId}

Validate Load Profile Array Structure
    [Arguments]  ${arrayElements}  ${expectedVersion}
    [Documentation]  Helper keyword to validate Load Profile capture_objects array structure.
    ...              Validates that the array matches either V1 or V2 configuration.

    # This could be expanded to validate array structure programmatically
    Log  Validating array structure for version ${expectedVersion}

Generate Load Profile Test Data
    [Arguments]  ${profileNumber}  ${version}  ${dataIndex}=0
    [Documentation]  Helper keyword to generate test data for different Load Profile scenarios.
    ...              This could be used to create test data files programmatically.

    Log  Generating test data for Load Profile ${profileNumber}, version ${version}, data index ${dataIndex}

NoSRV Detect Load Profile Raw Request
    [Arguments]    ${hex_data}
    ...          ${scenarioDeviceName}=LgEsmeA
    ...          ${encryptionKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Guek.Package]
    ...          ${authenticationKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Ak.Package]
    ...          ${initialisationVector64}=${scenario}[system-space.Dco.InitialisationVector.Base64]
    [Documentation]    Sends a NoSRV Detect request with raw hex data instead of reading from file
    ...                This avoids potential file formatting issues

    ${encryptionKey64} =  Get File  ${encryptionKeyFile}
    ${authenticationKey64} =  Get File  ${authenticationKeyFile}

    # Ensure hex data is clean - apply strict cleaning to remove any problematic characters
    ${hex_data_cleaned} =  Strip String  ${hex_data}
    ${hex_data_cleaned} =  Replace String Using Regexp  ${hex_data_cleaned}  [^0-9A-Fa-f]  ${EMPTY}

    # Debug logs to verify hex data
    Log  Original hex length: ${hex_data}
    Log  Cleaned hex: ${hex_data_cleaned}

    ${payload} =  Get Base64 From Hex  ${hex_data_cleaned}
    Log  Base64 payload: ${payload}

    ${future}=  Client Sends Async Http request to  ${scenario}[system-space.Dco.TrilliantNosrvAuthenticateService]
    ...  with method:  POST
    ...  with header parameters:
    ...     accept: application/json
    ...     Content-Type: application/json
    ...     sessionId: ${noSRVsessionId}
    ...     subsessionId: ${noSRVsubsessionId}
    ...  with body:
    ...  {
    ...    "encryptionKey": "${encryptionKey64}",
    ...    "authenticationKey": "${authenticationKey64}",
    ...    "cleartextMessage": "${payload}",
    ...    "initialisationVector": "${initialisationVector64}"
    ...  }

    [Return]  ${future}

NoSRV Detect Load Profile Base64 Request
    [Arguments]    ${base64_payload}
    ...          ${scenarioDeviceName}=LgEsmeA
    ...          ${encryptionKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Guek.Package]
    ...          ${authenticationKeyFile}=%{PYTHONPATH}/scenarios/${scenario}[system-space.${scenarioDeviceName}.DlmsClients.Management.ValidKey.Ak.Package]
    ...          ${initialisationVector64}=${scenario}[system-space.Dco.InitialisationVector.Base64]
    [Documentation]    Sends a NoSRV Detect request with Base64 payload directly
    ...                This bypasses hex conversion issues

    ${encryptionKey64} =  Get File  ${encryptionKeyFile}
    ${authenticationKey64} =  Get File  ${authenticationKeyFile}

    Log  Using Base64 payload directly: ${base64_payload}

    ${future}=  Client Sends Async Http request to  ${scenario}[system-space.Dco.TrilliantNosrvAuthenticateService]
    ...  with method:  POST
    ...  with header parameters:
    ...     accept: application/json
    ...     Content-Type: application/json
    ...     sessionId: ${noSRVsessionId}
    ...     subsessionId: ${noSRVsubsessionId}
    ...  with body:
    ...  {
    ...    "encryptionKey": "${encryptionKey64}",
    ...    "authenticationKey": "${authenticationKey64}",
    ...    "cleartextMessage": "${base64_payload}",
    ...    "initialisationVector": "${initialisationVector64}"
    ...  }

    [Return]  ${future}

Fix Hex File
    [Arguments]    ${hexFile}
    [Documentation]    Creates a temporary hex file with properly formatted content
    ...               This can be used to fix problematic hex files on the fly

    ${hexPath} =    Set Variable    %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/nosrv-detect-load-profile/${hexFile}
    ${hexRaw} =     Get File    ${hexPath}
    ${hexCleaned} =  Strip String    ${hexRaw}
    ${hexCleaned} =  Replace String Using Regexp    ${hexCleaned}    [^0-9A-Fa-f]    ${EMPTY}

    # Log for debugging
    Log    Original hex: ${hexRaw}
    Log    Cleaned hex: ${hexCleaned}

    ${tempPath} =    Set Variable    %{PYTHONPATH}/test-suites/s1sp-trilliant/command-manager/nosrv-detect-load-profile/temp_${hexFile}
    Create File    ${tempPath}    ${hexCleaned}
    [Return]    temp_${hexFile}
