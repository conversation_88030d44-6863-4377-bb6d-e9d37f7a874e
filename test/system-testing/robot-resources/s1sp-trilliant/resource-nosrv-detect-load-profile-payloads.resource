*** Settings ***
Documentation    Contains verified working payloads for Load Profile tests
...              These tested payloads can be used to replace problematic hex files

*** Variables ***
# Load Profile 1 - Using exact hex from XML examples without any modifications
${LP1_V1_CLEAN}=         C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

${LP1_V2_CLEAN}=         C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100010800FF0F02120000060000070800

${LP1_DATA_INDEX_0_CLEAN}=  C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F020000060000070800

${LP1_DATA_INDEX_1_CLEAN}=  C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F020100060000070800

${LP1_DATA_INDEX_255_CLEAN}=  C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02FF00060000070800

${LP1_ATTR3_V1_CLEAN}=      C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F0212000000

${LP1_ATTR3_V2_CLEAN}=      C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100010800FF0F0212000000

${LP1_ATTR4_CLEAN}=         C101410007000063010000FF04000600000708

# Load Profile 2
${LP2_V1_CLEAN}=         C104410200070000630101FF030000070000630101FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800

${LP2_V2_CLEAN}=         C104410200070000630101FF030000070000630101FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800

${LP2_ATTR3_V1_CLEAN}=      C1014100070000630101FF03000105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100020800FF0F02120000020412000509060100030800FF0F02120000020412000509060100040800FF0F0212000000

${LP2_ATTR3_V2_CLEAN}=      C1014100070000630101FF03000105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F0212000000

${LP2_ATTR4_CLEAN}=         C1014100070000630101FF04000600000708

# Helper test exact examples derived directly from the XML
${HELPER_LP1_V1}=        C10441020007000063010000FF03000007000063010000FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

${HELPER_LP2_V1}=        C10441020007000063010100FF03000007000063010100FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800

# Simplified direct hex strings - exact copy of hex from XML examples with minimal cleaning
${XML_LP1_V1}=           C10441020007000063010000FF03000007000063010000FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

${XML_LP2_V1}=           C10441020007000063010100FF03000007000063010100FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800
