*** Settings ***
Documentation    Contains verified working payloads for Load Profile tests
...              These tested payloads can be used to replace problematic hex files

*** Variables ***
# Load Profile 1 - Using your exact working payloads (OBIS: 0000630100FF)
# SetRequestWithList - Both attributes 3+4 V1 (class 5 for 3rd capture object)
${LP1_V1_CLEAN}=         C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

# SetRequestWithList - Both attributes 3+4 V2 (class 3 for 3rd capture object)
${LP1_V2_CLEAN}=         C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100010800FF0F02120000060000070800

# SetRequestNormal - Individual attribute 3 V1 (class 5)
${LP1_ATTR3_V1_CLEAN}=      C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F0212000000

# SetRequestNormal - Individual attribute 3 V2 (class 3)
${LP1_ATTR3_V2_CLEAN}=      C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100010800FF0F0212000000

# SetRequestNormal - Individual attribute 4 (capture_period)
${LP1_ATTR4_CLEAN}=         C101410007000063010000FF04000600000708

# SetRequestNormal - Mixed class IDs (failure scenario - all class 1)
${LP1_MIXED_CLASSES}=       C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000109060100010800FF0F0212000000

# SetRequestNormal - Duplicate OBIS codes (failure scenario - all same OBIS)
${LP1_DUPLICATE_OBIS}=      C101410007000063010000FF03000103020412000809060000010000FF0F02120000020412000809060000010000FF0F02120000020412000809060000010000FF0F0212000000

# Load Profile 2 (OBIS: 0000630101FF) - 5 capture objects
${LP2_V1_CLEAN}=         C104410200070000630101FF030000070000630101FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100020800FF0F02120000020412000509060100030800FF0F02120000020412000509060100040800FF0F02120000060000070800

${LP2_V2_CLEAN}=         C104410200070000630101FF030000070000630101FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800

${LP2_ATTR3_V1_CLEAN}=      C1014100070000630101FF03000105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100020800FF0F02120000020412000509060100030800FF0F02120000020412000509060100040800FF0F0212000000

${LP2_ATTR3_V2_CLEAN}=      C1014100070000630101FF03000105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F0212000000

${LP2_ATTR4_CLEAN}=         C1014100070000630101FF04000600000708

# Test payload - exact copy of your working example
${WORKING_EXAMPLE}=         C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

# Your exact working Base64 payload
${WORKING_BASE64}=          wQRBAgAHAABjAQD/AwAABwAAYwEA/wQAAgEDAgQSAAgJBgAAAQAA/w8CEgAAAgQSAAEJBgAAYAoB/w8CEgAAAgQSAAUJBgEAAQQA/w8CEgAABgAABwg=

# Helper test exact examples derived directly from the XML
${HELPER_LP1_V1}=        C10441020007000063010000FF03000007000063010000FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

${HELPER_LP2_V1}=        C10441020007000063010100FF03000007000063010100FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000309060100020800FF0F02120000020412000309060100030800FF0F02120000020412000309060100040800FF0F02120000060000070800

# Working payloads - using your exact working format (no spaces)
${XML_LP1_V1}=           C104410200070000630100FF030000070000630100FF0400020103020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100010400FF0F02120000060000070800

${XML_LP2_V1}=           C104410200070000630101FF030000070000630101FF0400020105020412000809060000010000FF0F02120000020412000109060000600A01FF0F02120000020412000509060100020800FF0F02120000020412000509060100030800FF0F02120000020412000509060100040800FF0F02120000060000070800
