# Path to the PEM encoded file containing the certification request.
# Provides default values for several parameters.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: no.
csr =

# Path to the PEM encoded file containing the public key to be included in the certificate.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: no.
# If undefined the following steps are performed:
# 1) A new private and public key pair is generated.
# 2) The newly generated private key is saved to the file defined by "out_pri_key".
# 3) The newly generated public key is included in the certificate.
# 4) If "sign_pri_key" is undefined, the certificate is self-signed.
pub_key =

# Path to the PEM encoded file containing the private key to be used to sign the certificate.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: yes if "pub_key" is defined; otherwise no.
sign_pri_key = ../../key.pem

# Path to the DER encoded file containing the issuer certificate.
# Provides default values for several parameters.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: no.
issuer_cert = ../../cert.der

# Path to the file where the certificate file will be saved.
# Mandatory: no.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# If undefined "certificate.crt" is used.
out_cert = cert.der

# Path to the PEM encoded file where the private key will be saved when a new private and public key pair is generated.
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: no.
# If undefined "private_key.pem" is used.
out_pri_key = key.pem

# Certificate file encoding (DER or PEM).
# Possible values: "DER" and "PEM".
# Mandatory: no.
# If undefined "DER" is used.
encoding = 

# Serial number in hexadecimal notation.
# Format: number in hexadecimal notation (e.g. "4fbc525201a1d7586c1bc1c4734deb9f").
# Mandatory: no.
# If undefined a serial number is generated with the length defined by "sn_len".
serial_num = 

# Length of the serial number generated when "serial_num" is undefined.
# Format: number.
# Mandatory: no.
# If undefined the value 16 is used.
sn_len = 

# Subject common name.
# Format: text (e.g. "Z1").
# Mandatory: no
# if undefined the subject will not include a common name.
cn = 

# Subject organizational unit.
# Format: a comma-separated list of at least one element (e.g. "07,DSP").
# Mandatory: no.
# if undefined the subject will not include an organizational unit.
ou = 8A

# Subject entity identifier.
# Format: EUI in hexadecimal notation (e.g. "4f5688d7ec933be2").
# Mandatory: no.
# if undefined the subject will not include an entity identifier.
entity_id = 90B3D51F30000002

# Issuer common name.
# Format: text (e.g. "Z1").
# Mandatory: no.
# If "issuer_cn" is undefined but "issuer_cert" is defined, the issuer certificate common name is used.
# If both "issuer_cn" and "issuer_cert" are undefined, the issuer will not include a common name.
issuer_cn = 

# Issuer organizational unit.
# Format: a comma-separated list of at least one element (e.g. "07,DSP").
# Mandatory: no.
# If "issuer_ou" is undefined but "issuer_cert" is defined, the issuer certificate organizational unit is used.
# If both "issuer_ou" and "issuer_cert" are undefined, the issuer will not include an organizational unit.
issuer_ou = 

# Validity UTC start time.
# Format: YYYY-MM-DD hh:mm:ss (e.g. "2017-01-01 00:00:00").
# Mandatory: no.
# If undefined the current time is used.
not_before = 2018-01-01 00:00:00

# Validity UTC end time.
# Format: YYYY-MM-DD hh:mm:ss (e.g. "2018-01-01 00:00:00").
# Mandatory: no.
# If undefined it will be set to start time plus 2 years.
not_after = 2018-02-01 00:00:00

# Specifies if the certificate policies extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "policies_critical" and "policies_value" are ignored.
# Mandatory: yes.
policies = true

# Specifies if the certificate policies extension will be marked as critical.
# Ignored if "policies" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "policies" is "true"; otherwise no.
policies_critical = true

# Policy identifiers in the certificate policies extension.
# Ignored if "policies" is "false".
# Format: comma-separated list of zero or more object identifiers.
# Examples:
# - List with a single policy identifier: 1.2.826.0.1.8641679.*******
# - List with multiple policy identifiers: 1.2.826.0.1.8641679.*******,1.2.826.0.1. 8641679.*******
# Mandatory: yes if "policies" is "true"; otherwise no.
# If undefined the certificate policies extension will not have any policy identifiers.
policies_value = 1.2.826.0.1.8641679.*******

# Specifies if the authority key identifier extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "authority_key_identifier_critical" and "authority_key_identifier_value" are ignored.
# Mandatory: yes.
authority_key_identifier = true

# Specifies if the authority key identifier extension will be marked as critical.
# Ignored if "authority_key_identifier" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "authority_key_identifier" is "true"; no otherwise.
authority_key_identifier_critical = false

# Key identifier in the authority key identifier extension.
# Ignored if "authority_key_identifier" is "false".
# Format: EUI in hexadecimal notation (e.g. "4f5688d7ec933be2").
# Mandatory: yes if "authority_key_identifier" is "true" and "issuer_cert" is undefined; no otherwise.
# If "authority_key_identifier_value" is undefined but "issuer_cert" is defined, the issuer subject identifier is used.
authority_key_identifier_value = 

# Specifies if the key usage extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "key_usage_critical" and "key_usage_value" are ignored.
# Mandatory: yes.
key_usage = true

# Specifies if the key usage extension will be marked as critical.
# Ignored if "key_usage" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "key_usage" is "true"; otherwise no.
# If undefined "true" is used.
key_usage_critical = true

# Key usages in the key usage extension.
# Ignored if "key_usage" is "false".
# Format: comma-separated list of zero or more of the following values surrounded by square brackets:
# - "DigitalSignature"
# - "ContentCommitment"
# - "KeyEncipherment"
# - "DataEncipherment"
# - "KeyAgreement"
# - "KeyCertificateSignature"
# - "CRLSignature"
# - "EncipherOnly"
# - "DecipherOnly"
# Examples:
# - Empty list: []
# - List with a single key usage: [DigitalSignature]
# - List with multiple policy identifiers: [KeyCertificateSignature,CRLSignature]
# Mandatory: yes if "key_usage" is "true"; otherwise no.
key_usage_value = [DigitalSignature]

# Specifies if the basic constraints extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "basic_constraints_critical", "basic_constraints_ca" and "basic_constraints_path_length" are ignored.
# Mandatory: yes.
basic_constraints = false

# Specifies if the basic constraints extension will be marked as critical.
# Ignored if "basic_constraints" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "basic_constraints" is "true"; otherwise no.
basic_constraints_critical = 

# CA flag in the basic constraints extension.
# Possible values: "true" and "false".
# Mandatory: yes if "basic_constraints" is "true"; otherwise no.
basic_constraints_ca = 

# Path length in the basic constraints extension.
# Format: number.
# Mandatory: no.
# If undefined the basic constraints extension will not include the path length.
basic_constraints_path_length =

# Specifies if the subject key identifier extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "subject_key_identifier_critical" and "subject_key_identifier_value" are ignored.
# Mandatory: yes.
subject_key_identifier = true

# Specifies if the subject key identifier extension will be marked as critical.
# Ignored if "subject_key_identifier" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "subject_key_identifier" is "true"; otherwise no.
subject_key_identifier_critical = false

# Key identifier in the subject key identifier extension.
# Ignored if "subject_key_identifier" is "false".
# Format: EUI in hexadecimal notation (e.g. "4f5688d7ec933be2").
# Mandatory: no.
# If undefined the value calculated from the public key is used.
subject_key_identifier_value = 

# Specifies if the wrapped contingency key extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "wrapped_contingency_key_critical" and "wrapped_contingency_key_value" are ignored.
# Mandatory: yes.
wrapped_contingency_key = false

# Specifies if the wrapped contingency key extension will be marked as critical.
# Ignored if "wrapped_contingency_key" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "wrapped_contingency_key" is "true"; otherwise no.
wrapped_contingency_key_critical = 

# Path to the file containing the wrapped contingency key.
# Ignored if "wrapped_contingency_key" is "false".
# Format: file path, absolute or relative to the current directory. On Microsoft Windows, "\" should be specified as "/" or "\\".
# Mandatory: yes if "wrapped_contingency_key" is "true"; otherwise no.
wrapped_contingency_key_value =

# Specifies if the subject alternative name extension will be included in the certificate.
# Possible values: "true" and "false". If "false" "subject_alternative_name_critical", and "subject_alternative_name_value" are ignored.
# Mandatory: yes.
subject_alternative_name = false

# Specifies if the subject alternative name extension will be marked as critical.
# Ignored if "subject_alternative_name" is "false".
# Possible values: "true" and "false".
# Mandatory: yes if "subject_alternative_name" is "true"; no otherwise.
subject_alternative_name_critical =

# Hardware type in the subject alternative name extension.
# Ignored if "subject_alternative_name" is "false".
# Format: OID (e.g. "1.2.826.0.1.8641679.1.2.3.4").
# Mandatory: no, but if "subject_alternative_name" is "true" "subject_alternative_name_hw_type" and "subject_alternative_name_hw_serial" must be either both defined or both undefined.
# If "subject_alternative_name_hw_type" is undefined but "csr" is defined, the hardware type in the certification request is used.
# If both "subject_alternative_name_hw_type" and "csr" are undefined, the subject alternative name extension will not include the hardware type. 
subject_alternative_name_hw_type =

# Hardware serial number in the subject alternative name extension.
# Ignored if "subject_alternative_name" is "false".
# Format: EUI in hexadecimal notation (e.g. "00db1234567890a0").
# Mandatory: no, but if "subject_alternative_name" is "true" "subject_alternative_name_hw_type" and "subject_alternative_name_hw_serial" must be either both defined or both undefined.
# If "subject_alternative_name_hw_serial" is undefined but "csr" is defined, the hardware serial number in the certification request is used.
# If both "subject_alternative_name_hw_serial" and "csr" are undefined, the subject alternative name extension will not include the hardware serial number.
subject_alternative_name_hw_serial =

# Directory name in the subject alternative name extension.
# Ignored if "subject_alternative_name" is "false".
# Format: text (e.g. "DCA1-357").
# Mandatory: no.
# If "subject_alternative_name_directory_name" is undefined but "csr" is defined, the directory name in the certification request is used.
# If both "subject_alternative_name_directory_name" and "csr" are undefined, the subject alternative name extension will not include the directory name.
subject_alternative_name_directory_name =

# Other certificate extensions.
# Used to define extensions for which there is no specific support or extensions for which the specific support is not complete (e.g. the support for the subject alternative name extension is limited to the requirements specified in GBCS).
# Format: a comma-separated list of groups of 3 values where each group contains the following:
# - An OID identifying the extension;
# - Either "true" or "false" specifying if the extension will be marked as critical;
# - The extension contents in hexadecimal notation.
# Examples:
# - List with a single key usage: *********,true,0123456789abcdef
# - List with multiple extensions identifiers: *********,true,0123456789ABCDEF,*********,false,FEDCBA9876543210
# Mandatory: no.
other_extensions = 
