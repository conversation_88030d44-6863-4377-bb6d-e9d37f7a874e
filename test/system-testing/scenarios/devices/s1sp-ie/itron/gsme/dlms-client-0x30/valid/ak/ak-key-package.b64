<?xml version="1.0" encoding="UTF-8" standalone="no"?><DLMSKeyPackage xmlns:ns2="http://www.w3.org/2000/09/xmldsig#" creationDate="2018-09-24T17:18:07.565+01:00" id="d65db131-0929-44f7-9a5c-2ae5e6eeb64b" schemaVersion="1.0">
    <DeviceIds>
        <DeviceId>00-00-00-00-00-01-02-0A</DeviceId>
        <DeviceId>00-00-00-00-00-01-02-0B</DeviceId>
        <DeviceId>00-00-00-00-00-01-02-0C</DeviceId>
    </DeviceIds>
    <KeyType>AK</KeyType>
    <DLMSClient>0x30</DLMSClient>
    <Encryption>
        <Mechanism>AES</Mechanism>
        <EncryptionKeyId>1</EncryptionKeyId>
        <Key>CwpBSvHOQzHPZaeasa4Ckg==</Key>
    </Encryption>
    <Authentication>
        <Mechanism>ECC</Mechanism>
        <AuthenticationKeyId>3</AuthenticationKeyId>
    </Authentication>
<Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/><SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#ecdsa-sha256"/><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><DigestValue>X+0oYrfEw4lrpNobxYXHnT3TeVxB/r9rG49Oqc22Ofw=</DigestValue></Reference></SignedInfo><SignatureValue>KhT3Y3zTzV9jr5+O+BJZlmNyhgjfDvY4ws0qQlHGJ2azn4/Re29saeStIKWL1zNn7508GIOm+XcZ&#13;
eUWU+fHlsQ==</SignatureValue></Signature></DLMSKeyPackage>