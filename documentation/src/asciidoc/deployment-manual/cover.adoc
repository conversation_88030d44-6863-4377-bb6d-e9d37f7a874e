:pdf-theme: front-page-theme.yml

{nbsp} +

{nbsp} +

{nbsp} +

{nbsp} +

[.red-title]#Critical Software# +
[.black-title]#{document-type}# +
[.subtitle]#{project-name}# +

{nbsp} +

{nbsp} +

[.red]#CONTRACT REFERENCE: {contract-reference}# +

DATE: {document-date} +
PROJECT CODE: {project-code} +
DOC. REF.: {document-reference} +
STATUS: {document-status} +
// The placeholder needs to contain all the number glyphs (0-9) so that when it is replaced the glyphs are available in the PDF file.
PAGES: PAGES_PLACEHOLDER_0123456789 +
INFORMATION CLASSIFICATION: {information-classification} +
VERSION: {project-version}

{nbsp} +

[.red]#DISCLAIMER - {customer-uppercase} CONTRACT REPORT.#

The work described in this report was performed under {customer} contract. Responsibility for the contents resides in the author or organization that prepared it.

'''

[.red]#CUSTOMER:#

image::images/dcc-logo.png[align=left]


'''
