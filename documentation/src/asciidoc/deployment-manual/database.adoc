== Database

Before being able to use DCO, DCO Migration and SMKI Importer, the DCO database structure needs to be created. As detailed before, 2 different database servers can be used (DCO Database and DCO Migration Database). If this is the case, the pre-requirements below are applicable to both database servers.

=== Pre-Requirements

* MySQL Database configured and running. Make sure MySQL server is configured to use the UTC time zone by adding the line default-time-zone='+00:00' to the my.cnf configuration file (which can be found at /etc/mysql).
* Database schemas (e.g. named dccdco and dccdcomigration).
* A user and password with access to both schemas (typically, this could be the same user that DCO will use to connect to the database).
* If needed, increase the server's default max_allowed_packet value. The DCO current value is 25M.

=== Database Scripts

The table below presents the database scripts folder structure. Please note that this is an example of the type of scripts that may exist. Depending on the drop, not all scripts may be available.

[width="100%",cols="62%,38%",options="header-custom",]
|===
^|*Directory* ^|*Description*
|dco-<cohort>-release-X.X.X |Distribution root.
|database |
|dco |Scripts to setup the DCO database.
|V_1_17_0_Full_Create_DCO_DB.sql |Script to create the base structure.
|V_X_X_X_Test_Data.sql |Test data script. This script contains test data that may be required on an early phase, to execute "smoke tests" while the external systems are not available.
|update |
|V_1_16_6_Update_DCO_DB_to_1_17_0.sql |Update scripts. For each drop, there may exist 0 or more update scripts.
|      rollback |
|V_1_17_0_Rollback_DCO_DB_to_1_16_6.sql |Script that allows the rollback to the previous version, if needed.
|      cleanup |
|       V_1_16_6_Cleanup_DCO_DB_to_1_17_0.sql |Clean-up script. *This script must be executed sometime after the last deployment when all DCO components for all cohorts are on the same version (the version on the name of the script)*. While this script is not executed, if required, rollback to the previous version of the system can be performed.
|      full_create |
|        V_1_16_6_Full_Create_DCO_DB.sql |Scripts to create the base structure for the previous versions.
|      drop |
|        V_0_00_0_Drop_DCO_DB.sql |Script to delete all the tables in DB.
|dco-migration |Scripts to setup the DCO Migration database. It follows the structure above.
|... |
|===

=== Database Creation

To create the DCO database:

* Copy the database/V_1_17_0_Full_Create_DCO_DB.sql file from the DCO release to a folder, for example, /tmp/V_1_17_0_Full_Create_DCO_DB.sql.
* Go to the folder where the file was copied to and execute the following command to execute the table creation scrips:

[source,text]
----
mysql -u <user> -p<password> < V_1_17_0_Full_Create_DCO_DB.sql

# Example:

mysql -u dco -pdco < V_1_17_0_Full_Create_DCO_DB.sql
----

* Once the mysql command finishes, the DCO tables have been created in the schema dccdco.
[.sidebar]
====
* If using 2 database servers, please repeat the steps above, using the applicable scripts on the DCO Migration database. If using 1 database server, then the DCO Migration scripts must be executed on the same schema.
====

=== Database Update

For each release, it's only required to execute the update scripts. The database creation scripts are only required when performing a greenfield deployment.

=== Database Clean-Up

For each release may be available "clean-up" scripts, e.g. V_1_16_6_Cleanup_DCO_DB_to_1_17_0.sql. These scripts follow this nomenclature, having _Cleanup_ on the name. These scripts execute operations that cannot be rollbacked, e.g. drop tables or columns. *They must only be executed, from time to time (a few days, for example) after the last deployment, when we are sure that no rollback is required. Please note that the "last" deployment means that all DCO components, for all cohorts, are on the same version (the version on the name of the script or above).*

Whenever possible, the update scripts only execute operations that leave the database in a state that allows rollback to the previous version of DCO. The "clean-up" scripts then remove the data that is not required.

=== Database Rollback

For each release are available "rollback" scripts, e.g. V_1_17_0_Rollback_DCO_DB_to_1_16_6.sql to allow rolling back the DB to the previous version (in the sample, from version 1.17.0 to 1.16.6).

These scripts follow this nomenclature, having _Rollback_ on the name.

=== Database Connection Properties (Thorntail)

All thorntail components that connect to the database have the following properties. <DS_NAME> can be dco-ds or dco-migration-ds.

[width="100%",cols="50%,50%",options="header-custom",cols=".^,"]
|===
^|*Property* ^|*Description*
|thorntail.datasources.data-sources.<DS_NAME>.background-validation a|
An element to specify that connections should be validated on a background thread versus being validated prior to use. In the case of a database connection failure it can take up to the value defined in <background-validation-millis> to try to recover the connections in the pool. Changing this value can be done only on disabled datasource, requires a server restart otherwise.

To detect a database connection failure immediately (e.g. on a multi-node cluster scenario, if a node fails) and to create a new connection, it's recommended to set this property to false.

|thorntail.datasources.data-sources.<DS_NAME>.background-validation-millis |The background-validation-millis element specifies the amount of time, in milliseconds, that background validation will run. Changing this value can be done only on disabled datasource, requires a server restart otherwise. Only applicable if <thorntail.data-sources.<DS_NAME>.background-validation> is set to true.
|thorntail.datasources.data-sources.<DS_NAME>.check-valid-connection-sql |Specify an SQL statement to check validity of a pool connection. This may be called when managed connection is obtained from the pool.
|thorntail.datasources.data-sources.<DS_NAME>.connection-url a|
JDBC connection URL. Several connection types can be used. See some examples below:

* *When connecting directly to a MySQL database instance (including NDB cluster or one InnoDB Router):* jdbc:mysql://<database_host>:3306/<database_schema>?useSSL=false&allowPublicKeyRetrieval=true&max_allowed_packet=25M
* *When connecting to multiple MySQL InnoDB Routers:* **********************://<database_router1_host>:6446,<database_router2_host>:6446/<database_schema>?useSSL=false&allowPublicKeyRetrieval=true&max_allowed_packet=25M

*Notes:*

* <database_schema> can be dccdco or dccdcomigration.
* <database_host>, <database_router1_host> and <database_router2_host> are the corresponding URLs.
* The port numbers are the default ones and may need to be updated.

|thorntail.datasources.data-sources.<DS_NAME>.driver-name |Defines the JDBC driver the datasource should use. It is a symbolic name matching the name of installed driver. In case the driver is deployed as jar, the name is the name of deployment unit. Do not change this value.
|thorntail.datasources.data-sources.<DS_NAME>.password |Database password.
|thorntail.datasources.data-sources.<DS_NAME>.use-ccm |Possibility to use the CachedConnectionManager. Do not change this value.
|thorntail.datasources.data-sources.<DS_NAME>.user-name |Database username.
|thorntail.datasources.data-sources.<DS_NAME>.min-pool-size |The min-pool-size element specifies the minimum number of connections for a pool. The default value is 0.
|thorntail.datasources.data-sources.<DS_NAME>.max-pool-size |The max-pool-size element specifies the maximum number of connections for a pool. No more connections will be created in each sub-pool. The default value is 20. This value may need to be updated, depending on the system expected load.
|thorntail.datasources.data-sources.<DS_NAME>.initial-pool-size |The initial-pool-size element indicates the initial number of connections a pool should hold. The default value is 0.
|thorntail.datasources.jdbc-drivers.com.mysql.driver-class-name |The fully qualified class name of the java.sql.Driver implementation. Do not change this value.
|thorntail.datasources.jdbc-drivers.com.mysql.driver-module-name |The name of the module from which the driver was loaded, if it was loaded from the module path. Do not change this value.
|thorntail.datasources.jdbc-drivers.com.mysql.xa-datasource-class-name |The fully qualified class name of the javax.sql.XADataSource implementation. Do not change this value.
|===

=== Database Connection Properties (Quarkus)

All quarkus components that connect to the database have the following properties.

[width="100%",cols="50%,50%",options="header-custom",cols=".^,"]
|===
^|*Property* ^|*Description*
|quarkus.datasource.username |Username credential to access the DCO database.
|quarkus.datasource.password |Password credential to access the DCO database.
|quarkus.datasource.jdbc.background-validation-interval |The interval at which idle connections are validated in the background.
|quarkus.datasource.jdbc.validation-query-sql |Query executed to validate the database connection.
|quarkus.datasource.jdbc.url |URL to the DCO database.
|quarkus.datasource.dco-migration-ds.username |Username credential to access the DCO migration database.
|quarkus.datasource.dco-migration-ds.password |Password credential to access the DCO migration database.
|quarkus.datasource.dco-migration-ds.jdbc.url |URL to the DCO migration database.
|quarkus.datasource.dco-migration-ds.jdbc.validation-query-sql |Query executed to validate the migration database connection.
|quarkus.datasource.dco-migration-ds.jdbc.background-validation-interval |The interval at which idle migration connections are validated in the background
|===