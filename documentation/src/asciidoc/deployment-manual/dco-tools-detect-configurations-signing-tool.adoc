== DCO Tools - Detect Configurations Signing Tool

This tool can be used to sign and verify detect configuration XML files.

=== Prerequisites

[arabic]
. Java 17 installed.
. Have the same Key Pair used by DCO to validate the key packages.

=== Distribution

This tool is distributed on a zip file, with the following nomenclature dco-detect-conf-signing-tool-<Version>.jar. This file contains an executable jar and a config folder with all the required configuration files and keystores.

=== Setting up the Keystores

The tool uses a keystore to sign detect configurations. The keystore is stored inside the dist folder, in config/keystores.

To add new keys:

[arabic]
. The keys must be inside a p12 keystore (so a new one needs to be created or added to an existing one);
. Move the keystore to the keystores folder (referenced above). The application.properties file has the path for the keystore to be used. It can be changed to any other place where the keystore with the key to sign the configurations is.

*Changing Keys*

It's possible that the keys (in this case for signing the detect configurations) in DCO change. If so, it's essential to change them in this tool as well, otherwise it will be signing the detect configurations with keys unknown to DCO.

To do this there are various options:

[arabic]
. Copy the keystore
[loweralpha]
.. Go to DCO keystore folder (considering a DCO deployment, check the directory config/keystores under each command-manager cohort);
.. Look for the keystore that has the detect configurations signing key (the default keystore is detect-configuration-keystore.p12);
.. Copy it to the keystore folder for this tool (by default $DCO_SIGNING_TOOL_HOME/config/keystores).
. Extract the key pair using the KeyStoreExplorer
[loweralpha]
.. Open the keystore using the KeyStoreExplorer;
.. Right-click on the desired key (the default name is 'detect-config'),
.. Select 'Export' → 'Export Key Pair';
.. You'll be prompted to input the key password;
.. After that, you'll have to choose the password for the new keystore (this will also be the password of the extracted key);
.. In the same box you can choose the path and the name of the new keystore, create it in the keystore folder for this tool.
. Change the 'application.properties' file accordingly:
[loweralpha]
.. detect.keystore.configuration.path=<path to the new keystore | default = config/keystores/keystore-DetectConfiguration.p12>
.. detect.keystore.configuration.password=<password for the key/keystore | default = 123456>

=== Usage

[arabic]
. Unzip the tool;
. cd into _dist/;_
. Execute the command:
----
java -jar dco-detect-conf-signing-tool-<Version>.jar --help
----

*Example Usage*
[source,text]
----
java -jar dco-detect-conf-signing-tool-<Version>.jar --help
Usage: dco-detect-conf-signing-tool [-hV] [--replace-on-path]
                                    [--detect-config-certificate-alias=<detectCo
                                    nfigCertificateAlias>]
                                    [--detect-config-certificate-password=<detec
                                    tConfigCertificatePassword>]
                                    [--keystore-password=<keystorePassword>]
                                    [--keystore-path=<keystorePath>] [COMMAND]
       --detect-config-certificate-alias=<detectConfigCertificateAlias>
       --detect-config-certificate-password=<detectConfigCertificatePassword>
-h, --help Show this help message and exit.
       --keystore-password=<keystorePassword>
       --keystore-path=<keystorePath>
       --replace-on-path
-V, --version Print version information and exit.
Commands:
    sign-detect-configuration-folder            Sign a DetectConfiguration XML
                                                folder
    sign-detect-configuration-xml               Sign a DetectConfiguration XML
                                                document
    verify-detect-configuration-xml-signature   Verify a DetectConfiguration XML
                                                signature.
----
Checking specific command help:
----
java -jar dco-detect-conf-signing-tool-<Version>.jar verify-detect-configuration-xml-signature --help

Usage: dco-detect-conf-signing-tool verify-detect-configuration-xml-signature
       [-hV] <arg0>
Verify a DetectConfiguration XML signature.
      <arg0>
  -h, --help      Show this help message and exit.
  -V, --version   Print version information and exit.

----
*Detect Configuration Signing*

To sign a detect configuration XML file, type the command:
----
java -jar dco-detect-conf-signing-tool-<Version>.jar sign-detect-configuration-xml <detect-configuration-file.xml>
----
Output example:
----
signed/<detect-configuration-file.xml>
----
*Detect Configuration Folder Signing*

To sign a detect configuration XML file, type the command:
----
java -jar dco-detect-conf-signing-tool-<Version>.jar sign-detect-configuration-folder <folder-path>
----
Output example:

----
signed/<detect-configuration-file.xml>(...)
----

*Verify Detect Configuration XML File Signature*

To verify a detect configuration XML file signature, type the command:
----
java -jar dco-detect-conf-signing-tool-<Version>.jar verify-detect-configuration-xml-signature <signed-detect-configuration-file.xml>
----
Output example:
----
[main] INFO com.criticalsoftware.dco.dcodetectconfsigningtool.commands.DetectConfigurationCommands - operation=verifyDetectConfigurationXmlSignature

[main] INFO com.criticalsoftware.dco.dcodetectconfsigningtool.commands.DetectConfigurationCommands - operation=verifyDetectConfigurationXmlSignature, isValidXmlSignature=true

[main] INFO com.criticalsoftware.dco.dcodetectconfsigningtool.commands.DetectConfigurationCommands - operation=verifyDetectConfigurationXmlSignature, message='DONE'
----