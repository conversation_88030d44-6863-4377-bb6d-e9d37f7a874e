== DCO Crypto - Secure Managing Keys

To calculate the authentication CMAC for the SUA, an ECDH key pair is needed. From this key pair, two keystores need to be generated, one that contains the key pair, that will be provided to DCO Crypto and another one with only the public key that will be provided to DCO Main. This key pair can be generated using the tool dco-secure-ecdh-keypair provided within DCO tools.

On the next sections are detailed the required steps to manage the key material on the HSM. Two different scenarios are being considered:

* *Production deployment*, where the key pair is generated on the DCO HSM.
* *Testing deployment*, where the key pair is generated locally on the filesystem, using DCO Crypto Soft mode.

=== Production Deployment

To generate a key pair with the HSM, the tool must be executed from an Admin HSM client (please refer to the nShield Connect documentation from Entrust for further detail). The tool will create a key pair in the Security World and export the public key part of the key pair to a keystore. The key pair in the HSM will be identified by an id and an appname. The provided tool sets the appname to the default name "simple". The key id is given by the user when generating the key pair.

In DCO Crypto, the properties related to the key pair are the following:

----
dco:
  hsm-crypto:
    key:
      app-name: simple
----

The key alias will be provided during the CMAC request.

After generating the key pair, the tool will store the public key part in a keystore. This keystore is stored in the path provided as a parameter, when generating the key pair. You can then copy this keystore from the hardserver to the local filesystem or container, where the DCO Main is running.

In DCO Main, the properties related to this keystore are the following:

----
secure:
  keystore:
  path: keystores/dco-secure.pk12
  type: PKCS12
  password: changeit
  key:
    alias: changeit
----

To run the tool:

----
java -jar dco-secure-ecdh-generator-[version].jar hsm [key-id] [public-key-keystore-path]
----

=== Testing Deployment

To generate an EC key pair for the testing environment, using soft crypto, you can generate it using JCE tools or the provided tool. The EC pair must be generated with the elliptic curve NISTP256. After generating the key pair, you will need two keystores, one that contains the key pair and that will be defined in the properties of DCO Crypto and another containing only the public key, that will be used by DCO Main. The provided tool already creates these keystores and stores them in different paths, according to the provided parameters.

In DCO Crypto, the properties related to the keystore are the following:

----
dco:
  soft-crypto:
    keystore:
      path: keystores/dco-secure.pk12
      type: PKCS12
      password: changeit
      key:
        key-password: changeit
----

If the provided tool is not used to generate the key pair, assure that the second keystore only contains the public part of the key pair. In DCO Main, the properties related to the keystore are the following:

----
secure:
  keystore:
    path: keystores/dco-secure.pk12
    type: PKCS12
    password: changeit
    key:
      alias: changeit
    decryption-key:
      alias: initialdk
      password: changeit
----

To run the tool:

----
java -jar dco-secure-ecdh-generator-[version].jar soft [key-id] [public-key-keystore-path] [private-key-keystore-path]
----

=== Switching between modes (HSM and Soft)

In test environment, when using DCO Crypto, we may not have HSMs available. To switch to soft crypto mode, set the following property to false in the DCO Crypto config file.

----
dco:
  crypto:
    use-hsm: false
----