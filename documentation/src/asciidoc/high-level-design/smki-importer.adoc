== SMKI Importer

=== Overview

The SMKI Importer is responsible for reading files with *lists of certificates and revoked certificates* from an SFTP server and for processing them, *updating their respective information in a database*. The processing of the mentioned information must only be done if all the necessary files for the daily processing are available in the SFTP server.

image::images/image30.png[image,width=468,height=361,align=center]

The system should run once a day, although this must be configurable. The component will use Quarkus Scheduler, the configurations that are defined set the frequency of execution of the SMKI Importer. If running multiple times, a day, the certificates won’t be reimported, as the file content is assumed to be the same. ARLs/CRLs will be re-imported every time the application is run. +
SMKI Importer can also be run only once.

=== SFTP Server

The SMKI Importer will connect to the SFTP server using an authentication based on username/password, with verification of the public key of the server.  After connecting to the server, the system must validate if the server contains all the required files, to continue with the daily processing and only then start processing.

=== Files

The SMKI Repository SFTP must contain the following files:

[cols=".^,.^,.^,.^",options="header-custom",]
|===
^|*Type* ^|*File Format* ^|*Description* ^|*Number of Files Required*
|FULL |SMKIKR_FULL_YYYY-MM-DD.xml.gz (configurable) |A file in .gz format containing a xml with all certificates with a status of ‘In-Use’. Before processing this file, a full clean-up of the database is necessary. This file is updated daily and should have the current day date. |1
|DELTA |SMKIKR_DELTA_YYYY-MM-DD.xml.gz (configurable) a|
A file in .gz format containing a xml with certificates issued and lodged in the SMKI repository during the previous 24 hours as well as certificates that have changed status.

There is one file for each day of the previous week, with the most recent file matching the current day date.

|There isn't a minimum of files required by default, but this value is configured. Processing will be done in sequence until there is a day with a missing file.
|ARL |CRL_SA.crl.gz (configurable) X509 revocation list, Base64 DER format. a|
A file in .gz format containing a file with extension 'arl' or 'crl' that is the latest Organisational ARL (Authority Revocation List).

|1*
|CRL |CRL_S1.crl.gz (configurable) X509 revocation list, Base64 DER format. a|
A file in .gz format containing a file with extension 'crl' that is the latest Organisational CRL (Certificate Revocation List).

|1*
|===

All the .gz files are in a compressed format, so they must be extracted. The DCC shall ensure that SFTP files holding Certificates will be made available in .gz format, with all versions of .gz being supported. In case any file is missing, or the total number of files don't match the expected number, the processing for the day must be terminated after logging an error.

*At the start of the SMKI Importer execution, only the total number of revocation list files present in the SFTP server (ARL or CRL) is verified. Later on, when validating the revocation lists, SMKI Importer will notice there's not at least one of each (ARL and CRL), and will log an error, but won't stop running or throw an exception.

The Full file will contain millions of certificates, but only a small subset (Device Certificates are ignored) will be processed by the SMKI Importer. Also, the file will have a big size (~60GB uncompressed).

=== Files Schema

Full and Delta

Although both full and delta files contain a complex schema, for the SMKI Importer it's only relevant to validate the information contained on the XML element CertificateResponse represented below.

[source,html]
----
<xs:complexType name="CertificateResponse">
   <xs:sequence>
     <xs:element name="CertificateSubjectAltName" type="CertificateSubjectAltName" minOccurs="0" maxOccurs="1"/>
     <xs:element name="CertificateSubjectName" type="CertificateSubjectName" minOccurs="0" maxOccurs="1"/>
     <xs:element name="CertificateSerial" type="CertificateSerial" minOccurs="1" maxOccurs="1"/>
     <xs:element name="CertificateStatus" type="CertificateStatus" minOccurs="1" maxOccurs="1"/>
     <xs:element name="CertificateBody" type="xs:base64Binary" minOccurs="0" maxOccurs="1"/>
     <xs:element name="CertificateRole" type="xs:integer" minOccurs="0" maxOccurs="1"/>
     <xs:element name="CertificateUsage" type="CertificateUsage" minOccurs="1" maxOccurs="1"/>
     <xs:element name="ManufacturingFlag" type="xs:boolean" minOccurs="1" maxOccurs="1"/>
  </xs:sequence>
 </xs:complexType>
----

Below, the elements required to update the database.

[cols=".^,.^,.^",options="header-custom",]
|===
^|*Element* ^|*Description* ^|*SMKI Importer Usage*
a|
CertificateSubjectAltName

a|
64Bit EUI relative to a device.

a|
If this field is present the CertificateResponse entry should be ignored, as this identifies a Device Certificate. This will be defined as a configurable property.

a|
CertificateSerial

a|
Certificate unique identifier. Up to 50 char String.

a|
The SMKI Importer should ignore this field and extract it from the CertificateBody.

a|
CertificateStatus

a|
Current status of the certificate. 1 char String.

a|
Value used to update the database. Any certificate with any other status that "I" (In-Use) should be removed from the database.

a|
CertificateBody

a|
Body in Base64 DER format.

a|
Certificate content. All the metadata for the database should be extracted from this element.

|===

ARL and CRL

DER encoded files are binary, thus it will be required to create an internal object schema to represent these elements. Below is an example of a CRL file content. Consider that an ARL is structurally the same as a CRL file.

[source,text]
----
Certificate Revocation List (CRL):
        Version 2 (0x1)
    Signature Algorithm: ecdsa-with-SHA1
        Issuer: /OU=07/CN=DCO_CA
        Last Update: Aug 23 12:53:50 2018 GMT
        Next Update: Sep 22 12:53:50 2018 GMT
        CRL extensions:
            X509v3 Authority Key Identifier:
                keyid:41:5D:34:77:39:33:92:DA

            X509v3 CRL Number:
                1
Revoked Certificates:
    Serial Number: 27F08D12E6C3F6F7BFED4B3146484A31
        Revocation Date: Aug 23 12:53:40 2018 GMT
    Serial Number: 2C939F434BB0C4CA0CA6AF487DA1FE39
        Revocation Date: Aug 23 12:53:38 2018 GMT
    Serial Number: 3582CE0C07496F71CC18DCE45E498520
        Revocation Date: Aug 23 12:53:47 2018 GMT
    Serial Number: 35E03AADD1410ACD575071488377AECA
        Revocation Date: Aug 23 12:53:42 2018 GMT
    Signature Algorithm: ecdsa-with-SHA1
         30:45:02:20:47:c3:12:06:32:b3:2f:4d:3e:94:66:f8:32:a5:
         2a:a3:49:7f:84:d8:f5:5d:6d:54:ad:d0:21:95:94:8d:57:ae:
         02:21:00:d9:ce:ad:bf:ea:6e:38:f8:0f:f8:ec:f7:a4:ea:9b:,
----

=== Execution Phases

When starting the component, before starting execution, it needs to validate which phase of execution must be processed first. There is a total of five execution phases that must be executed in the order below.

[arabic]
. {blank}
+
*Validation Phase*
[arabic]
.. {blank}
+
Before processing any file, the SMKI Importer needs to verify if all the required files are available: 1 Full file within the time period (if there is no update record for the configurable last X days) and at least one 1 ARL file and at least 1 CRL file (considering we can't differentiate between ARL and CRL files during this phase, we only validate that there are two files matching the given name pattern for revocation lists).
. {blank}
+
*FULL Import Phase*
[arabic]
.. To execute this phase one of the following criteria must be present (If none of the criteria is present, execution starts from phase 3):
[arabic]
... No Full file has been processed before.
... The last Full or Delta file processed was at more than X days ago (configurable property).
.. Transfer the .gz file to a local folder.
.. Extract the xml from the .gz.
.. If device certificates are to be ignored (configurable by a property), then execute next steps, otherwise an exception is thrown.
.. Without loading into memory, read all valid entries for each certificate. We extract all the required metadata from the certificate body instead of the XML files.
.. In case we need to process a Full file, clean all the certificates from the database.
[arabic]
... *NOTE:* Cleaning the DB should be a transactional action only executed if all this execution phase is successful.
.. Add certificates to the smki_certificates table.
.. Update the smki_file_history table with information about the processing of the file.
.. Delete the files from the local folder.
.. Process any Delta files after Full file date, in sequence, until the current day. Delta files will stop to be processed if after Full file date there is a missing file for the day.
. {blank}
+
*DELTA Import Phase*
[arabic]
.. To execute this phase, we need to validate the last time a Full or Delta file was processed and process all delta files incrementally. The last Full or Delta file that was processed must have X (configurable property) or less days.
[arabic]
... {blank}
+
Delta files are processed in sequence until there is a missing file for the day.
.. If device certificates are to be ignored (configurable by a property), then execute next steps, otherwise an exception is thrown.
.. Transfer the .gz file(s) to a local folder. Transfer only the file(s) between the next day since the last processed Delta file, and the current day.
.. Extract the xml from the .gz file.
.. Without loading into memory, read all valid entries for each certificate.
.. Update the required entries:
[arabic]
... If the certificate is not present in the database and the status is 'In-Use' add it to the database;
... If the certificate is not present in the database and as an invalid status (any status different than "In-Use"), discard that entry;
... If the certificate is present in the database and the status changed from 'In-Use' remove that entry from the database;
... If the certificate is present in the database but has kept the status 'In-Use', discard that entry.
[arabic]
.. Update the smki_file_history with information about the processing of the DELTA.
.. Delete the files from the local folder.

. {blank}
+
*Revocation List Import Phase*
[arabic]
.. *NOTE*: It's possible to find in the SFTP more than 1 valid ARL file and more than 1 CRL file. Considering that the file name pattern defined in the properties only in this phase it's possible to validate which files are ARLs and which files are CRLs.
.. Transfer the .gz file(s) to a local folder. 
.. Extract the .arl / .crl file(s) to the local folder.
.. Look for a parent certificate.
[arabic]
... Look for a valid CA certificate for the (A/C)RL file in the database.
[arabic]
.... If one is found: validate the CRL signature.
.... If valid add it to the CRL list of files.
... If there was not a valid CA in the database, look for a matching root certificate in the keystore.
[arabic]
.... If one is found: validate the ARL signature.
.... If valid add it to the ARL list of files.
[arabic]
.. Delete the files from the local folder.
.. If there is a valid list of ARL files:
[arabic]
... Update the Revocation List in the database.
... Update the smki_file_history with information about the processing of the ARL.
.. In case no valid ARL file was found, log the error.
.. If there is a valid list of CRL files:
[arabic]
... Update the Revocation List in the database.
... Update the smki_file_history with information about the processing of the CRL.
.. In case no valid CRL file was found, log the error.

=== Logging

Below are detailed the expected log messages that the system must produce for each phase.

*Validation Phase (History Manager)*

[cols="1,5",options="header-custom",]
|===
|*Level* |
a|
DEBUG|Files present in SMKI Repository SFTP Server: <List of files>
|INFO |Number of files present in SMKI Repository SFTP Server: <n>. Files with extension <extension> will be ignored.
|INFO |The SFTP server contains <n> FULL file(s), <n> DELTA file(s), <n> Revocation List File(s).
|DEBUG |Reading list of files in SMKI Repository SFTP Server. Attempt: <listCount>.
|===

If no Full/Delta processed in the last X days:

[cols="1,5",options="header-custom",]
|===
|*Level* |
|INFO |SMKI Database is not synchronized and hasn't been updated in the past X days.
|===

If a Full/Delta has been processed in the last X days:

[cols="1,5",options="header-custom",]
|===
|*Level* |
|INFO |SMKI Database was last updated with information from <date>. A DELTA import is required.
|===

*FULL Execution Phase (FULL Importer)*

[cols="1,5",options="header-custom",]
|===
|*Level* |
a|
DEBUG |Starting FULL Import Execution. FULL file <gzname>
| DEBUG |Starting to transfer file <file_name>. Attempt=<attempt>.
|INFO |Downloaded file <file_name> from SFTP folder <sftp_folder> to path <local_path>
|INFO |Extracted file from <gz_path> as <file_path>
|INFO |Finished reading [ <file_name> ]. <n> certificates will be added to the database.
|DEBUG |Successfully cleaned all certificates from SMKI Database
|DEBUG |Added <n> certificates to the database.
|INFO a|
Added entry to SMKI file history for FULL: [ File Name: <file_name>, File Date <file_date>
|INFO |Cleaned all files from temporary local folder <local_folder> after executing FULL import
| INFO |Finished importing FULL file <file_name>
|DEBUG |Finished FULL import execution.
|===

*DELTA Execution Phase (DELTA Importer)*

[cols="1,4,1",options="header-custom",]
|===
|*Level* | |
a| DEBUG a| Starting DELTA Import Execution. DELTA files in SMKIR SFTP server <list_of_deltas>|
a| DEBUG a| Starting to transfer file <file_name>. Attempt=<attempt>. |For each file
| INFO a|Downloaded file <file_name> from SFTP folder <sftp_folder> to path <local_path> a|For each file
| INFO a|Extracted file from <gz_path> as <file_path> a|For each file
| INFO |DELTA file(s) for the last <n> day(s) need to be executed |
| DEBUG |DELTA file(s) <list_of_deltas> are going to be executed |
| INFO a|Finished reading [ <file_name> ]. <n> certificates listed in file to be updated. a|For each file
| INFO a|<n> new certificates will be added to the database. a|For each file
| DEBUG a|The following certificates < [serial: ... issuer...], [serial: ... issuer...], ...]> will be added to the database. a|For each file
| INFO a|<n> certificates will be removed from the database. a|For each file
| DEBUG a|The following certificates <[serial: ... issuer...], [serial: ... issuer...], ...]> will be removed from the database. a|For each file
| INFO a|Finished importing DELTA file <file_name> a|For each file
| INFO a|Cleaned all files from temporary local folder <local_folder> after executing DELTA import a| For each file
| DEBUG |Finished DELTA import execution.|
| INFO | Added entry to SMKI file history for DELTA: [ File Name: <file_name>, File Date <file_date> | For each file

|===

*Revocation List Execution Phase (Revocation List Importer)*

[cols="1,4,1",options="header-custom",]
|===
|*Level* | |
| INFO | Starting Revocation List Import Execution. There are <number_files> RL files in SFTP. |
a| DEBUG a| Starting to transfer file <file_name>. Attempt=<attempt>. | For each file
| INFO | Downloaded file <file_name> from SFTP folder <sftp_folder> to path <local_path> | For each file
| INFO | Extracted file from <gz_path> as <file_path>. | For each file
| DEBUG | Testing Revocation List for issuer <issuer_name> Authority Key Identifier <authority_key_identifier> as a CRL. Retrieving matching Certificate Authority entry from database. | For each file
| DEBUG | Revocation List for issuer <issuer> Authority Key Identifier <authority_key_identifier> is not a CRL (has no valid Parent CA in Database). | For each file
| DEBUG | Testing Revocation List for issuer <issuer_name> Authority Key Identifier <authority_key_identifier> as an ARL. Retrieving matching certificate from list of roots. | For each file
| DEBUG | Revocation List for issuer <issuer> Authority Key Identifier <authority_key_identifier> is not an ARL (has no valid Parent Root in Keystore). | For each file
| INFO a| Cleaned all files from temporary local folder <local_folder> after executing REVOCATION_LISTS import.|

|===

When a CRL has a valid CA parent in Database but CRL signature validation fails

[cols="1,5",options="header-custom",]
|===
a|*Level*|
a| ERROR a| Error SMKIE108 - Failed validation of CRL signature, despite metadata matching CA Certificate found in DB

|===

When an ARL has a valid parent root in keystore but ARL signature validation fails

[cols="1,5",options="header-custom",]
|===
a| *Level* |
a| ERROR a| Error SMKIE106 - Failed validation of ARL signature, despite metadata Root Certificate present in Keystore

|===

Finishing execution

[cols="1,5",options="header-custom",]
|===
a| *Level* |
a| INFO a| CRL file(s) <list_of_files> were validated with success and will be added to the database.
|INFO a| ARL file(s) <list_of_files> were validated with success and will be added to the database.
|DEBUG |Finished Revocation List import execution.
|===