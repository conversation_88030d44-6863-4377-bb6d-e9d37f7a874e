<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.criticalsoftware.dco</groupId>
  <artifactId>dco-documentation</artifactId>
  <version>1.0</version>
  <name>DCO Documentation</name>

  <properties>
    <maven.compiler.source>17</maven.compiler.source>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.build.timestamp.format>yyyy-MM-dd</maven.build.timestamp.format>
    <document-date>2025-01-29</document-date>
    <dep-manual-document-reference>CSWT-DCCDCOUK-2019-MAN-00031</dep-manual-document-reference>
    <hld-document-reference>CSWT-DCCDCO-2019-SPC-00259</hld-document-reference>
    <hld-data-model-document-reference>CSWT-DCCDCOUK-2019-DBS-00025</hld-data-model-document-reference>
    <dep-manual-version>19.0</dep-manual-version>
    <hld-version>17.0</hld-version>
    <hld-data-model-version>11.0</hld-data-model-version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>org.apache.pdfbox</groupId>
      <artifactId>pdfbox</artifactId>
      <version>2.0.26</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.asciidoctor</groupId>
        <artifactId>asciidoctor-maven-plugin</artifactId>
        <version>2.2.2</version>
        <dependencies>
          <dependency>
            <groupId>org.asciidoctor</groupId>
            <artifactId>asciidoctorj-pdf</artifactId>
            <version>1.6.0</version>
          </dependency>
          <dependency>
            <groupId>org.asciidoctor</groupId>
            <artifactId>asciidoctorj</artifactId>
            <version>2.5.2</version>
          </dependency>
        </dependencies>
        <configuration>
          <backend>pdf</backend>
          <doctype>book</doctype>
          <attributes>
            <sourcedir>${project.build.sourceDirectory}</sourcedir>
            <pdf-fontsdir>${project.basedir}/src/main/resources/themes/fonts;GEM_FONTS_DIR</pdf-fontsdir>
            <data-uri/>
            <allow-uri-read/>
            <project-code>DCCS1ES24</project-code>
            <project-name>SMETS1 ENDURING SUPPORT</project-name>
            <information-classification>CONFIDENTIAL</information-classification>
            <document-status>APPROVED</document-status>
            <customer-uppercase>DCC</customer-uppercase>
            <customer>DCC</customer>
            <document-date>${document-date}</document-date>
            <copyright-year>2024</copyright-year>
            <contract-reference>SMETS1 ENDURING SUPPORT AGREEMENT</contract-reference>
            <pdf-stylesdir>${project.basedir}/src/main/resources/themes</pdf-stylesdir>
            <idseparator>-</idseparator>
            <pdf-style>custom</pdf-style>
            <pagenums/>
            <toc>macro</toc>
            <toclevels>5</toclevels>
            <sectnumlevels>5</sectnumlevels>
            <idprefix/>
            <source-highlighter>coderay</source-highlighter>
            <sectnums/>
            <chapter-label/>
          </attributes>
          <requires>
            <require>${project.basedir}/src/main/resources/asciidoc-extensions/asciidoctor-lists.rb</require>
          </requires>
        </configuration>
        <executions>
          <!-- Generate Deployment manual documentation -->
          <execution>
            <id>generate-pdf-doc-cover-deployment-manual</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/asciidoc/deployment-manual/</sourceDirectory>
              <sourceDocumentName>cover.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/deployment-manual</outputDirectory>
              <attributes>
                <project-version>${dep-manual-version}</project-version>
                <document-type>DCO DEPLOYMENT MANUAL</document-type>
                <document-reference>${dep-manual-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>
          <execution>
            <id>generate-pdf-doc-content-deployment-manual</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>src/asciidoc/deployment-manual/</sourceDirectory>
              <sourceDocumentName>deployment-manual.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/deployment-manual</outputDirectory>
              <attributes>
                <project-version>${dep-manual-version}</project-version>
                <document-type>DCO DEPLOYMENT MANUAL</document-type>
                <document-reference>${dep-manual-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>

          <!-- Generate High level design documentation -->
          <execution>
            <id>generate-pdf-doc-cover-high-level-design</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/asciidoc/high-level-design</sourceDirectory>
              <sourceDocumentName>cover.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/high-level-design</outputDirectory>
              <attributes>
                <project-version>${hld-version}</project-version>
                <document-type>DCO HIGH LEVEL DESIGN</document-type>
                <document-reference>${hld-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>
          <execution>
            <id>generate-pdf-doc-content-high-level-design</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/asciidoc/high-level-design</sourceDirectory>
              <sourceDocumentName>high-level-design.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/high-level-design</outputDirectory>
              <attributes>
                <project-version>${hld-version}</project-version>
                <document-type>DCO HIGH LEVEL DESIGN</document-type>
                <document-reference>${hld-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>

          <!-- Generate High level design data model documentation -->
          <execution>
            <id>generate-pdf-doc-cover-high-level-design-data-model</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/asciidoc/high-level-design-data-model</sourceDirectory>
              <sourceDocumentName>cover.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/high-level-design-data-model</outputDirectory>
              <attributes>
                <sourcedir>${project.build.sourceDirectory}</sourcedir>
                <project-version>${hld-data-model-version}</project-version>
                <document-type>DCO DATABASE MODEL</document-type>
                <document-reference>${hld-data-model-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>
          <execution>
            <id>generate-pdf-doc-content-high-level-design-data-model</id>
            <phase>generate-resources</phase>
            <goals>
              <goal>process-asciidoc</goal>
            </goals>
            <configuration>
              <sourceDirectory>${project.basedir}/src/asciidoc/high-level-design-data-model</sourceDirectory>
              <sourceDocumentName>high-level-design-data-model.adoc</sourceDocumentName>
              <outputDirectory>${project.build.directory}/generated-docs/high-level-design-data-model</outputDirectory>
              <attributes>
                <sourcedir>${project.build.sourceDirectory}</sourcedir>
                <project-version>${hld-data-model-version}</project-version>
                <document-type>DCO DATABASE MODEL</document-type>
                <document-reference>${hld-data-model-document-reference}</document-reference>
              </attributes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>3.5.0</version>
        <executions>
          <execution>
            <!-- Generate Deployment manual documentation -->
            <id>merge-pdf-deployment-manual</id>
            <phase>process-classes</phase>
            <goals>
              <goal>java</goal>
            </goals>
            <configuration>
              <mainClass>com.criticalsoftware.dco.documentation.pdfmergetool.PDFMergeTool</mainClass>
              <arguments>
                <argument>${project.build.directory}/generated-docs/deployment-manual</argument>
                <argument>deployment-manual.pdf</argument>
                <argument>${project.basedir}/src/main/resources</argument>
                <argument>${dep-manual-document-reference}-dco-deployment-manual-v${dep-manual-version}.pdf</argument>
              </arguments>
            </configuration>
          </execution>

          <!-- Generate High level design documentation -->
          <execution>
            <id>merge-pdf-high-level-design</id>
            <phase>process-classes</phase>
            <goals>
              <goal>java</goal>
            </goals>
            <configuration>
              <mainClass>com.criticalsoftware.dco.documentation.pdfmergetool.PDFMergeTool</mainClass>
              <arguments>
                <argument>${project.build.directory}/generated-docs/high-level-design</argument>
                <argument>high-level-design.pdf</argument>
                <argument>${project.basedir}/src/main/resources</argument>
                <argument>${hld-document-reference}-dco-high-level-design-v${hld-version}.pdf</argument>
              </arguments>
            </configuration>
          </execution>

          <!-- Generate High level design data model documentation -->
          <execution>
            <id>merge-pdf-high-level-design-data-model</id>
            <phase>process-classes</phase>
            <goals>
              <goal>java</goal>
            </goals>
            <configuration>
              <mainClass>com.criticalsoftware.dco.documentation.pdfmergetool.PDFMergeTool</mainClass>
              <arguments>
                <argument>${project.build.directory}/generated-docs/high-level-design-data-model</argument>
                <argument>high-level-design-data-model.pdf</argument>
                <argument>${project.basedir}/src/main/resources</argument>
                <argument>${hld-data-model-document-reference}-dco-database-model-${hld-data-model-version}.pdf</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>