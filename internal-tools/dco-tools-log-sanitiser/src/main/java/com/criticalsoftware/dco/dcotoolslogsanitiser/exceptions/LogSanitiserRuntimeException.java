package com.criticalsoftware.dco.dcotoolslogsanitiser.exceptions;

import com.criticalsoftware.dco.dcotoolslogsanitiser.enums.LogSanitiserErrorCode;

/**
 * The class indicates that there was a HSMCli runtime problem with the LogSanitiserRuntimeException operations.
 */
public final class LogSanitiserRuntimeException extends RuntimeException {

    private final String errorCode;

    /**
     * Creates a new instance of class <code>RuntimeException</code> with the specified resource code and wrapping original cause of the
     * error.
     *
     * @param errorCode The resource identifier
     * @param message   The detail message
     * @param e         Original exception
     */
    private LogSanitiserRuntimeException(final LogSanitiserErrorCode errorCode, final String message, final Throwable e) {
        super(message, e);
        this.errorCode = errorCode.getErrorCode();
    }

    /**
     * Creates a new instance of this exception with {@link LogSanitiserErrorCode#FILE_READ_ERROR} error code.
     *
     * @param e Original exception
     *
     * @return instance of {@link LogSanitiserRuntimeException}
     */
    public static LogSanitiserRuntimeException newLogSanitisationReadError(final Throwable e) {
        return new LogSanitiserRuntimeException(LogSanitiserErrorCode.FILE_READ_ERROR, "Failed to load file", e);
    }

    /**
     * Creates a new instance of this exception with {@link LogSanitiserErrorCode#FILE_WRITE_ERROR} error code.
     *
     * @return instance of {@link LogSanitiserRuntimeException}
     */
    public static LogSanitiserRuntimeException newLogSanitisationWriteError(final Throwable e) {
        return new LogSanitiserRuntimeException(LogSanitiserErrorCode.FILE_WRITE_ERROR, "Failed to write file", e);
    }

    /**
     * Creates a new instance of this exception with {@link LogSanitiserErrorCode#PARSE_DATE_ERROR} error code.
     *
     * @return instance of {@link LogSanitiserRuntimeException}
     */
    public static LogSanitiserRuntimeException newLogSanitisationDateParseError(final Throwable e) {
        return new LogSanitiserRuntimeException(LogSanitiserErrorCode.PARSE_DATE_ERROR, "Failed to parse date", e);
    }

}
