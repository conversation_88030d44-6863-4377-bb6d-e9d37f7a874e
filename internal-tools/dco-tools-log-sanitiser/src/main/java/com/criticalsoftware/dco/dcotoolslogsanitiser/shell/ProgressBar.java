package com.criticalsoftware.dco.dcotoolslogsanitiser.shell;

import java.io.Console;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ProgressBar {

    private class ProgressBarInfo {

        long counter = 0;
        long maxCounter = 0;
        long percentage = 0;
        int lastIndex = 0;
        char[] bar = new char[BAR_LENGTH];

    }

    private static final String MOVE_UP_CHARACTER = "\u001B[%dA";
    private static final String MOVE_DOWN_CHARACTER = "\u001B[%dB";
    private static final String CLEAN_LINE_CHARACTER = "\u001B[2K";
    private static final int MAX_PERCENT = 100;
    private static final int BAR_LENGTH = 50;
    private static final char START_CHAR = '[';
    private static final char END_CHAR = ']';
    private static final char DONE_CHAR = '=';
    private static final char TODO_CHAR = '-';
    private static final char PERCENT_CHAR = '%';
    private static final String BAR_NAME = "File Name:";

    private final Map<String, ProgressBarInfo> bars;
    private final Console terminal;

    public ProgressBar() {
        terminal = System.console();
        bars = new ConcurrentHashMap<>();
        resetAll();
    }

    public synchronized void add(final String name, final long maxCounter) {
        bars.put(name, new ProgressBarInfo());
        start(name, maxCounter);
    }

    public void progress(final String name) {
        progressCounter(name, 1L);
    }

    public void progressCounter(final String name, final long progress) {
        final ProgressBarInfo progressBarInfo = bars.get(name);
        if (progressBarInfo.counter < progressBarInfo.maxCounter) {
            progressBarInfo.counter += progress;
            final int newPercentage = (int) (progressBarInfo.counter * MAX_PERCENT / progressBarInfo.maxCounter);
            if (newPercentage > progressBarInfo.percentage) {
                progressBarInfo.percentage = newPercentage;
                displayProgress();
            }
        }
    }

    public void finish(final String name) {
        final ProgressBarInfo progressBarInfo = bars.get(name);
        progressBarInfo.counter = progressBarInfo.maxCounter - 1;
        progress(name);
        if (bars.size() == 1) {
            bars.remove(name);
            terminal.writer().print(String.format(MOVE_DOWN_CHARACTER, 1));
            terminal.flush();
        }
    }

    public void finishAll() {
        bars.forEach((name, info) -> finish(name));
        displayProgress();
        terminal.writer().print(String.format(MOVE_DOWN_CHARACTER, bars.size()));
        terminal.flush();
        resetAll();
    }

    public void resetAll() {
        bars.clear();
    }

    public void reset(final String name) {
        final ProgressBarInfo progressBarInfo = bars.get(name);
        progressBarInfo.counter = 0;
        progressBarInfo.maxCounter = 0;
        progressBarInfo.percentage = 0;
        progressBarInfo.lastIndex = -1;
        for (int i = 0; i < BAR_LENGTH; ++i) {
            progressBarInfo.bar[i] = TODO_CHAR;
        }
    }

    private void start(final String name, final long maxCount) {
        reset(name);
        if (maxCount > 0) {
            final ProgressBarInfo progressBarInfo = bars.get(name);
            progressBarInfo.maxCounter = maxCount;
            displayProgress();
        }
    }

    public synchronized void displayProgress() {
        final StringBuilder stringBuilder = new StringBuilder();
        bars.forEach((name, progressBarInfo) -> {
            final int index = (int) (progressBarInfo.percentage * BAR_LENGTH / MAX_PERCENT) - 1;
            if (index >= 0 && index < MAX_PERCENT) {
                for (int j = progressBarInfo.lastIndex + 1; j <= index; j++) {
                    progressBarInfo.bar[j] = DONE_CHAR;
                }
            }
            progressBarInfo.lastIndex = index;
            stringBuilder.append(CLEAN_LINE_CHARACTER + START_CHAR).append(String.valueOf(progressBarInfo.bar)).append(END_CHAR)
                .append(" ").append(String.format("%3d", progressBarInfo.percentage)).append(PERCENT_CHAR)
                .append(String.format(" %" + (int) (Math.log10(progressBarInfo.maxCounter) + 1) + "d/%d lines", progressBarInfo.counter,
                    progressBarInfo.maxCounter)).append(String.format(" - %s %s", BAR_NAME, name)).append(System.lineSeparator());
        });
        terminal.writer().print(stringBuilder);
        terminal.writer().print(String.format(MOVE_UP_CHARACTER, bars.size()));
        terminal.flush();
    }

}